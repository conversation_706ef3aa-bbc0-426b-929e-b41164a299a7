{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-28 00:12:29"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-28 00:13:47"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-28 00:15:18"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-28 00:16:20"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-28 00:17:04"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-28 00:18:19"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-28 00:42:08"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-28 00:49:30"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-28 00:51:33"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-28 00:57:03"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-28 00:57:13"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-28 00:59:43"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-28 01:00:07"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-28 01:05:11"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-28 01:05:20"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-28 01:05:45"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-28 01:08:56"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-28 01:09:23"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-28 01:19:01"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-28 01:33:01"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-28 01:33:25"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-28 01:33:29"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-28 01:55:39"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-28 02:00:02"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-28 02:22:25"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-28 02:24:46"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-28 02:26:09"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-28 02:29:53"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-28 02:35:45"}
{"level":"error","message":"Failed to log to database: Cannot read properties of undefined (reading 'headers')","timestamp":"2025-01-28 02:35:49"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-28 02:40:37"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-28 20:44:52"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-28 20:52:34"}
