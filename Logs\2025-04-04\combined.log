{"level":"info","message":"{\"method\":\"POST\",\"api_url\":\"/auth/signin\",\"table_name\":\"Organization\",\"operation\":\"SIGNIN_ATTEMPT\",\"description\":\"Sign-in attempt failed: Invalid email or password\",\"old_value\":\"{\\\"email\\\":\\\"<EMAIL>\\\"}\",\"new_value\":null,\"browser\":\"Chrome 134.0.0\",\"ip_address\":\"::1\",\"user\":\"<EMAIL>\"}","timestamp":"2025-04-04 15:02:22"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 134.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-04-04 15:02:58"}
{"level":"info","message":{"api_url":"/organization/createOrganizationDetails","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create ","table_name":"organization details","user":"<EMAIL>"},"timestamp":"2025-04-04 15:03:31"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-04-04 15:03:31"}
{"level":"info","message":{"api_url":"/organization/createOrganizationDetails","browser":"Chrome 134.0.0","description":"New Site created","ip_address":"::1","method":"POST","new_value":"{\"organization_name\":\"test\",\"organization_address\":\"test\",\"timezone\":\"Asia/Karachi\"}","old_value":"null","operation":"CREATE","table_name":"Site","user":"<EMAIL>"},"timestamp":"2025-04-04 15:03:31"}
{"level":"info","message":{"api_url":"/organization/logout","browser":"Chrome 134.0.0","description":"User logged out successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"LOGOUT","table_name":"N/A","user":"<EMAIL>"},"timestamp":"2025-04-04 15:40:49"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 134.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-04-04 15:41:08"}
{"level":"info","message":{"api_url":"/organization/logout","browser":"Chrome 134.0.0","description":"User logged out successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"LOGOUT","table_name":"N/A","user":"<EMAIL>"},"timestamp":"2025-04-04 16:15:35"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 134.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-04-04 16:15:47"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/433","browser":"Chrome 134.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-04-04 16:56:24"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-04-04 16:56:25"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Other 0.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-04-04 17:35:13"}
{"level":"info","message":{"api_url":"/incident_report/createIncidentReportResponse","browser":"Other 0.0.0","description":"Incident report response created:\n          study_id: 1,\n          user_id: 576,\n          question_id: 45,\n          response_text: Testing email.,\n          description: Testing email ,\n          incident_severety: Moderate,\n          start_date: 01/10/2025,\n          start_time: 4:54:00 PM,\n          medical_issue: 1,\n          end_date: 2024-12-25,\n          end_time: 15:00","ip_address":"::1","method":"POST","new_value":"{\"study_id\":1,\"user_id\":576,\"question_id\":45,\"response_text\":\"Testing email.\",\"description\":\"Testing email \",\"incident_severety\":\"Moderate\",\"start_date\":\"01/10/2025\",\"start_time\":\"4:54:00 PM\",\"medical_issue\":1,\"end_date\":\"2024-12-25\",\"end_time\":\"15:00\"}","old_value":"null","operation":"SUBMIT","table_name":"Incident Report Response","user":"<EMAIL>"},"timestamp":"2025-04-04 17:35:35"}
{"level":"error","message":"Failed to log to database or Excel: Data too long for column 'description' at row 1","timestamp":"2025-04-04 17:35:37"}
{"level":"info","message":{"api_url":"/incident_report/createIncidentReportResponse","browser":"Other 0.0.0","description":"Incident report response created:\n          study_id: 1,\n          user_id: 563,\n          question_id: 45,\n          response_text: Testing email.,\n          description: Testing email ,\n          incident_severety: Moderate,\n          start_date: 01/10/2025,\n          start_time: 4:54:00 PM,\n          medical_issue: 1,\n          end_date: 2024-12-25,\n          end_time: 15:00","ip_address":"::1","method":"POST","new_value":"{\"study_id\":1,\"user_id\":563,\"question_id\":45,\"response_text\":\"Testing email.\",\"description\":\"Testing email \",\"incident_severety\":\"Moderate\",\"start_date\":\"01/10/2025\",\"start_time\":\"4:54:00 PM\",\"medical_issue\":1,\"end_date\":\"2024-12-25\",\"end_time\":\"15:00\"}","old_value":"null","operation":"SUBMIT","table_name":"Incident Report Response","user":"<EMAIL>"},"timestamp":"2025-04-04 17:47:28"}
{"level":"error","message":"Failed to log to database or Excel: Data too long for column 'description' at row 1","timestamp":"2025-04-04 17:47:29"}
{"level":"info","message":{"api_url":"/incident_report/createIncidentReportResponse","browser":"Other 0.0.0","description":"Incident report response created:\n          study_id: 1,\n          user_id: 563,\n          question_id: 45,\n          response_text: Testing email.,\n          description: Testing email ,\n          incident_severety: Moderate,\n          start_date: 01/10/2025,\n          start_time: 4:54:00 PM,\n          medical_issue: 1,\n          end_date: 2024-12-25,\n          end_time: 15:00","ip_address":"::1","method":"POST","new_value":"{\"study_id\":1,\"user_id\":563,\"question_id\":45,\"response_text\":\"Testing email.\",\"description\":\"Testing email \",\"incident_severety\":\"Moderate\",\"start_date\":\"01/10/2025\",\"start_time\":\"4:54:00 PM\",\"medical_issue\":1,\"end_date\":\"2024-12-25\",\"end_time\":\"15:00\"}","old_value":"null","operation":"SUBMIT","table_name":"Incident Report Response","user":"<EMAIL>"},"timestamp":"2025-04-04 17:53:31"}
{"level":"error","message":"Failed to log to database or Excel: Data too long for column 'description' at row 1","timestamp":"2025-04-04 17:53:31"}
{"level":"info","message":{"api_url":"/incident_report/createIncidentReportResponse","browser":"Other 0.0.0","description":"Incident report response created:\n          study_id: 1,\n          user_id: 563,\n          question_id: 45,\n          response_text: Testing email.,\n          description: Testing email ,\n          incident_severety: Moderate,\n          start_date: 01/10/2025,\n          start_time: 4:54:00 PM,\n          medical_issue: 1,\n          end_date: 2024-12-25,\n          end_time: 15:00","ip_address":"::1","method":"POST","new_value":"{\"study_id\":1,\"user_id\":563,\"question_id\":45,\"response_text\":\"Testing email.\",\"description\":\"Testing email \",\"incident_severety\":\"Moderate\",\"start_date\":\"01/10/2025\",\"start_time\":\"4:54:00 PM\",\"medical_issue\":1,\"end_date\":\"2024-12-25\",\"end_time\":\"15:00\"}","old_value":"null","operation":"SUBMIT","table_name":"Incident Report Response","user":"<EMAIL>"},"timestamp":"2025-04-04 18:00:36"}
{"level":"error","message":"Failed to log to database or Excel: Data too long for column 'description' at row 1","timestamp":"2025-04-04 18:00:37"}
{"level":"info","message":{"api_url":"/incident_report/createIncidentReportResponse","browser":"Other 0.0.0","description":"Incident report response created:\n          study_id: 1,\n          user_id: 563,\n          question_id: 45,\n          response_text: Testing email.,\n          description: Testing email ,\n          incident_severety: Moderate,\n          start_date: 01/10/2025,\n          start_time: 4:54:00 PM,\n          medical_issue: 1,\n          end_date: 2024-12-25,\n          end_time: 15:00","ip_address":"::1","method":"POST","new_value":"{\"study_id\":1,\"user_id\":563,\"question_id\":45,\"response_text\":\"Testing email.\",\"description\":\"Testing email \",\"incident_severety\":\"Moderate\",\"start_date\":\"01/10/2025\",\"start_time\":\"4:54:00 PM\",\"medical_issue\":1,\"end_date\":\"2024-12-25\",\"end_time\":\"15:00\"}","old_value":"null","operation":"SUBMIT","table_name":"Incident Report Response","user":"<EMAIL>"},"timestamp":"2025-04-04 18:04:30"}
{"level":"error","message":"Failed to log to database or Excel: Data too long for column 'description' at row 1","timestamp":"2025-04-04 18:04:31"}
{"level":"info","message":{"api_url":"/incident_report/createIncidentReportResponse","browser":"Other 0.0.0","description":"Incident report response created:\n          study_id: 1,\n          user_id: 563,\n          question_id: 45,\n          response_text: Testing email.,\n          description: Testing email ,\n          incident_severety: Moderate,\n          start_date: 01/10/2025,\n          start_time: 4:54:00 PM,\n          medical_issue: 1,\n          end_date: 2024-12-25,\n          end_time: 15:00","ip_address":"::1","method":"POST","new_value":"{\"study_id\":1,\"user_id\":563,\"question_id\":45,\"response_text\":\"Testing email.\",\"description\":\"Testing email \",\"incident_severety\":\"Moderate\",\"start_date\":\"01/10/2025\",\"start_time\":\"4:54:00 PM\",\"medical_issue\":1,\"end_date\":\"2024-12-25\",\"end_time\":\"15:00\"}","old_value":"null","operation":"SUBMIT","table_name":"Incident Report Response","user":"<EMAIL>"},"timestamp":"2025-04-04 18:07:17"}
{"level":"error","message":"Failed to log to database or Excel: Data too long for column 'description' at row 1","timestamp":"2025-04-04 18:07:17"}
{"level":"info","message":{"api_url":"/incident_report/aesi-responses","browser":"Chrome 134.0.0","description":"No Reason Provided","ip_address":"::1","method":"POST","new_value":"{\"ticket_id\":\"AE-0OWWWP4\",\"formattedResponses\":[{\"ticket_id\":\"AE-0OWWWP4\",\"question_id\":1,\"option_id\":1,\"description\":\"\"},{\"ticket_id\":\"AE-0OWWWP4\",\"question_id\":2,\"option_id\":3,\"description\":\"\"},{\"ticket_id\":\"AE-0OWWWP4\",\"question_id\":3,\"option_id\":\"\",\"description\":\"testing\"},{\"ticket_id\":\"AE-0OWWWP4\",\"question_id\":4,\"option_id\":5,\"description\":\"\"},{\"ticket_id\":\"AE-0OWWWP4\",\"question_id\":5,\"option_id\":\"\",\"description\":\"testing\"}]}","old_value":"null","operation":"CREATE","table_name":"AESI Reponses","user":"<EMAIL>"},"timestamp":"2025-04-04 23:17:47"}
{"level":"info","message":{"api_url":"/incident_report/aesi-responses","browser":"Chrome 134.0.0","description":"No Reason Provided","ip_address":"::1","method":"POST","new_value":"{\"ticket_id\":\"AE-V1UTRUX\",\"formattedResponses\":[{\"ticket_id\":\"AE-V1UTRUX\",\"question_id\":1,\"option_id\":1,\"description\":\"\"},{\"ticket_id\":\"AE-V1UTRUX\",\"question_id\":2,\"option_id\":4,\"description\":\"\"},{\"ticket_id\":\"AE-V1UTRUX\",\"question_id\":3,\"option_id\":\"\",\"description\":\"testing\"},{\"ticket_id\":\"AE-V1UTRUX\",\"question_id\":4,\"option_id\":5,\"description\":\"\"},{\"ticket_id\":\"AE-V1UTRUX\",\"question_id\":5,\"option_id\":\"\",\"description\":\"testing\"}]}","old_value":"null","operation":"CREATE","table_name":"AESI Reponses","user":"<EMAIL>"},"timestamp":"2025-04-04 23:21:50"}
{"level":"info","message":{"api_url":"/incident_report/aesi-responses","browser":"Chrome 134.0.0","description":"No Reason Provided","ip_address":"::1","method":"POST","new_value":"{\"ticket_id\":\"AE-V1UTRUX\",\"formattedResponses\":[{\"ticket_id\":\"AE-V1UTRUX\",\"question_id\":1,\"option_id\":1,\"description\":\"\"},{\"ticket_id\":\"AE-V1UTRUX\",\"question_id\":2,\"option_id\":4,\"description\":\"\"},{\"ticket_id\":\"AE-V1UTRUX\",\"question_id\":3,\"option_id\":\"\",\"description\":\"testing\"},{\"ticket_id\":\"AE-V1UTRUX\",\"question_id\":4,\"option_id\":5,\"description\":\"\"},{\"ticket_id\":\"AE-V1UTRUX\",\"question_id\":5,\"option_id\":\"\",\"description\":\"testing\"}]}","old_value":"null","operation":"CREATE","table_name":"AESI Reponses","user":"<EMAIL>"},"timestamp":"2025-04-04 23:22:38"}
{"level":"info","message":{"api_url":"/incident_report/aesi-responses","browser":"Chrome 134.0.0","description":"No Reason Provided","ip_address":"::1","method":"POST","new_value":"{\"ticket_id\":\"AE-V1UTRUX\",\"formattedResponses\":[{\"ticket_id\":\"AE-V1UTRUX\",\"question_id\":1,\"option_id\":1,\"description\":\"\"},{\"ticket_id\":\"AE-V1UTRUX\",\"question_id\":2,\"option_id\":4,\"description\":\"\"},{\"ticket_id\":\"AE-V1UTRUX\",\"question_id\":3,\"option_id\":\"\",\"description\":\"testing\"},{\"ticket_id\":\"AE-V1UTRUX\",\"question_id\":4,\"option_id\":5,\"description\":\"\"},{\"ticket_id\":\"AE-V1UTRUX\",\"question_id\":5,\"option_id\":\"\",\"description\":\"testing\"}]}","old_value":"null","operation":"CREATE","table_name":"AESI Reponses","user":"<EMAIL>"},"timestamp":"2025-04-04 23:23:16"}
{"level":"info","message":{"api_url":"/incident_report/aesi-responses","browser":"Chrome 134.0.0","description":"No Reason Provided","ip_address":"::1","method":"POST","new_value":"{\"ticket_id\":\"AE-V1UTRUX\",\"formattedResponses\":[{\"ticket_id\":\"AE-V1UTRUX\",\"question_id\":1,\"option_id\":1,\"description\":\"\"},{\"ticket_id\":\"AE-V1UTRUX\",\"question_id\":2,\"option_id\":4,\"description\":\"\"},{\"ticket_id\":\"AE-V1UTRUX\",\"question_id\":3,\"option_id\":\"\",\"description\":\"testing\"},{\"ticket_id\":\"AE-V1UTRUX\",\"question_id\":4,\"option_id\":5,\"description\":\"\"},{\"ticket_id\":\"AE-V1UTRUX\",\"question_id\":5,\"option_id\":\"\",\"description\":\"testing\"}]}","old_value":"null","operation":"CREATE","table_name":"AESI Reponses","user":"<EMAIL>"},"timestamp":"2025-04-04 23:29:35"}
{"level":"info","message":{"api_url":"/incident_report/aesi-responses","browser":"Chrome 134.0.0","description":"No Reason Provided","ip_address":"::1","method":"POST","new_value":"{\"ticket_id\":\"AE-0OWWWP4\",\"formattedResponses\":[{\"ticket_id\":\"AE-0OWWWP4\",\"question_id\":1,\"option_id\":1,\"description\":\"\"},{\"ticket_id\":\"AE-0OWWWP4\",\"question_id\":2,\"option_id\":4,\"description\":\"\"},{\"ticket_id\":\"AE-0OWWWP4\",\"question_id\":3,\"option_id\":\"\",\"description\":\"testing\"},{\"ticket_id\":\"AE-0OWWWP4\",\"question_id\":4,\"option_id\":5,\"description\":\"\"},{\"ticket_id\":\"AE-0OWWWP4\",\"question_id\":5,\"option_id\":\"\",\"description\":\"testing\"}]}","old_value":"null","operation":"CREATE","table_name":"AESI Reponses","user":"<EMAIL>"},"timestamp":"2025-04-04 23:50:00"}
{"level":"info","message":{"api_url":"/incident_report/aesi-responses","browser":"Chrome 134.0.0","description":"No Reason Provided","ip_address":"::1","method":"POST","new_value":"{\"ticket_id\":\"AE-0OWWWP4\",\"formattedResponses\":[{\"ticket_id\":\"AE-0OWWWP4\",\"question_id\":1,\"option_id\":1,\"description\":\"\"},{\"ticket_id\":\"AE-0OWWWP4\",\"question_id\":2,\"option_id\":4,\"description\":\"\"},{\"ticket_id\":\"AE-0OWWWP4\",\"question_id\":3,\"option_id\":\"\",\"description\":\"testing\"},{\"ticket_id\":\"AE-0OWWWP4\",\"question_id\":4,\"option_id\":5,\"description\":\"\"},{\"ticket_id\":\"AE-0OWWWP4\",\"question_id\":5,\"option_id\":\"\",\"description\":\"testing\"}]}","old_value":"null","operation":"CREATE","table_name":"AESI Reponses","user":"<EMAIL>"},"timestamp":"2025-04-04 23:56:02"}
{"level":"info","message":{"api_url":"/incident_report/aesi-responses","browser":"Chrome 134.0.0","description":"No Reason Provided","ip_address":"::1","method":"POST","new_value":"{\"ticket_id\":\"AE-0OWWWP4\",\"formattedResponses\":[{\"ticket_id\":\"AE-0OWWWP4\",\"question_id\":1,\"option_id\":1,\"description\":\"\"},{\"ticket_id\":\"AE-0OWWWP4\",\"question_id\":2,\"option_id\":4,\"description\":\"\"},{\"ticket_id\":\"AE-0OWWWP4\",\"question_id\":3,\"option_id\":\"\",\"description\":\"testing\"},{\"ticket_id\":\"AE-0OWWWP4\",\"question_id\":4,\"option_id\":5,\"description\":\"\"},{\"ticket_id\":\"AE-0OWWWP4\",\"question_id\":5,\"option_id\":\"\",\"description\":\"testing\"}]}","old_value":"null","operation":"CREATE","table_name":"AESI Reponses","user":"<EMAIL>"},"timestamp":"2025-04-04 23:58:19"}
