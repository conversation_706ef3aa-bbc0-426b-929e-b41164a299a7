{"level":"error","message":"Failed to log to database or Excel: userData is not defined","timestamp":"2025-01-31 08:09:23"}
{"level":"error","message":"Failed to log to database or Excel: userData is not defined","timestamp":"2025-01-31 08:09:44"}
{"level":"error","message":"Failed to log to database or Excel: userData is not defined","timestamp":"2025-01-31 08:09:47"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 132.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-01-31 08:11:44"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1497","browser":"Chrome 132.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-01-31 08:12:02"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-31 08:12:03"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1497","browser":"Chrome 132.0.0","description":"Schedule completed with ID: 1497","ip_address":"::1","method":"PUT","new_value":"{\"schedule_date\":\"2025-01-23\",\"schedule_time\":\"09:00\",\"status\":\"Completed\",\"note\":\"Auto-created schedule\",\"user_id\":437}","old_value":"{\"schedule_date\":\"2025-01-16T19:00:00.000Z\",\"schedule_time\":\"09:00\",\"status\":\"Completed\",\"note\":\"Auto-created schedule\",\"user_id\":437}","operation":"UPDATE","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-01-31 08:12:05"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1497","browser":"Chrome 132.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-01-31 08:16:01"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-01-31 08:16:02"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1497","browser":"Chrome 132.0.0","description":"Schedule completed with ID: 1497","ip_address":"::1","method":"PUT","new_value":"{\"schedule_date\":\"2025-01-19\",\"schedule_time\":\"09:00\",\"status\":\"Completed\",\"note\":\"Auto-created schedule\",\"user_id\":437}","old_value":"{\"schedule_date\":\"2025-01-22T19:00:00.000Z\",\"schedule_time\":\"09:00\",\"status\":\"Completed\",\"note\":\"Auto-created schedule\",\"user_id\":437}","operation":"UPDATE","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-01-31 08:16:04"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 132.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-01-31 08:16:12"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 132.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-01-31 08:19:37"}
{"level":"error","message":"Failed to log to database or Excel: EBUSY: resource busy or locked, open 'C:\\Users\\<USER>\\Downloads\\test_live_environment-master\\Logs\\2025-01-31\\audit_logs.xlsx'","timestamp":"2025-01-31 08:19:38"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1497","browser":"Chrome 132.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-01-31 08:19:53"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-01-31 08:19:53"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1497","browser":"Chrome 132.0.0","description":"Schedule completed with ID: 1497","ip_address":"::1","method":"PUT","new_value":"{\"schedule_date\":\"2025-01-15\",\"schedule_time\":\"09:00\",\"status\":\"Completed\",\"note\":\"Auto-created schedule\",\"user_id\":437}","old_value":"{\"schedule_date\":\"2025-01-18T19:00:00.000Z\",\"schedule_time\":\"09:00\",\"status\":\"Completed\",\"note\":\"Auto-created schedule\",\"user_id\":437}","operation":"UPDATE","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-01-31 08:19:55"}
{"level":"error","message":"Failed to log to database or Excel: EBUSY: resource busy or locked, open 'C:\\Users\\<USER>\\Downloads\\test_live_environment-master\\Logs\\2025-01-31\\audit_logs.xlsx'","timestamp":"2025-01-31 08:19:56"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 132.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-01-31 08:20:15"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1497","browser":"Chrome 132.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-01-31 08:20:51"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-01-31 08:20:51"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1497","browser":"Chrome 132.0.0","description":"Schedule completed with ID: 1497","ip_address":"::1","method":"PUT","new_value":"{\"schedule_date\":\"2025-01-16\",\"schedule_time\":\"09:00\",\"status\":\"Completed\",\"note\":\"Auto-created schedule\",\"user_id\":437}","old_value":"{\"schedule_date\":\"2025-01-14T19:00:00.000Z\",\"schedule_time\":\"09:00\",\"status\":\"Completed\",\"note\":\"Auto-created schedule\",\"user_id\":437}","operation":"UPDATE","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-01-31 08:20:53"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1497","browser":"Chrome 132.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-01-31 08:24:06"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-01-31 08:24:07"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1497","browser":"Chrome 132.0.0","description":"Schedule completed with ID: 1497","ip_address":"::1","method":"PUT","new_value":"{\"schedule_date\":\"2025-01-13\",\"schedule_time\":\"09:00\",\"status\":\"Completed\",\"note\":\"Auto-created schedule\",\"user_id\":437}","old_value":"{\"schedule_date\":\"2025-01-15T19:00:00.000Z\",\"schedule_time\":\"09:00\",\"status\":\"Completed\",\"note\":\"Auto-created schedule\",\"user_id\":437}","operation":"UPDATE","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-01-31 08:24:09"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1514","browser":"Chrome 132.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-01-31 08:25:26"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-01-31 08:25:27"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1514","browser":"Chrome 132.0.0","description":"Schedule completed with ID: 1514","ip_address":"::1","method":"PUT","new_value":"{\"schedule_date\":\"2025-04-11\",\"schedule_time\":\"09:00\",\"status\":\"Completed\",\"note\":\"Auto-scheduled for Safety Follow-up Day 64\",\"user_id\":438}","old_value":"{\"schedule_date\":\"2025-04-10T19:00:00.000Z\",\"schedule_time\":\"09:00\",\"status\":\"Pending\",\"note\":\"Auto-scheduled for Safety Follow-up Day 64\",\"user_id\":438}","operation":"UPDATE","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-01-31 08:25:28"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 132.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-01-31 08:26:15"}
{"level":"info","message":{"api_url":"/organization/logout","browser":"Chrome 132.0.0","description":"User logged out successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"LOGOUT","table_name":"N/A","user":"<EMAIL>"},"timestamp":"2025-01-31 12:32:18"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 132.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-01-31 12:32:27"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/313","browser":"Chrome 132.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-01-31 12:32:47"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-01-31 12:32:47"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/313","browser":"Chrome 132.0.0","description":"Welcome email sent to test tet (<EMAIL>)","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":416,\"email\":\"<EMAIL>\",\"subject\":\"Welcome to ResearchHero!\"}","old_value":"null","operation":"EMAIL_SENT","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-01-31 12:32:50"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/313","browser":"Chrome 132.0.0","description":"Registration status updated to Accepted for ID 313","ip_address":"::1","method":"PUT","new_value":"{\"id\":\"313\",\"status\":\"Accepted\",\"reason\":\"Accepted By Super Admin \",\"user_id\":416}","old_value":"[{\"account_status_id\":313,\"user_id\":416,\"account_status\":\"Pending\",\"reason\":\"Initial registration\",\"first_time\":\"1\",\"updated_at\":\"2024-12-20T08:10:24.000Z\"}]","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-01-31 12:32:51"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/312","browser":"Chrome 132.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-01-31 12:33:58"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-01-31 12:33:59"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/312","browser":"Chrome 132.0.0","description":"Welcome email sent to tlfb test (<EMAIL>)","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":415,\"email\":\"<EMAIL>\",\"subject\":\"Welcome to ResearchHero!\"}","old_value":"null","operation":"EMAIL_SENT","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-01-31 12:34:02"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/312","browser":"Chrome 132.0.0","description":"Error updating registration status: study_enrolled_id is not defined","ip_address":"::1","method":"PUT","new_value":"null","old_value":"{\"id\":\"312\",\"status\":\"Accepted\",\"reason\":\"Accepted By Super Admin \",\"user_id\":415}","operation":"UPDATE_ERROR","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-01-31 12:34:03"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/311","browser":"Chrome 132.0.0","ip_address":"::ffff:127.0.0.1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-01-31 12:35:18"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-01-31 12:35:18"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/311","browser":"Chrome 132.0.0","description":"Welcome email sent to testsub jest (<EMAIL>)","ip_address":"::ffff:127.0.0.1","method":"PUT","new_value":"{\"user_id\":414,\"email\":\"<EMAIL>\",\"subject\":\"Welcome to ResearchHero!\"}","old_value":"null","operation":"EMAIL_SENT","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-01-31 12:35:21"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/311","browser":"Chrome 132.0.0","description":"Registration status updated to Accepted for ID 311","ip_address":"::ffff:127.0.0.1","method":"PUT","new_value":"{\"id\":\"311\",\"status\":\"Accepted\",\"reason\":\"Accepted By Super Admin \",\"user_id\":414}","old_value":"[{\"account_status_id\":311,\"user_id\":414,\"account_status\":\"Pending\",\"reason\":\"Initial registration\",\"first_time\":\"1\",\"updated_at\":\"2024-12-19T07:42:02.000Z\"}]","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-01-31 12:35:24"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/296","browser":"Chrome 132.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-01-31 12:37:09"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-01-31 12:37:09"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/296","browser":"Chrome 132.0.0","description":"Welcome email sent to TLFB TEST_2 (<EMAIL>)","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":399,\"email\":\"<EMAIL>\",\"subject\":\"Welcome to ResearchHero!\"}","old_value":"null","operation":"EMAIL_SENT","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-01-31 12:37:12"}
{"level":"info","message":{"api_url":"/schedule/createSchedule","browser":"Other 0.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-01-31 12:37:14"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-01-31 12:37:14"}
{"level":"info","message":{"api_url":"/schedule/createSchedule","browser":"Other 0.0.0","description":"Schedule created successfully for user 399","ip_address":"::1","method":"POST","new_value":"{}","old_value":"null","operation":"CREATE","table_name":"Schedule","user":"<EMAIL>"},"timestamp":"2025-01-31 12:37:19"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/296","browser":"Chrome 132.0.0","description":"Registration status updated to Accepted for ID 296","ip_address":"::1","method":"PUT","new_value":"{\"id\":\"296\",\"status\":\"Accepted\",\"reason\":\"Accepted By Super Admin \",\"user_id\":399}","old_value":"[{\"account_status_id\":296,\"user_id\":399,\"account_status\":\"Pending\",\"reason\":\"Initial registration\",\"first_time\":\"1\",\"updated_at\":\"2024-12-11T18:24:59.000Z\"}]","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-01-31 12:37:21"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/288","browser":"Chrome 132.0.0","ip_address":"::ffff:127.0.0.1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-01-31 12:39:58"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-01-31 12:39:59"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/288","browser":"Chrome 132.0.0","description":"Error updating registration status: Cannot destructure property 'role_id' of 'user' as it is null.","ip_address":"::ffff:127.0.0.1","method":"PUT","new_value":"null","old_value":"{\"id\":\"288\",\"status\":\"Accepted\",\"reason\":\"Accepted By Super Admin \",\"user_id\":391}","operation":"UPDATE_ERROR","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-01-31 12:40:00"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/287","browser":"Chrome 132.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-01-31 12:42:08"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-01-31 12:42:08"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/287","browser":"Chrome 132.0.0","description":"Welcome email sent to Mickey Mouse (<EMAIL>)","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":390,\"email\":\"<EMAIL>\",\"subject\":\"Welcome to ResearchHero!\"}","old_value":"null","operation":"EMAIL_SENT","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-01-31 12:42:12"}
{"level":"info","message":{"api_url":"/schedule/createSchedule","browser":"Other 0.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-01-31 12:42:13"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-01-31 12:42:14"}
{"level":"info","message":{"api_url":"/schedule/createSchedule","browser":"Other 0.0.0","description":"Schedule created successfully for user 390","ip_address":"::1","method":"POST","new_value":"{}","old_value":"null","operation":"CREATE","table_name":"Schedule","user":"<EMAIL>"},"timestamp":"2025-01-31 12:42:18"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/287","browser":"Chrome 132.0.0","description":"Registration status updated to Accepted for ID 287","ip_address":"::1","method":"PUT","new_value":"{\"id\":\"287\",\"status\":\"Accepted\",\"reason\":\"Accepted By Super Admin \",\"user_id\":390}","old_value":"[{\"account_status_id\":287,\"user_id\":390,\"account_status\":\"Pending\",\"reason\":\"Initial registration\",\"first_time\":\"1\",\"updated_at\":\"2024-12-06T20:55:38.000Z\"}]","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-01-31 12:42:21"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/286","browser":"Chrome 132.0.0","ip_address":"::ffff:127.0.0.1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-01-31 12:43:57"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-01-31 12:43:58"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/286","browser":"Chrome 132.0.0","description":"Welcome email sent to bob lee (<EMAIL>)","ip_address":"::ffff:127.0.0.1","method":"PUT","new_value":"{\"user_id\":389,\"email\":\"<EMAIL>\",\"subject\":\"Welcome to ResearchHero!\"}","old_value":"null","operation":"EMAIL_SENT","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-01-31 12:44:01"}
{"level":"info","message":{"api_url":"/schedule/createSchedule","browser":"Other 0.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-01-31 12:44:02"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-01-31 12:44:03"}
{"level":"info","message":{"api_url":"/schedule/createSchedule","browser":"Other 0.0.0","description":"Schedule created successfully for user 389","ip_address":"::1","method":"POST","new_value":"{}","old_value":"null","operation":"CREATE","table_name":"Schedule","user":"<EMAIL>"},"timestamp":"2025-01-31 12:44:09"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/286","browser":"Chrome 132.0.0","description":"Registration status updated to Accepted for ID 286","ip_address":"::ffff:127.0.0.1","method":"PUT","new_value":"{\"id\":\"286\",\"status\":\"Accepted\",\"reason\":\"Accepted By Super Admin \",\"user_id\":389}","old_value":"[{\"account_status_id\":286,\"user_id\":389,\"account_status\":\"Pending\",\"reason\":\"Initial registration\",\"first_time\":\"1\",\"updated_at\":\"2024-12-06T15:38:18.000Z\"}]","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-01-31 12:44:11"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/276","browser":"Chrome 132.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-01-31 12:47:22"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-01-31 12:47:22"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/276","browser":"Chrome 132.0.0","description":"Welcome email sent to boss mafia (<EMAIL>)","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":379,\"email\":\"<EMAIL>\",\"subject\":\"Welcome to ResearchHero!\"}","old_value":"null","operation":"EMAIL_SENT","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-01-31 12:47:25"}
{"level":"info","message":{"api_url":"/schedule/createSchedule","browser":"Other 0.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-01-31 12:47:27"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-01-31 12:47:27"}
{"level":"info","message":{"api_url":"/schedule/createSchedule","browser":"Other 0.0.0","description":"Schedule created successfully for user 379","ip_address":"::1","method":"POST","new_value":"{}","old_value":"null","operation":"CREATE","table_name":"Schedule","user":"<EMAIL>"},"timestamp":"2025-01-31 12:47:34"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/276","browser":"Chrome 132.0.0","description":"Registration status updated to Accepted for ID 276","ip_address":"::1","method":"PUT","new_value":"{\"id\":\"276\",\"status\":\"Accepted\",\"reason\":\"Accepted By Super Admin \",\"user_id\":379}","old_value":"[{\"account_status_id\":276,\"user_id\":379,\"account_status\":\"Pending\",\"reason\":\"Initial registration\",\"first_time\":\"1\",\"updated_at\":\"2024-11-28T07:36:02.000Z\"}]","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-01-31 12:47:37"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/273","browser":"Chrome 132.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-01-31 12:48:45"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-01-31 12:48:45"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/273","browser":"Chrome 132.0.0","description":"Welcome email sent to yoboi haaland (<EMAIL>)","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":376,\"email\":\"<EMAIL>\",\"subject\":\"Welcome to ResearchHero!\"}","old_value":"null","operation":"EMAIL_SENT","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-01-31 12:48:47"}
{"level":"info","message":{"api_url":"/schedule/createSchedule","browser":"Other 0.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-01-31 12:48:49"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-01-31 12:48:49"}
{"level":"info","message":{"api_url":"/schedule/createSchedule","browser":"Other 0.0.0","description":"Schedule created successfully for user 376","ip_address":"::1","method":"POST","new_value":"{}","old_value":"null","operation":"CREATE","table_name":"Schedule","user":"<EMAIL>"},"timestamp":"2025-01-31 12:48:55"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/273","browser":"Chrome 132.0.0","description":"Registration status updated to Accepted for ID 273","ip_address":"::1","method":"PUT","new_value":"{\"id\":\"273\",\"status\":\"Accepted\",\"reason\":\"Accepted By Super Admin \",\"user_id\":376}","old_value":"[{\"account_status_id\":273,\"user_id\":376,\"account_status\":\"Pending\",\"reason\":\"Initial registration\",\"first_time\":\"1\",\"updated_at\":\"2024-11-25T19:38:19.000Z\"}]","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-01-31 12:48:58"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/270","browser":"Chrome 132.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-01-31 12:51:08"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-01-31 12:51:08"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/270","browser":"Chrome 132.0.0","description":"Welcome email sent to SAM Test (<EMAIL>)","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":373,\"email\":\"<EMAIL>\",\"subject\":\"Welcome to ResearchHero!\"}","old_value":"null","operation":"EMAIL_SENT","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-01-31 12:51:11"}
{"level":"info","message":{"api_url":"/schedule/createSchedule","browser":"Other 0.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-01-31 12:51:12"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-01-31 12:51:12"}
{"level":"info","message":{"api_url":"/schedule/createSchedule","browser":"Other 0.0.0","description":"Schedule created successfully for user 373","ip_address":"::1","method":"POST","new_value":"{}","old_value":"null","operation":"CREATE","table_name":"Schedule","user":"<EMAIL>"},"timestamp":"2025-01-31 12:51:20"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/270","browser":"Chrome 132.0.0","description":"Registration status updated to Accepted for ID 270","ip_address":"::1","method":"PUT","new_value":"{\"id\":\"270\",\"status\":\"Accepted\",\"reason\":\"Accepted By Super Admin \",\"user_id\":373}","old_value":"[{\"account_status_id\":270,\"user_id\":373,\"account_status\":\"Pending\",\"reason\":\"Initial registration\",\"first_time\":\"1\",\"updated_at\":\"2024-11-25T17:36:13.000Z\"}]","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-01-31 12:51:23"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/270","browser":"Chrome 132.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-01-31 12:53:16"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-01-31 12:53:16"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/270","browser":"Chrome 132.0.0","description":"Registration status updated to Blocked for ID 270","ip_address":"::1","method":"PUT","new_value":"{\"id\":\"270\",\"status\":\"Blocked\",\"reason\":\"Accepted By Super Admin \",\"user_id\":373}","old_value":"[{\"account_status_id\":270,\"user_id\":373,\"account_status\":\"Accepted\",\"reason\":\"Accepted By Super Admin \",\"first_time\":\"0\",\"updated_at\":\"2024-11-25T17:36:13.000Z\"}]","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-01-31 12:53:17"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/270","browser":"Chrome 132.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-01-31 12:53:30"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-01-31 12:53:31"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/270","browser":"Chrome 132.0.0","description":"Welcome email sent to SAM Test (<EMAIL>)","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":373,\"email\":\"<EMAIL>\",\"subject\":\"Welcome to ResearchHero!\"}","old_value":"null","operation":"EMAIL_SENT","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-01-31 12:53:33"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/270","browser":"Chrome 132.0.0","description":"Registration status updated to Accepted for ID 270","ip_address":"::1","method":"PUT","new_value":"{\"id\":\"270\",\"status\":\"Accepted\",\"reason\":\"Accepted By Super Admin \",\"user_id\":373}","old_value":"[{\"account_status_id\":270,\"user_id\":373,\"account_status\":\"Blocked\",\"reason\":\"Accepted By Super Admin \",\"first_time\":\"0\",\"updated_at\":\"2024-11-25T17:36:13.000Z\"}]","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-01-31 12:53:34"}
{"level":"info","message":{"api_url":"/organization/deleteOrganization/373","browser":"Chrome 132.0.0","ip_address":"::1","method":"DELETE","new_value":"null","old_value":"null","operation":"delete","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-01-31 12:54:36"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-01-31 12:54:36"}
{"level":"info","message":{"api_url":"/organization/deleteOrganization/373","browser":"Chrome 132.0.0","ip_address":"::1","method":"DELETE","new_value":"null","old_value":"null","operation":"delete","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-01-31 12:54:45"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-01-31 12:54:45"}
{"level":"info","message":{"api_url":"/organization/deleteOrganization/373","browser":"Chrome 132.0.0","ip_address":"::1","method":"DELETE","new_value":"null","old_value":"null","operation":"delete","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-01-31 12:55:50"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-01-31 12:55:51"}
{"level":"info","message":{"api_url":"/organization/deleteOrganization/373","browser":"Chrome 132.0.0","ip_address":"::1","method":"DELETE","new_value":"null","old_value":"null","operation":"delete","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-01-31 12:55:51"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-01-31 12:55:51"}
{"level":"info","message":{"api_url":"/organization/deleteOrganization/373","browser":"Chrome 132.0.0","ip_address":"::1","method":"DELETE","new_value":"null","old_value":"null","operation":"delete","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-01-31 12:55:51"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-01-31 12:55:51"}
{"level":"info","message":{"api_url":"/organization/deleteOrganization/438","browser":"Chrome 132.0.0","ip_address":"::1","method":"DELETE","new_value":"null","old_value":"null","operation":"delete","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-01-31 12:56:26"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-01-31 12:56:26"}
