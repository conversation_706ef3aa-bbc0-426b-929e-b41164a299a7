{"openapi": "3.0.1", "info": {"title": "Resources and endpoints", "version": "1"}, "paths": {"/api/v1/documents/{id}/": {"get": {"summary": "Get Document", "description": "Returns a document and all associated document data. Supply the unique document ID from either a document creation request or Document page URL.", "tags": ["Document"], "security": [{"api_key": []}], "parameters": [{"name": "id", "in": "path", "schema": {"type": "string", "format": "uuid", "description": "Unique identifier for a document."}, "required": true}], "responses": {"200": {"description": "successful", "content": {"application/json": {"example": {"id": "a643adb2-a615-4356-ac05-de3a458bff7b", "archived": false, "embedded_edit_url": "https://www.signwell.com/edit/document/2b2fdbe7-fc73-44ce-b7b6-2aac34c507c2/", "embedded_preview_url": null, "name": "deport-galaxy/illum.jpeg", "requester_email_address": "<EMAIL>", "status": "Created", "test_mode": false, "created_at": "2024-09-03T12:29:13Z", "updated_at": "2024-09-03T12:29:13Z", "error_message": null, "decline_message": null, "allow_decline": true, "allow_reassign": true, "api_application_id": null, "custom_requester_email": "<EMAIL>", "custom_requester_name": "<PERSON><PERSON><PERSON>", "decline_redirect_url": "http://hintz.example/felix_kuhlman", "embedded_signing": false, "expires_in": 10, "metadata": {"qui": "consequatur", "asperiores": "possimus"}, "redirect_url": "http://macejkovic.example/audrea", "reminders": true, "apply_signing_order": false, "message": "<p>Hey there,</p><p>&nbsp;</p><p>Please review and complete this document. You can click on the document below to get started.</p>", "subject": "Please complete deport-galaxy/illum.jpeg", "labels": [{"id": "e27c3208-8cd1-4845-aec4-41b441afe476", "name": "<PERSON><PERSON><PERSON>"}, {"id": "e390ac66-86bb-472d-b248-807372e3808e", "name": "Sydney Steuber Phd"}], "fields": [[{"api_id": "CheckBox_2", "height": "13.0", "page": 1, "required": true, "type": "checkbox", "value": null, "width": "13.0", "x": 1.1, "y": 1.1, "name": null, "recipient_id": null, "signing_elements_group_id": "36cd3904-2a6b-41d6-b59e-a4f116c8a468"}, {"api_id": "CheckBox_3", "height": "13.0", "page": 1, "required": true, "type": "checkbox", "value": null, "width": "13.0", "x": 1.1, "y": 1.1, "name": null, "recipient_id": null, "signing_elements_group_id": "36cd3904-2a6b-41d6-b59e-a4f116c8a468"}, {"api_id": "Signature_1", "height": "32.0", "page": 1, "required": true, "type": "signature", "value": null, "width": "112.0", "x": 1.1, "y": 1.1, "recipient_id": "aa0e44dc-3b03-4b6d-ae46-3fdb458a77ee"}]], "files": [{"name": "aut.xlsx", "pages_number": 1}], "copied_contacts": [{"name": "Msg<PERSON>. <PERSON>", "email": "<EMAIL>"}], "recipients": [{"email": "mary<PERSON><EMAIL>", "id": "aa0e44dc-3b03-4b6d-ae46-3fdb458a77ee", "message": null, "name": "<PERSON><PERSON><PERSON>", "passcode": null, "send_email": null, "send_email_delay": null, "status": "created", "subject": null, "signing_order": 1, "signing_url": "https://www.signwell.com/docs/0d67c6e16a/", "bounced": null, "bounced_details": null, "attachment_requests": [{"name": "turkey-cutting/aut.ppt", "required": true, "url": "https://www.signwell.com/document_attachments/og73wX1kzjhMD8gwb36GhOaKypK/?access=9f609962-dc1d-490d-998c-dd0934059bdf"}]}, {"email": "<EMAIL>", "id": "ff69c57c-5a63-4931-b466-9d5b94cb2cfe", "message": null, "name": "<PERSON><PERSON>", "passcode": null, "send_email": null, "send_email_delay": null, "status": "created", "subject": null, "signing_order": 2, "signing_url": "https://www.signwell.com/docs/1a1045b4a4/", "bounced": null, "bounced_details": null, "attachment_requests": []}], "checkbox_groups": [{"id": "36cd3904-2a6b-41d6-b59e-a4f116c8a468", "group_name": null, "recipient_id": null, "checkbox_ids": ["CheckBox_2", "CheckBox_3"], "validation": null, "required": false}]}}}}, "404": {"description": "not_found", "content": {"application/json": {"example": {"message": "Not found", "meta": {"error": "record_not_found", "message": "Couldn't find the document requested", "messages": ["Couldn't find the document requested"]}}}}}}}, "delete": {"summary": "Delete Document", "description": "Deletes a document. Deleting a document will also cancel document signing (if in progress).  Supply the unique document ID from either a Create Document request or document page URL.", "tags": ["Document"], "security": [{"api_key": []}], "parameters": [{"name": "id", "in": "path", "schema": {"type": "string", "format": "uuid", "description": "Unique identifier for a document."}, "required": true}], "responses": {"204": {"description": "no content"}, "404": {"description": "not found", "content": {"application/json": {"example": {"message": "Not found", "meta": {"error": "record_not_found", "message": "Couldn't find the document requested", "messages": ["Couldn't find the document requested"]}}}}}}}}, "/api/v1/documents/": {"post": {"summary": "Create Document", "description": "Creates and optionally sends a new document for signing. If `draft` is set to true the document will not be sent.", "tags": ["Document"], "security": [{"api_key": []}], "parameters": [], "responses": {"201": {"description": "created", "content": {"application/json": {"example": {"id": "7d7d34fa-f579-4832-a8b1-c02ada9cabad", "archived": false, "embedded_edit_url": "https://www.signwell.com/edit/document/a4456371-a190-43ea-93b1-4beeed455460/", "embedded_preview_url": null, "name": "Agreement", "requester_email_address": "<EMAIL>", "status": "Created", "test_mode": false, "created_at": "2024-09-03T12:29:15Z", "updated_at": "2024-09-03T12:29:15Z", "error_message": null, "decline_message": null, "allow_decline": true, "allow_reassign": true, "api_application_id": null, "custom_requester_email": "<EMAIL>", "custom_requester_name": "Requester", "decline_redirect_url": "https://www.domain.com/decline_path/", "embedded_signing": false, "expires_in": 365, "metadata": {"key1": "value1", "key2": "value2"}, "redirect_url": "https://www.domain.com/redirect_path/", "reminders": true, "apply_signing_order": false, "message": "Normal Body", "subject": "Normal subject", "labels": [{"id": "7d4c1245-cc05-4476-b100-784de5df4c93", "name": "My First Label"}, {"id": "736d8172-46e9-4b81-8d4d-239415482c37", "name": "My Second Label"}, {"id": "a473f699-85ec-45b7-aabb-d81958c47c07", "name": "My Third Label"}], "fields": [[{"api_id": "DateField_1", "height": "19.0", "page": 1, "required": true, "type": "date", "value": "06/05/2021", "width": "86.0", "x": 230, "y": 60, "date_format": "DD/MM/YYYY", "formula": "", "lock_sign_date": true, "recipient_id": "recipient_id_1"}, {"api_id": "DateSigned_1", "height": "19.0", "page": 1, "required": true, "type": "autofill_date_signed", "value": null, "width": "86.0", "x": 260, "y": 80, "recipient_id": "recipient_id_1"}, {"api_id": "Company_1", "height": "19.0", "page": 1, "required": true, "type": "autofill_company", "value": "Great Company", "width": "86.0", "x": 260, "y": 80, "recipient_id": "recipient_id_1"}, {"api_id": "Email_1", "height": "19.0", "page": 1, "required": true, "type": "autofill_email", "value": "<EMAIL>", "width": "86.0", "x": 260, "y": 90, "recipient_id": "recipient_id_1"}, {"api_id": "Name_1", "height": "19.0", "page": 1, "required": true, "type": "autofill_name", "value": "Recipient One", "width": "86.0", "x": 260, "y": 80, "recipient_id": "recipient_id_1"}, {"api_id": "FirstName_1", "height": "19.0", "page": 1, "required": true, "type": "autofill_name", "value": "Recipient", "width": "86.0", "x": 260, "y": 80, "recipient_id": "recipient_id_1"}, {"api_id": "LastName_1", "height": "19.0", "page": 1, "required": true, "type": "autofill_name", "value": "One", "width": "86.0", "x": 260, "y": 80, "recipient_id": "recipient_id_1"}, {"api_id": "Phone_1", "height": "19.0", "page": 1, "required": true, "type": "autofill_phone", "value": "*********", "width": "86.0", "x": 260, "y": 100, "recipient_id": "recipient_id_1"}, {"api_id": "Title_1", "height": "19.0", "page": 1, "required": true, "type": "autofill_title", "value": "Important Title", "width": "86.0", "x": 260, "y": 100, "recipient_id": "recipient_id_1"}], [{"api_id": "DateField_2", "height": "2.0", "page": 1, "required": true, "type": "date", "value": "05/06/2021", "width": "1.0", "x": 260, "y": 80, "date_format": "MM/DD/YYYY", "formula": "", "lock_sign_date": false, "recipient_id": "recipient_id_2"}, {"api_id": "Signature_1", "height": "32.0", "page": 2, "required": true, "type": "signature", "value": null, "width": "112.0", "x": 260, "y": 80, "recipient_id": "recipient_id_2"}, {"api_id": "Signature_2", "height": "2.0", "page": 1, "required": true, "type": "signature", "value": null, "width": "1.0", "x": 260, "y": 80, "recipient_id": "recipient_id_2"}, {"api_id": "Initials_1", "height": "32.0", "page": 2, "required": true, "type": "initials", "value": null, "width": "44.0", "x": 260, "y": 80, "recipient_id": "recipient_id_2"}, {"api_id": "Initials_2", "height": "2.0", "page": 1, "required": true, "type": "initials", "value": null, "width": "1.0", "x": 260, "y": 80, "recipient_id": "recipient_id_2"}, {"api_id": "TextField_1", "height": "19.0", "page": 1, "required": true, "type": "text", "value": "123456", "width": "86.0", "x": 260, "y": 80, "fixed_width": true, "label": "label", "validation": "numbers", "recipient_id": "recipient_id_2"}, {"api_id": "TextField_2", "height": "2.0", "page": 1, "required": true, "type": "text", "value": "123456", "width": "1.0", "x": 260, "y": 80, "fixed_width": true, "label": "label", "validation": "numbers", "recipient_id": "recipient_id_2"}, {"api_id": "CheckBox_1", "height": "13.0", "page": 1, "required": false, "type": "checkbox", "value": "t", "width": "13.0", "x": 260, "y": 80, "name": null, "recipient_id": "recipient_id_2", "signing_elements_group_id": "ea8bb1e7-6de2-450f-99dd-1921a0a78019"}, {"api_id": "CheckBox_2", "height": "2.0", "page": 1, "required": false, "type": "checkbox", "value": "t", "width": "1.0", "x": 260, "y": 80, "name": "Checkbox name", "recipient_id": "recipient_id_2", "signing_elements_group_id": "ea8bb1e7-6de2-450f-99dd-1921a0a78019"}, {"api_id": "Company_2", "height": "19.0", "page": 1, "required": true, "type": "autofill_company", "value": "Great Company", "width": "86.0", "x": 260, "y": 80, "recipient_id": "recipient_id_1"}, {"api_id": "Email_2", "height": "19.0", "page": 1, "required": true, "type": "autofill_email", "value": "<EMAIL>", "width": "86.0", "x": 260, "y": 90, "recipient_id": "recipient_id_1"}, {"api_id": "Name_2", "height": "19.0", "page": 1, "required": true, "type": "autofill_name", "value": "Recipient One", "width": "86.0", "x": 260, "y": 80, "recipient_id": "recipient_id_1"}, {"api_id": "FirstName_2", "height": "19.0", "page": 1, "required": true, "type": "autofill_name", "value": "Recipient", "width": "86.0", "x": 260, "y": 80, "recipient_id": "recipient_id_1"}, {"api_id": "LastName_2", "height": "19.0", "page": 1, "required": true, "type": "autofill_name", "value": "One", "width": "86.0", "x": 260, "y": 80, "recipient_id": "recipient_id_1"}, {"api_id": "Phone_2", "height": "19.0", "page": 1, "required": true, "type": "autofill_phone", "value": "*********", "width": "86.0", "x": 260, "y": 100, "recipient_id": "recipient_id_1"}, {"api_id": "Title_2", "height": "19.0", "page": 1, "required": true, "type": "autofill_title", "value": "Important Title", "width": "86.0", "x": 260, "y": 100, "recipient_id": "recipient_id_1"}]], "files": [{"name": "filename1.pdf", "pages_number": 0}, {"name": "filename2.pdf", "pages_number": 0}], "copied_contacts": [{"name": "CC one", "email": "<EMAIL>"}, {"name": "CC 2", "email": "<EMAIL>"}], "recipients": [{"email": "<EMAIL>", "id": "recipient_id_1", "message": "This is a message", "name": "Recipient One", "passcode": "123", "send_email": false, "send_email_delay": 0, "status": "created", "subject": "Hi, please sign this contract", "signing_order": 1, "signing_url": "https://www.signwell.com/docs/91fbe9dcb0/", "bounced": null, "bounced_details": null, "attachment_requests": [{"name": "normal name", "required": true, "url": "https://www.signwell.com/document_attachments/Lxmzkgo7xdtgwj9YGydrieyqlM3/?access=3f5ba175-5e5f-4260-95e0-babe5ef1695a"}]}, {"email": "<EMAIL>", "id": "recipient_id_2", "message": null, "name": "Recipient 2", "passcode": null, "send_email": false, "send_email_delay": 0, "status": "created", "subject": null, "signing_order": 2, "signing_url": "https://www.signwell.com/docs/995889dc16/", "bounced": null, "bounced_details": null, "attachment_requests": [{"name": "Driver License", "required": false, "url": "https://www.signwell.com/document_attachments/a1xoWe62Y2HXp2YLyD3BIPZBEap/?access=3f5ba175-5e5f-4260-95e0-babe5ef1695a"}]}], "checkbox_groups": [{"id": "ea8bb1e7-6de2-450f-99dd-1921a0a78019", "group_name": "Group 1", "recipient_id": "recipient_id_2", "checkbox_ids": ["CheckBox_1", "CheckBox_2"], "validation": "minimum", "required": true, "min_value": 1}]}}}}, "400": {"description": "bad request", "content": {"application/json": {"example": {"errors": {"message": "The request contains invalid key values.", "invalid_keys": ["name"]}}}}}, "422": {"description": "unprocessable entity", "content": {"application/json": {"example": {"errors": {"custom_requester_email": ["is invalid"], "custom_requester_name": ["is too long (maximum is 100 characters)"], "decline_redirect_url": ["is not a valid URL"], "redirect_url": ["is not a valid URL"], "expires_in": ["must be less than or equal to 365"], "metadata": ["The key/value pairs must not exceed 50", "Check the following keys: [qgtxtpsnyxjwzvbzhnmrjziscxsmgcugpqanuygve], length needs to be less than 40 characters", "Check the value of the following keys: [ke50], length needs to be less than 500 characters and must be a string"], "api_application_id": ["The provided API application id is invalid"], "subject": ["is too long (maximum is 255 characters)"], "message": ["is too long (maximum is 4000 characters)"], "recipients": {"duplicated_ids": "These ids are duplicated: recipient_id_1.", "duplicated_emails": "These emails are duplicated: recipient1domain.com.", "recipient_1": {"name": ["is too long (maximum is 255 characters)"], "email": ["format is invalid."]}, "recipient_2": {"email": ["format is invalid."]}}, "copied_contacts": {"copied_contact_1": {"name": ["is too long (maximum is 255 characters)"], "email": ["format is invalid."]}, "copied_contact_2": {"email": ["format is invalid.", "is already a recipient"]}}, "attachment_requests": {"invalid_ids": "These recipient ids are not present in the recipients array: [recipient_id_3]"}, "fields": {"duplicated_api_ids": "These api ids are duplicated: CheckBox_2.", "invalid_ids": "These recipient ids are not present in the recipients array: [recipient_id_2, recipient_id_3]", "file_1": {"field_1": {"date_format": ["Not allowed for text."], "lock_sign_date": ["Not allowed for text."]}}, "file_2": {"field_1": {"date_format": ["Not allowed for checkbox."], "fixed_width": ["Not allowed for checkbox."], "validation": ["Not allowed for checkbox."], "label": ["Not allowed for checkbox."], "lock_sign_date": ["Not allowed for checkbox."]}, "field_2": {"invalid_date_value": "DateField value must be in Iso8601 format.", "fixed_width": ["Not allowed for date."], "validation": ["Not allowed for date."], "label": ["Not allowed for date."], "date_format": ["Allowed formats for date fields are: MM/DD/YYYY | DD/MM/YYYY | YYYY/MM/DD | Month DD, YYYY | MM/DD/YYYY hh:mm:ss a"]}, "field_3": {"date_format": ["Not allowed for initials."], "fixed_width": ["Not allowed for initials."], "validation": ["Not allowed for initials."], "label": ["Not allowed for initials."], "lock_sign_date": ["Not allowed for initials."]}, "field_4": {"date_format": ["Not allowed for signature."], "fixed_width": ["Not allowed for signature."], "validation": ["Not allowed for signature."], "label": ["Not allowed for signature."], "lock_sign_date": ["Not allowed for signature."]}}}, "checkbox_groups": {"invalid_element_type": "The checkbox group Group 1 contains elements that are not checkboxes: CheckBox_4_test.", "missing_checkbox_ids": "Missing checkbox ids: CheckBox_4_test", "recipient_id_not_found": "recipient_id recipient_id_2_test not found in the checkbox group Group 1", "fields_not_in_same_document_or_page": "The fields are not in the same document or page for the checkbox group Group 1", "min_value_negative": "The min value must be greater than or equal to 0 in the checkbox group Group 1"}, "files": {"file_1": {"file_data": ["At least one of file_url or file_base64 should be present"]}, "file_2": {"file_data": ["Only one of file_url or file_base64 should be present, not both"]}, "file_3": {"file_url": ["is not a valid URL"]}, "file_4": {"file_base64": ["the file type is unsupported, we support the following formats: application/msword, application/pdf, application/octet-stream, application/x-ole-storage, application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/vnd.ms-powerpoint, application/vnd.openxmlformats-officedocument.presentationml.presentation, application/vnd.ms-excel, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/x-iwork-keynote-sffkey, application/x-iwork-numbers-sffnumbers, application/x-iwork-pages-sffpages, image/jpeg, image/png, image/tiff, image/webp"]}, "file_5": {"name": ["The file extension is invalid."]}, "file_6": {"name": ["can't be blank"]}}}}}}}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"test_mode": {"type": "boolean", "default": false, "description": "Set to `true` to enable Test Mode. Documents created with Test Mode do not count towards API billing and are not legally binding. Defaults to `false`"}, "files": {"type": "array", "description": "Document files can be uploaded by specifying a file URL or base64 string. Either `file_url` or `file_base64` must be present (not both). Valid file types are: .pdf, .doc, .docx, .pages, .ppt, .pptx, .key, .xls, .xlsx, .numbers, .jpg, .jpeg, .png, .tiff, .tif, and .webp", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the file that will be uploaded."}, "file_url": {"type": "string", "format": "url", "description": "Publicly available URL of the file to be uploaded."}, "file_base64": {"type": "string", "format": "byte", "description": "A RFC 4648 base64 string of the file to be uploaded."}}, "required": ["name"]}}, "name": {"type": "string", "description": "The name of the document."}, "subject": {"type": "string", "description": "Email subject for the signature request that recipients will see. Defaults to the default system subject or a template subject (if the document is created from a template)."}, "message": {"type": "string", "description": "Email message for the signature request that recipients will see. Defaults to the default system message or a template message (if the document is created from a template)."}, "recipients": {"type": "array", "description": "Document recipients are people that must complete and/or sign a document.", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "A unique identifier that you will give to each recipient. We recommend numbering sequentially from 1 to X. IDs are required for associating recipients to fields and more."}, "name": {"type": "string", "description": "Name of the recipient."}, "email": {"type": "string", "format": "email", "description": "Email address for the recipient."}, "passcode": {"type": "string", "description": "If set, signers assigned with a passcode will be required to enter the passcode before they’re able to view and complete the document."}, "subject": {"type": "string", "description": "Email subject for the signature request that the recipient will see. Overrides the general subject for the document."}, "message": {"type": "string", "description": "Email message for the signature request that the recipient will see. Overrides the general message for the document."}, "send_email": {"type": "boolean", "default": false, "description": "Applies on when `embedded_signing` is `true`. By default, recipients are not notified through email to sign when doing embedded signing. Setting this to `true`  will send a notification email to the recipient. Default is `false`."}, "send_email_delay": {"type": "integer", "default": 0, "description": "If `send_email` is `true` recipients will receive a new document notification immediately. In the case of embedded signing, you can delay this notification to only send if the document is not completed within a few minutes. The email notification will not go out if the document is completed before the delay time is over. Valid values are in minutes ranging from `0` to `60`. Defaults to `0`."}}, "required": ["id", "email"]}}, "draft": {"type": "boolean", "default": false, "description": "Whether the document can still be updated before sending a signature request. If set to `false` the document is sent for signing as part of this request. Defaults to `false`."}, "with_signature_page": {"type": "boolean", "default": false, "description": "When set to `true` the document will have a signature page added to the end, and all signers will be required to add their signature on that page."}, "expires_in": {"type": "integer", "minimum": 1, "description": "Number of days before the signature request expires. Defaults to the account expiration setting or template expiration (if the document is created from a template)."}, "reminders": {"type": "boolean", "default": true, "description": "Whether to send signing reminders to recipients. Reminders are sent on day 3, day 6, and day 10 if set to `true`. Defaults to `true`."}, "apply_signing_order": {"type": "boolean", "default": false, "description": "When set to `true` recipients will sign one at a time in the order of the `recipients` collection of this request."}, "api_application_id": {"type": "string", "format": "uuid", "description": "Unique identifier for API Application settings to use. API Applications are optional and mainly used when isolating OAuth apps or for more control over embedded API settings"}, "embedded_signing": {"type": "boolean", "default": false, "description": "When set to `true` it enables embedded signing in your website/web application. Embedded functionality works with an iFrame and email authentication is disabled. :embedded_signinig defaults to `false`."}, "embedded_signing_notifications": {"type": "boolean", "default": false, "description": "On embedding signing, document owners (and CC'd contacts) do not get a notification email when documents have been completed. Setting this param to `true` will send out those final completed notifications. Default is `false`"}, "text_tags": {"type": "boolean", "default": false, "description": "An alternative way (if you can’t use the recommended way) of placing fields in specific locations of your document by using special text tags. Useful when changing the content of your files changes the location of fields. See API documentation for “Text Tags” for details. Defaults to false."}, "custom_requester_name": {"type": "string", "description": "Sets the custom requester name for the document. When set, this is the name used for all email communications, signing notifications, and in the audit file."}, "custom_requester_email": {"type": "string", "format": "email", "description": "Sets the custom requester email for the document. When set, this is the email used for all email communications, signing notifications, and in the audit file."}, "redirect_url": {"type": "string", "format": "url", "description": "A URL that recipients are redirected to after successfully signing a document."}, "allow_decline": {"type": "boolean", "default": true, "description": "Whether to allow recipients the option to decline signing a document. If multiple signers are involved in a document, any single recipient can cancel the entire document signing process by declining to sign."}, "allow_reassign": {"type": "boolean", "default": true, "description": "In some cases a signer is not the right person to sign and may need to reassign their signing responsibilities to another person. This feature allows them to reassign the document to someone else."}, "decline_redirect_url": {"type": "string", "format": "url", "description": "A URL that recipients are redirected to if the document is declined."}, "metadata": {"type": "object", "description": "Optional key-value data that can be associated with the document. If set, will be available every time the document data is returned."}, "fields": {"type": "array", "description": "Document fields placed on a document for collecting data or signatures from recipients. At least one field must be present in the Create Document request if `draft` is `false` (unless adding a signature page by using `with_signature_page`). Field data should be sent as a 2-dimensional JSON array. One array of fields is needed for each file in the files array. An array of fields can be empty if you have a file that does not contain any fields.", "items": {"type": "array", "description": "Array of Fields you're adding to each file.", "items": {"type": "object", "properties": {"x": {"type": "number", "format": "float", "description": "Horizontal value in the coordinates of the field (in pixels). Coordinates are specific to the page where fields are located."}, "y": {"type": "number", "format": "float", "description": "Vertical value in the coordinates of the field (in pixels). Coordinates are specific to the page where fields are located."}, "page": {"type": "integer", "description": "The page number within the file. If the page does not exist within the file then the field won't be created."}, "recipient_id": {"type": "string", "description": "Unique identifier of the recipient assigned to the field. Recipients assigned to fields will be the only ones that will see and be able to complete those fields."}, "type": {"type": "string", "enum": ["initials", "signature", "checkbox", "date", "text", "autofill_company", "autofill_email", "autofill_first_name", "autofill_last_name", "autofill_name", "autofill_phone", "autofill_title", "autofill_date_signed"], "description": "Field type of the field. Valid field types: initials, signatures, checkbox, date, and text. To autofill fields with contact data, use an autofill field type. To group checkbox fields, enter an api_id for each checkbox and add the checkbox_groups parameter."}, "required": {"type": "boolean", "default": true, "description": "Whether the field must be completed by the recipient. Defaults to `true` except for checkbox type fields."}, "label": {"type": "string", "description": "Text and Date fields only: label that is displayed when the field is empty."}, "value": {"description": "Varies according to the field type. Text fields accept strings or numbers. Date fields accept Iso8601 date strings. CheckBoxes accept booleans. Signature and Initials fields can't be signed through API requests. Autofill text fields accept strings or numbers."}, "api_id": {"type": "string", "description": "Unique identifier of the field. Useful when needing to reference specific field values or update a document and its fields."}, "name": {"type": "string", "description": "Checkbox fields only. At least 2 checkbox fields in an array of fields must be assigned to the same recipient and grouped with selection requirements."}, "validation": {"type": "string", "enum": ["no_text_validation", "numbers", "letters", "email_address", "us_phone_number", "us_zip_code", "us_ssn", "us_age", "alphanumeric", "us_bank_routing_number", "us_bank_account_number"], "description": "Text fields only: optional validation for field values. Valid values: numbers, letters, email_address, us_phone_number, us_zip_code, us_ssn, us_age, alphanumeric, us_bank_routing_number, us_bank_account."}, "fixed_width": {"type": "boolean", "default": false, "description": "Text fields only: whether the field width will stay fixed and text will display in multiple lines, rather than one long line. If set to `false` the field width will automatically grow horizontally to fit text on one line. Defaults to `false`."}, "lock_sign_date": {"type": "boolean", "default": false, "description": "Date fields only: makes fields readonly and automatically populates with the date the recipient signed. Defaults to `false`."}, "date_format": {"type": "string", "enum": ["MM/DD/YYYY", "DD/MM/YYYY", "YYYY/MM/DD", "Month DD, YYYY", "MM/DD/YYYY hh:mm:ss a"], "description": "Date fields only: date format to use for the field. Valid values: MM/DD/YYYY, DD/MM/YYYY, YYYY/MM/DD, Month DD, YYYY, and MM/DD/YYYY hh:mm:ss a. Defaults to MM/DD/YYYY."}, "formula": {"type": "string", "description": "Date fields only (text field formulas coming soon): formulas are a way to prefill fields with calculated future or past dates. Addition, subtraction, and parentheses are allowed. Valid event dates are `created_date`, `sent_date`, and `signed_date`. Valid time periods are `day`, `days`, `week`, `weeks`, `month`, and `months`. Example: `formula: \"sent_date + 10 days\"`. Use with `lock_sign_date` if you'd like to make the field readonly and prevent signers from choosing a different date."}}, "required": ["x", "y", "page", "recipient_id", "type"]}}}, "attachment_requests": {"type": "array", "description": "Attachments that a recipient must upload to complete the signing process. Attachment requests are shown after all document fields have been completed.", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the requested attachment."}, "recipient_id": {"type": "string", "description": "Unique identifier of the recipient that will view the attachment request."}, "required": {"type": "boolean", "default": true, "description": "Whether the recipient will need to upload the attachment to successfully complete/sign the document. Defaults to `true`."}}, "required": ["name", "recipient_id"]}}, "copied_contacts": {"type": "array", "description": "Copied contacts are emailed the final document once it has been completed by all recipients.", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the copied contact."}, "email": {"type": "string", "format": "email", "description": "Email for the copied contact."}}, "required": ["email"]}}, "labels": {"type": "array", "description": "Labels can be used to organize documents in a way that can make it easy to find using the document search in SignWell. A document can have multiple labels.", "items": {"type": "object", "description": "Labels can be used to organize documents and templates in a way that can make it easy to find using the document search/template search in SignWell. Labels can be used to organize documents in a way that can make it easy to find using the document search in SignWell.", "properties": {"name": {"type": "string"}}, "required": ["name"]}}, "checkbox_groups": {"type": "array", "description": "Checkbox fields that are placed on a document can be grouped with selection requirements. At least 2 checkbox fields in an array of fields must be assigned to the same recipient.", "items": {"type": "object", "properties": {"group_name": {"type": "string", "description": "A unique identifier for the checkbox group."}, "recipient_id": {"type": "string", "description": "The recipient ID associated with the checkbox group."}, "checkbox_ids": {"type": "array", "items": {"type": "string", "description": "A unique identifier for each checkbox in a group. ID must match the api_id of the checkbox field."}}, "validation": {"type": "string", "enum": ["minimum", "maximum", "range", "exact"], "description": "Set requirements for the group of one or multiple selections by the recipient. Defaults to minimum. Validation values: minimum, maximum, exact, range."}, "required": {"type": "boolean", "default": false, "description": "Whether the group must be completed by the recipient. Defaults to false."}, "min_value": {"type": "integer", "description": "The minimum number of checkboxes that must be checked in the group. (Only for validation: minimum and range)"}, "max_value": {"type": "integer", "description": "The maximum number of checkboxes that can be checked in the group. (Only for validation: maximum and range)"}, "exact_value": {"type": "integer", "description": "The exact number of checkboxes that must be checked in the group. (Only for validation: exact)"}}, "required": ["group_name", "recipient_id", "checkbox_ids"]}}}, "required": ["files", "recipients"]}}}}}}, "/api/v1/document_templates/documents/": {"post": {"summary": "Create Document from Template", "description": "Creates and optionally sends a new document for signing. If `draft` is set to true the document will not be sent.", "tags": ["Document"], "security": [{"api_key": []}], "parameters": [], "responses": {"201": {"description": "created", "content": {"application/json": {"example": {"id": "20817a04-6676-4b9f-8b66-22622348dfa4", "archived": false, "embedded_edit_url": "https://www.signwell.com/edit/document/52d0a05b-2493-434c-8369-4690e72c7d3d/", "embedded_preview_url": null, "name": "Agreement", "requester_email_address": "<EMAIL>", "status": "Created", "test_mode": false, "created_at": "2024-09-03T12:29:17Z", "updated_at": "2024-09-03T12:29:17Z", "error_message": null, "decline_message": null, "template_ids": ["28c037ee-1fae-4d91-99d2-6ba6de149c45", "53e4b9c0-eea1-4fcd-b563-364ee025e715"], "allow_decline": true, "allow_reassign": true, "api_application_id": null, "custom_requester_email": "<EMAIL>", "custom_requester_name": "Requester", "decline_redirect_url": "https://www.domain.com/decline_path/", "embedded_signing": false, "expires_in": 365, "metadata": {"key1": "value1", "key2": "value2"}, "redirect_url": "https://www.domain.com/redirect_path/", "reminders": true, "apply_signing_order": false, "message": "Normal Body", "subject": "Normal subject", "labels": [], "fields": [[{"api_id": "Signature_1", "height": "19.0", "page": 1, "required": true, "type": "signature", "value": "", "width": "86.0", "x": 1.1, "y": 1.1, "recipient_id": "document_sender"}, {"api_id": "TextField_1", "height": "19.0", "page": 1, "required": true, "type": "text", "value": "<EMAIL>", "width": "86.0", "x": 1.1, "y": 1.1, "fixed_width": false, "label": "", "validation": "no_text_validation", "recipient_id": "recipient_1"}, {"api_id": "DateField_1", "height": "19.0", "page": 1, "required": true, "type": "date", "value": "05/06/2021", "width": "86.0", "x": 1.1, "y": 1.1, "date_format": "MM/DD/YYYY", "formula": "", "lock_sign_date": false, "recipient_id": "recipient_1"}, {"api_id": "DateField_2", "height": "19.0", "page": 1, "required": true, "type": "date", "value": null, "width": "86.0", "x": 1.1, "y": 1.1, "date_format": "MM/DD/YYYY", "formula": "", "lock_sign_date": true, "recipient_id": "recipient_1"}, {"api_id": "CheckBox_1", "height": "19.0", "page": 1, "required": true, "type": "checkbox", "value": "t", "width": "86.0", "x": 1.1, "y": 1.1, "name": null, "recipient_id": "recipient_1"}], [{"api_id": "Initials_1", "height": "19.0", "page": 1, "required": true, "type": "initials", "value": "", "width": "86.0", "x": 1.1, "y": 1.1, "recipient_id": "document_sender"}, {"api_id": "TextField_1_1", "height": "19.0", "page": 1, "required": true, "type": "text", "value": "<EMAIL>", "width": "86.0", "x": 1.1, "y": 1.1, "fixed_width": false, "label": "", "validation": "email_address", "recipient_id": "recipient_2"}, {"api_id": "TextField_2", "height": "19.0", "page": 1, "required": true, "type": "text", "value": "", "width": "86.0", "x": 1.1, "y": 1.1, "fixed_width": false, "label": "", "validation": "no_text_validation", "recipient_id": "recipient_2"}], [{"api_id": "DateField_1_1", "height": "19.0", "page": 1, "required": true, "type": "date", "value": null, "width": "86.0", "x": 230, "y": 60, "date_format": "DD/MM/YYYY", "formula": "", "lock_sign_date": true, "recipient_id": "recipient_1"}, {"api_id": "DateField_2_1", "height": "19.0", "page": 1, "required": true, "type": "date", "value": "05/06/2021", "width": "86.0", "x": 230, "y": 120, "date_format": "MM/DD/YYYY", "formula": "", "lock_sign_date": false, "recipient_id": "recipient_1"}], [{"api_id": "Signature_1_1", "height": "32.0", "page": 2, "required": true, "type": "signature", "value": null, "width": "112.0", "x": 260, "y": 80, "recipient_id": "recipient_2"}, {"api_id": "CheckBox_3", "height": "13.0", "page": 1, "required": true, "type": "checkbox", "value": "t", "width": "13.0", "x": 260, "y": 80, "name": null, "recipient_id": "recipient_2"}, {"api_id": "Initials_4", "height": "32.0", "page": 2, "required": true, "type": "initials", "value": null, "width": "44.0", "x": 260, "y": 80, "recipient_id": "recipient_2"}, {"api_id": "TextField_5", "height": "19.0", "page": 1, "required": true, "type": "text", "value": "123456", "width": "86.0", "x": 260, "y": 80, "fixed_width": true, "label": "label", "validation": "numbers", "recipient_id": "recipient_2"}]], "files": [{"name": "vitae.numbers", "pages_number": 2}, {"name": "asperiores.mp3", "pages_number": 2}, {"name": "filename1.pdf", "pages_number": 0}, {"name": "filename2.pdf", "pages_number": 0}], "copied_contacts": [{"name": "CC one", "email": "<EMAIL>"}, {"name": "CC 2", "email": "<EMAIL>"}], "recipients": [{"email": "<EMAIL>", "id": "document_sender", "message": null, "name": "Recipient 0", "passcode": "123", "send_email": false, "send_email_delay": 0, "status": "created", "subject": null, "signing_order": 1, "signing_url": "https://www.signwell.com/docs/81c497409e/", "bounced": null, "bounced_details": null, "placeholder_name": "Document Sender", "attachment_requests": []}, {"email": "<EMAIL>", "id": "recipient_1", "message": null, "name": "Recipient 1", "passcode": null, "send_email": false, "send_email_delay": 0, "status": "created", "subject": null, "signing_order": 2, "signing_url": "https://www.signwell.com/docs/54882fb0de/", "bounced": null, "bounced_details": null, "placeholder_name": "Placeholder 1", "attachment_requests": [{"name": "normal name", "required": true, "url": "https://www.signwell.com/document_attachments/kDlDZNwyy7FENmwgpz2YH2Nmogbp/?access=692fa28a-fc4c-484b-a295-0c64d6f2de6a"}]}, {"email": "<EMAIL>", "id": "recipient_2", "message": null, "name": "Recipient 2", "passcode": null, "send_email": false, "send_email_delay": 0, "status": "created", "subject": null, "signing_order": 3, "signing_url": "https://www.signwell.com/docs/5b1847f8ec/", "bounced": null, "bounced_details": null, "placeholder_name": "Placeholder 2", "attachment_requests": [{"name": "Driver License", "required": false, "url": "https://www.signwell.com/document_attachments/6XB4znnqVZSY1oNDz6gqI9pY4Ex/?access=692fa28a-fc4c-484b-a295-0c64d6f2de6a"}]}], "checkbox_groups": []}}}}, "400": {"description": "bad request", "content": {"application/json": {"example": {"errors": {"message": "The request contains invalid key values.", "invalid_keys": ["name"]}}}}}, "422": {"description": "unprocessable entity", "content": {"application/json": {"example": {"errors": {"template_id_or_ids": "At least one of template_id or template_ids should be present.", "custom_requester_email": ["is invalid"], "custom_requester_name": ["is too long (maximum is 100 characters)"], "decline_redirect_url": ["is not a valid URL"], "redirect_url": ["is not a valid URL"], "expires_in": ["must be less than or equal to 365"], "metadata": ["The key/value pairs must not exceed 50", "Check the following keys: [qgtxtpsnyxjwzvbzhnmrjziscxsmgcugpqanuygve], length needs to be less than 40 characters", "Check the value of the following keys: [ke50], length needs to be less than 500 characters and must be a string"], "api_application_id": ["The provided API application id is invalid"], "subject": ["is too long (maximum is 255 characters)"], "message": ["is too long (maximum is 4000 characters)"], "recipients": {"duplicated_ids": "These ids are duplicated: recipient_id_1.", "duplicated_emails": "These emails are duplicated: recipient1domain.com.", "recipient_1": {"name": ["is too long (maximum is 255 characters)"], "email": ["format is invalid."]}, "recipient_2": {"email": ["format is invalid."]}}, "copied_contacts": {"copied_contact_1": {"name": ["is too long (maximum is 255 characters)"], "email": ["format is invalid."]}, "copied_contact_2": {"email": ["format is invalid.", "is already a recipient"]}}, "attachment_requests": {"invalid_ids": "These recipient ids are not present in the recipients array: [recipient_id_3]"}, "fields": {"duplicated_api_ids": "These api ids are duplicated: CheckBox_2.", "invalid_ids": "These recipient ids are not present in the recipients array: [recipient_id_2, recipient_id_3]", "file_1": {"field_1": {"date_format": ["Not allowed for text."], "lock_sign_date": ["Not allowed for text."]}}, "file_2": {"field_1": {"date_format": ["Not allowed for checkbox."], "fixed_width": ["Not allowed for checkbox."], "validation": ["Not allowed for checkbox."], "label": ["Not allowed for checkbox."], "lock_sign_date": ["Not allowed for checkbox."]}, "field_2": {"invalid_date_value": "DateField value must be in Iso8601 format.", "fixed_width": ["Not allowed for date."], "validation": ["Not allowed for date."], "label": ["Not allowed for date."], "date_format": ["Allowed formats for date fields are: MM/DD/YYYY | DD/MM/YYYY | YYYY/MM/DD | Month DD, YYYY | MM/DD/YYYY hh:mm:ss a"]}, "field_3": {"date_format": ["Not allowed for initials."], "fixed_width": ["Not allowed for initials."], "validation": ["Not allowed for initials."], "label": ["Not allowed for initials."], "lock_sign_date": ["Not allowed for initials."]}, "field_4": {"date_format": ["Not allowed for signature."], "fixed_width": ["Not allowed for signature."], "validation": ["Not allowed for signature."], "label": ["Not allowed for signature."], "lock_sign_date": ["Not allowed for signature."]}}}, "files": {"file_1": {"file_data": ["At least one of file_url or file_base64 should be present"]}, "file_2": {"file_data": ["Only one of file_url or file_base64 should be present, not both"]}, "file_3": {"file_url": ["is not a valid URL"]}, "file_4": {"file_base64": ["the file type is unsupported, we support the following formats: application/msword, application/pdf, application/octet-stream, application/x-ole-storage, application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/vnd.ms-powerpoint, application/vnd.openxmlformats-officedocument.presentationml.presentation, application/vnd.ms-excel, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/x-iwork-keynote-sffkey, application/x-iwork-numbers-sffnumbers, application/x-iwork-pages-sffpages, image/jpeg, image/png, image/tiff, image/webp"]}, "file_5": {"name": ["The file extension is invalid."]}, "file_6": {"name": ["can't be blank"]}}}}}}}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"test_mode": {"type": "boolean", "default": false, "description": "Set to `true` to enable Test Mode. Documents created with Test Mode do not count towards API billing and are not legally binding. Defaults to `false`"}, "template_id": {"type": "string", "format": "uuid", "description": "Use when you have to create a document from a single template. Either :template_id or :template_ids must be present in the request, not both."}, "template_ids": {"type": "array", "description": "Use when you have to create a document from multiple templates. Either :template_id or :template_ids must be present in the request, not both.", "items": {"type": "string"}}, "name": {"type": "string", "description": "The name of the document."}, "subject": {"type": "string", "description": "Email subject for the signature request that recipients will see. Defaults to the default system subject or a template subject (if the document is created from a template)."}, "message": {"type": "string", "description": "Email message for the signature request that recipients will see. Defaults to the default system message or a template message (if the document is created from a template)."}, "recipients": {"type": "array", "description": "Document recipients are people that must complete and/or sign a document. Recipients of the document must be assigned to a placeholder of the template. Recipients will inherit all placeholder fields and settings.", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "A unique identifier that you will give to each recipient. We recommend numbering sequentially from 1 to X. IDs are required for associating recipients to fields and more."}, "name": {"type": "string", "description": "Name of the recipient."}, "email": {"type": "string", "format": "email", "description": "Email address for the recipient."}, "placeholder_name": {"type": "string", "description": "The name of the placeholder you want this recipient assigned to."}, "passcode": {"type": "string", "description": "If set, signers assigned with a passcode will be required to enter the passcode before they’re able to view and complete the document."}, "subject": {"type": "string", "description": "Email subject for the signature request that the recipient will see. Overrides the general subject for the template."}, "message": {"type": "string", "description": "Email message for the signature request that the recipient will see. Overrides the general message for the template."}, "send_email": {"type": "boolean", "default": false, "description": "Applies on when `embedded_signing` is `true`. By default, recipients are not notified through email to sign when doing embedded signing. Setting this to `true`  will send a notification email to the recipient. Default is `false`."}, "send_email_delay": {"type": "integer", "default": 0, "description": "If `send_email` is `true` recipients will receive a new document notification immediately. In the case of embedded signing, you can delay this notification to only send if the document is not completed within a few minutes. The email notification will not go out if the document is completed before the delay time is over. Valid values are in minutes ranging from `0` to `60`. Defaults to `0`."}}, "required": ["id", "email"]}}, "draft": {"type": "boolean", "default": false, "description": "Whether the document can still be updated before sending a signature request. If set to `false` the document is sent for signing as part of this request. Defaults to `false`."}, "with_signature_page": {"type": "boolean", "default": false, "description": "When set to `true` the document will have a signature page added to the end, and all signers will be required to add their signature on that page."}, "expires_in": {"type": "integer", "minimum": 1, "description": "Number of days before the signature request expires. Defaults to the account expiration setting or template expiration (if the document is created from a template)."}, "reminders": {"type": "boolean", "default": true, "description": "Whether to send signing reminders to recipients. Reminders are sent on day 3, day 6, and day 10 if set to `true`. Defaults to `true`."}, "apply_signing_order": {"type": "boolean", "default": false, "description": "When set to `true` recipients will sign one at a time in the order of the `recipients` collection of this request."}, "api_application_id": {"type": "string", "format": "uuid", "description": "Unique identifier for API Application settings to use. API Applications are optional and mainly used when isolating OAuth apps or for more control over embedded API settings"}, "embedded_signing": {"type": "boolean", "default": false, "description": "When set to `true` it enables embedded signing in your website/web application. Embedded functionality works with an iFrame and email authentication is disabled. :embedded_signinig defaults to `false`."}, "embedded_signing_notifications": {"type": "boolean", "default": false, "description": "On embedding signing, document owners (and CC'd contacts) do not get a notification email when documents have been completed. Setting this param to `true` will send out those final completed notifications. Default is `false`"}, "text_tags": {"type": "boolean", "default": false, "description": "An alternative way (if you can’t use the recommended way) of placing fields in specific locations of your document by using special text tags. Useful when changing the content of your files changes the location of fields. See API documentation for “Text Tags” for details. Defaults to false."}, "custom_requester_name": {"type": "string", "description": "Sets the custom requester name for the document. When set, this is the name used for all email communications, signing notifications, and in the audit file."}, "custom_requester_email": {"type": "string", "format": "email", "description": "Sets the custom requester email for the document. When set, this is the email used for all email communications, signing notifications, and in the audit file."}, "redirect_url": {"type": "string", "format": "url", "description": "A URL that recipients are redirected to after successfully signing a document."}, "allow_decline": {"type": "boolean", "default": true, "description": "Whether to allow recipients the option to decline signing a document. If multiple signers are involved in a document, any single recipient can cancel the entire document signing process by declining to sign."}, "allow_reassign": {"type": "boolean", "default": true, "description": "In some cases a signer is not the right person to sign and may need to reassign their signing responsibilities to another person. This feature allows them to reassign the document to someone else."}, "decline_redirect_url": {"type": "string", "format": "url", "description": "A URL that recipients are redirected to if the document is declined."}, "metadata": {"type": "object", "description": "Optional key-value data that can be associated with the document. If set, will be available every time the document data is returned."}, "template_fields": {"type": "array", "description": "Fields of your template(s) that you can prepopulate with values. Signature and Initials fields cannot be signed through the API.", "items": {"type": "object", "properties": {"api_id": {"type": "string", "description": "The API ID of the field in your template. This field is case sensitive."}, "value": {"description": "TextField value must be a string or a number."}}, "required": ["api_id", "value"]}}, "files": {"type": "array", "items": {"type": "object", "description": "Additional files to be appended to the document. Will not replace existing files from the template. Document files can be uploaded by specifying a file URL or base64 string. Either `file_url` or `file_base64` must be present (not both). Valid file types are: .pdf, .docx, .jpg, .png, .ppt, .xls, .pages, and .txt.", "properties": {"name": {"type": "string", "description": "Name of the file that will be uploaded."}, "file_url": {"type": "string", "format": "url", "description": "Publicly available URL of the file to be uploaded."}, "file_base64": {"type": "string", "format": "byte", "description": "A RFC 4648 base64 string of the file to be uploaded."}}, "required": ["name"]}}, "fields": {"type": "array", "description": "Fields to be added to any appended files (not existing files). Document fields placed on a document for collecting data or signatures from recipients. Field data should be sent as a 2-dimensional JSON array. One array of fields is needed for each file in the files array. An array of fields can be empty if you have a file that does not contain any fields.", "items": {"type": "array", "description": "Array of Fields you're adding to each file.", "items": {"type": "object", "properties": {"x": {"type": "number", "format": "float", "description": "Horizontal value in the coordinates of the field (in pixels). Coordinates are specific to the page where fields are located."}, "y": {"type": "number", "format": "float", "description": "Vertical value in the coordinates of the field (in pixels). Coordinates are specific to the page where fields are located."}, "page": {"type": "integer", "description": "The page number within the file. If the page does not exist within the file then the field won't be created."}, "recipient_id": {"type": "string", "description": "Unique identifier of the recipient assigned to the field. Recipients assigned to fields will be the only ones that will see and be able to complete those fields."}, "type": {"type": "string", "enum": ["initials", "signature", "checkbox", "date", "text", "autofill_company", "autofill_email", "autofill_first_name", "autofill_last_name", "autofill_name", "autofill_phone", "autofill_title", "autofill_date_signed"], "description": "Field type of the field. Valid field types: initials, signatures, checkbox, date, and text. To autofill fields with contact data, use an autofill field type. To group checkbox fields, enter an api_id for each checkbox and add the checkbox_groups parameter."}, "required": {"type": "boolean", "default": true, "description": "Whether the field must be completed by the recipient. Defaults to `true` except for checkbox type fields."}, "label": {"type": "string", "description": "Text and Date fields only: label that is displayed when the field is empty."}, "value": {"description": "Varies according to the field type. Text fields accept strings or numbers. Date fields accept Iso8601 date strings. CheckBoxes accept booleans. Signature and Initials fields can't be signed through API requests. Autofill text fields accept strings or numbers."}, "api_id": {"type": "string", "description": "Unique identifier of the field. Useful when needing to reference specific field values or update a document and its fields."}, "name": {"type": "string", "description": "Checkbox fields only. At least 2 checkbox fields in an array of fields must be assigned to the same recipient and grouped with selection requirements."}, "validation": {"type": "string", "enum": ["no_text_validation", "numbers", "letters", "email_address", "us_phone_number", "us_zip_code", "us_ssn", "us_age", "alphanumeric", "us_bank_routing_number", "us_bank_account_number"], "description": "Text fields only: optional validation for field values. Valid values: numbers, letters, email_address, us_phone_number, us_zip_code, us_ssn, us_age, alphanumeric, us_bank_routing_number, us_bank_account."}, "fixed_width": {"type": "boolean", "default": false, "description": "Text fields only: whether the field width will stay fixed and text will display in multiple lines, rather than one long line. If set to `false` the field width will automatically grow horizontally to fit text on one line. Defaults to `false`."}, "lock_sign_date": {"type": "boolean", "default": false, "description": "Date fields only: makes fields readonly and automatically populates with the date the recipient signed. Defaults to `false`."}, "date_format": {"type": "string", "enum": ["MM/DD/YYYY", "DD/MM/YYYY", "YYYY/MM/DD", "Month DD, YYYY", "MM/DD/YYYY hh:mm:ss a"], "description": "Date fields only: date format to use for the field. Valid values: MM/DD/YYYY, DD/MM/YYYY, YYYY/MM/DD, Month DD, YYYY, and MM/DD/YYYY hh:mm:ss a. Defaults to MM/DD/YYYY."}}, "required": ["x", "y", "page", "recipient_id", "type"]}}}, "attachment_requests": {"type": "array", "description": "Attachments that a recipient must upload to complete the signing process. Attachment requests are shown after all document fields have been completed.", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the requested attachment."}, "recipient_id": {"type": "string", "description": "Unique identifier of the recipient that will view the attachment request."}, "required": {"type": "boolean", "default": true, "description": "Whether the recipient will need to upload the attachment to successfully complete/sign the document. Defaults to `true`."}}, "required": ["name", "recipient_id"]}}, "copied_contacts": {"type": "array", "description": "Copied contacts are emailed the final document once it has been completed by all recipients.", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the copied contact."}, "email": {"type": "string", "format": "email", "description": "Email for the copied contact."}}, "required": ["email"]}}, "labels": {"type": "array", "description": "Labels can be used to organize documents in a way that can make it easy to find using the document search in SignWell. A document can have multiple labels. Updating labels on a document will replace any existing labels for that document.", "items": {"type": "object", "description": "Labels can be used to organize documents and templates in a way that can make it easy to find using the document search/template search in SignWell. Labels can be used to organize documents in a way that can make it easy to find using the document search in SignWell.", "properties": {"name": {"type": "string"}}, "required": ["name"]}}, "checkbox_groups": {"type": "array", "description": "Checkbox fields that are placed on a document can be grouped with selection requirements. At least 2 checkbox fields in an array of fields must be assigned to the same recipient.", "items": {"type": "object", "properties": {"group_name": {"type": "string", "description": "A unique identifier for the checkbox group."}, "recipient_id": {"type": "string", "description": "The recipient ID associated with the checkbox group."}, "checkbox_ids": {"type": "array", "items": {"type": "string", "description": "A unique identifier for each checkbox in a group. ID must match the api_id of the checkbox field."}}, "validation": {"type": "string", "enum": ["minimum", "maximum", "range", "exact"], "description": "Set requirements for the group of one or multiple selections by the recipient. Defaults to minimum. Validation values: minimum, maximum, exact, range."}, "required": {"type": "boolean", "default": false, "description": "Whether the group must be completed by the recipient. Defaults to false."}, "min_value": {"type": "integer", "description": "The minimum number of checkboxes that must be checked in the group. (Only for validation: minimum and range)"}, "max_value": {"type": "integer", "description": "The maximum number of checkboxes that can be checked in the group. (Only for validation: maximum and range)"}, "exact_value": {"type": "integer", "description": "The exact number of checkboxes that must be checked in the group. (Only for validation: exact)"}}, "required": ["group_name", "recipient_id", "checkbox_ids"]}}}, "required": ["recipients"]}}}}}}, "/api/v1/documents/{id}/send/": {"post": {"summary": "Update and Send Document", "description": "Updates a draft document and sends it to be signed by recipients.", "tags": ["Document"], "security": [{"api_key": []}], "parameters": [{"name": "id", "in": "path", "schema": {"type": "string", "format": "uuid", "description": "Unique identifier for a document."}, "required": true}], "responses": {"201": {"description": "created", "content": {"application/json": {"example": {"id": "09e5e5ad-7b67-4a43-bea5-5c23770490f4", "archived": false, "copied_contacts": [{"email": "<EMAIL>", "name": "<PERSON><PERSON>. <PERSON><PERSON>"}], "created_at": "2024-09-03T12:29:18Z", "custom_requester_email": "<EMAIL>", "custom_requester_name": "<PERSON><PERSON><PERSON>", "decline_redirect_url": "https://www.domain.com/decline_path/", "embedded_edit_url": "https://www.signwell.com/edit/document/1a3e21cf-c645-424e-8cd1-2c1292a74440/", "embedded_preview_url": null, "error_message": null, "fields": [[{"api_id": "CheckBox_8", "height": "13.0", "required": true, "type": "checkbox", "value": null, "width": "13.0", "x": 1.1, "y": 1.1, "name": null, "page": 1, "recipient_id": null}, {"api_id": "CheckBox_9", "height": "13.0", "required": true, "type": "checkbox", "value": null, "width": "13.0", "x": 1.1, "y": 1.1, "name": null, "page": 1, "recipient_id": null}, {"api_id": "Signature_7", "height": "32.0", "required": true, "type": "signature", "value": null, "width": "112.0", "x": 1.1, "y": 1.1, "page": 1, "recipient_id": "6aaedbfe-a41a-4923-a5c6-e1167a75d5f0"}]], "labels": [], "metadata": {"key1": "value1", "key2": "value2"}, "name": "Agreement", "recipients": [{"id": "6aaedbfe-a41a-4923-a5c6-e1167a75d5f0", "attachment_requests": [], "email": "<EMAIL>", "message": null, "name": "<PERSON>", "passcode": null, "send_email_delay": null, "signing_order": 7, "status": "draft", "subject": null, "bounced": null, "bounced_details": null, "send_email": null, "signing_url": "https://www.signwell.com/docs/892ec546b0/"}, {"id": "76369601-e749-41b8-9423-1df9b4107ce0", "attachment_requests": [], "email": "<EMAIL>", "message": null, "name": "<PERSON><PERSON>", "passcode": null, "send_email_delay": null, "signing_order": 8, "status": "draft", "subject": null, "bounced": null, "bounced_details": null, "send_email": null, "signing_url": "https://www.signwell.com/docs/7640f7dec3/"}], "subject": "Normal subject", "test_mode": false, "updated_at": "2024-09-03T12:29:19Z", "decline_message": null, "api_application_id": null, "allow_decline": true, "allow_reassign": true, "apply_signing_order": false, "embedded_signing": false, "expires_in": 365, "message": "Normal Body", "reminders": true, "requester_email_address": "<EMAIL>", "redirect_url": "https://www.domain.com/redirect_path/", "status": "Draft", "files": [{"name": "magnam.ods", "pages_number": 1}]}}}}, "422": {"description": "unprocessable entity", "content": {"application/json": {"example": {"errors": {"decline_redirect_url": ["is not a valid URL"], "redirect_url": ["is not a valid URL"], "expires_in": ["must be less than or equal to 365"], "metadata": ["The key/value pairs must not exceed 50", "Check the following keys: [qgtxtpsnyxjwzvbzhnmrjziscxsmgcugpqanuygve], length needs to be less than 40 characters", "Check the value of the following keys: [ke50], length needs to be less than 500 characters and must be a string"], "subject": ["is too long (maximum is 255 characters)"], "message": ["is too long (maximum is 4000 characters)"]}}}}}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"test_mode": {"type": "boolean", "default": false, "description": "Set to `true` to enable Test Mode. Documents created with Test Mode do not count towards API billing and are not legally binding. Defaults to `false`"}, "name": {"type": "string", "description": "The name of the document."}, "subject": {"type": "string", "description": "Email subject for the signature request that recipients will see. Defaults to the default system subject or a template subject (if the document is created from a template)."}, "message": {"type": "string", "description": "Email message for the signature request that recipients will see. Defaults to the default system message or a template message (if the document is created from a template)."}, "expires_in": {"type": "integer", "minimum": 1, "description": "Number of days before the signature request expires. Defaults to the account expiration setting or template expiration (if the document is created from a template)."}, "reminders": {"type": "boolean", "default": true, "description": "Whether to send signing reminders to recipients. Reminders are sent on day 3, day 6, and day 10 if set to `true`. Defaults to `true`."}, "apply_signing_order": {"type": "boolean", "default": false, "description": "When set to `true` recipients will sign one at a time in the order of the `recipients` collection of this request."}, "api_application_id": {"type": "string", "format": "uuid", "description": "Unique identifier for API Application settings to use. API Applications are optional and mainly used when isolating OAuth apps or for more control over embedded API settings"}, "embedded_signing": {"type": "boolean", "default": false, "description": "When set to `true` it enables embedded signing in your website/web application. Embedded functionality works with an iFrame and email authentication is disabled. :embedded_signinig defaults to `false`."}, "embedded_signing_notifications": {"type": "boolean", "default": false, "description": "On embedding signing, document owners (and CC'd contacts) do not get a notification email when documents have been completed. Setting this param to `true` will send out those final completed notifications. Default is `false`"}, "custom_requester_name": {"type": "string", "description": "Sets the custom requester name for the document. When set, this is the name used for all email communications, signing notifications, and in the audit file."}, "custom_requester_email": {"type": "string", "format": "email", "description": "Sets the custom requester email for the document. When set, this is the email used for all email communications, signing notifications, and in the audit file."}, "redirect_url": {"type": "string", "format": "url", "description": "A URL that recipients are redirected to after successfully signing a document."}, "allow_decline": {"type": "boolean", "default": true, "description": "Whether to allow recipients the option to decline signing a document. If multiple signers are involved in a document, any single recipient can cancel the entire document signing process by declining to sign."}, "allow_reassign": {"type": "boolean", "default": true, "description": "In some cases a signer is not the right person to sign and may need to reassign their signing responsibilities to another person. This feature allows them to reassign the document to someone else."}, "decline_redirect_url": {"type": "string", "format": "url", "description": "A URL that recipients are redirected to if the document is declined."}, "metadata": {"type": "object", "description": "Optional key-value data that can be associated with the document. If set, will be available every time the document data is returned."}, "labels": {"type": "array", "description": "Labels can be used to organize documents in a way that can make it easy to find using the document search in SignWell. A document can have multiple labels. Updating labels on a document will replace any existing labels for that document.", "items": {"type": "object", "description": "Labels can be used to organize documents and templates in a way that can make it easy to find using the document search/template search in SignWell. Labels can be used to organize documents in a way that can make it easy to find using the document search in SignWell.", "properties": {"name": {"type": "string"}}, "required": ["name"]}}, "checkbox_groups": {"type": "array", "description": "Checkbox fields that are placed on a document can be grouped with selection requirements. At least 2 checkbox fields in an array of fields must be assigned to the same recipient.", "items": {"type": "object", "properties": {"group_name": {"type": "string", "description": "A unique identifier for the checkbox group."}, "recipient_id": {"type": "string", "description": "The recipient ID associated with the checkbox group."}, "checkbox_ids": {"type": "array", "items": {"type": "string", "description": "A unique identifier for each checkbox in a group. ID must match the api_id of the checkbox field."}}, "validation": {"type": "string", "enum": ["minimum", "maximum", "range", "exact"], "description": "Set requirements for the group of one or multiple selections by the recipient. Defaults to minimum. Validation values: minimum, maximum, exact, range."}, "required": {"type": "boolean", "default": false, "description": "Whether the group must be completed by the recipient. Defaults to false."}, "min_value": {"type": "integer", "description": "The minimum number of checkboxes that must be checked in the group. (Only for validation: minimum and range)"}, "max_value": {"type": "integer", "description": "The maximum number of checkboxes that can be checked in the group. (Only for validation: maximum and range)"}, "exact_value": {"type": "integer", "description": "The exact number of checkboxes that must be checked in the group. (Only for validation: exact)"}}, "required": ["group_name", "recipient_id", "checkbox_ids"]}}}}}}}}}, "/api/v1/documents/{id}/remind": {"post": {"summary": "Send Reminder", "description": "Sends a reminder email to recipients that have not signed yet.", "tags": ["Document"], "security": [{"api_key": []}], "parameters": [{"name": "id", "in": "path", "schema": {"type": "string", "format": "uuid", "description": "Unique identifier for a document."}, "required": true}], "responses": {"201": {"description": "created", "content": {"application/json": {"example": {"id": "63ff7168-d293-44f5-8ecb-ff005a39c35d", "archived": false, "embedded_edit_url": null, "embedded_preview_url": "https://www.signwell.com/preview/document/63ff7168-d293-44f5-8ecb-ff005a39c35d/", "name": "cutting-authority/consequuntur.docx", "requester_email_address": "<EMAIL>", "status": "<PERSON><PERSON>", "test_mode": false, "created_at": "2024-09-03T12:29:20Z", "updated_at": "2024-09-03T12:29:20Z", "error_message": null, "decline_message": null, "allow_decline": true, "allow_reassign": true, "api_application_id": null, "custom_requester_email": "<EMAIL>", "custom_requester_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "decline_redirect_url": "http://stark-kub.test/khalilah", "embedded_signing": false, "expires_in": 10, "metadata": {"quia": "delectus", "ipsum": "officia"}, "redirect_url": "http://oreilly-graham.test/felicita.anderson", "reminders": true, "apply_signing_order": false, "message": "<p>Hey there,</p><p>&nbsp;</p><p>Please review and complete this document. You can click on the document below to get started.</p>", "subject": "Please complete cutting-authority/consequuntur.docx", "labels": [{"id": "b24633e8-d939-4f93-b8ff-c3739b481b55", "name": "<PERSON><PERSON><PERSON>"}, {"id": "9a41d2f4-eba7-4d6e-804a-7f6094082a51", "name": "<PERSON><PERSON><PERSON>"}], "fields": [[{"api_id": "Signature_13", "height": "32.0", "page": 1, "required": true, "type": "signature", "value": null, "width": "112.0", "x": 1.1, "y": 1.1, "recipient_id": "e69c9ffc-6b9d-451b-b052-d23b61bb371b"}, {"api_id": "CheckBox_14", "height": "13.0", "page": 1, "required": true, "type": "checkbox", "value": null, "width": "13.0", "x": 1.1, "y": 1.1, "name": null, "recipient_id": null, "signing_elements_group_id": "df988908-10e3-495e-ae97-fc8ec9ac1384"}, {"api_id": "CheckBox_15", "height": "13.0", "page": 1, "required": true, "type": "checkbox", "value": null, "width": "13.0", "x": 1.1, "y": 1.1, "name": null, "recipient_id": null, "signing_elements_group_id": "df988908-10e3-495e-ae97-fc8ec9ac1384"}]], "files": [{"name": "impedit.pages", "pages_number": 1}], "copied_contacts": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "recipients": [{"email": "lakesha.<PERSON><PERSON>@oberbrunner.test", "id": "e69c9ffc-6b9d-451b-b052-d23b61bb371b", "message": null, "name": "<PERSON><PERSON>", "passcode": null, "send_email": null, "send_email_delay": null, "status": null, "subject": null, "signing_order": 13, "signing_url": "https://www.signwell.com/docs/c7a74cd583/", "bounced": null, "bounced_details": null, "attachment_requests": [{"name": "twilight_book/aut.pdf", "required": true, "url": "https://www.signwell.com/document_attachments/aBGlRr1kkeCX1amRdGPps7G42Kz/?access=37f9e174-f2af-4282-a2de-d3b4944ca81a"}]}, {"email": "<EMAIL>", "id": "188af56c-a46c-492b-a252-3f6a4951c050", "message": null, "name": "<PERSON><PERSON>", "passcode": null, "send_email": null, "send_email_delay": null, "status": null, "subject": null, "signing_order": 14, "signing_url": "https://www.signwell.com/docs/483f9cbd75/", "bounced": null, "bounced_details": null, "attachment_requests": []}], "checkbox_groups": [{"id": "df988908-10e3-495e-ae97-fc8ec9ac1384", "group_name": null, "recipient_id": null, "checkbox_ids": ["CheckBox_14", "CheckBox_15"], "validation": null, "required": false}]}}}}, "404": {"description": "not found", "content": {"application/json": {"example": {"message": "Not found", "meta": {"error": "record_not_found", "message": "Couldn't find the document requested", "messages": ["Couldn't find the document requested"]}}}}}, "422": {"description": "unprocessable entity", "content": {"application/json": {"example": {"errors": {"document": "The document is in a status that cannot be reminded"}}}}}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"recipients": {"type": "array", "description": "Optional list if recipients within the document to send a reminder email to. If none are specified, all recipients that have not signed yet will receive a reminder email.", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "Recipient's name (required if multiple recipients share the same email)."}, "email": {"type": "string", "format": "email", "description": "Recipient's email address."}}}}}}}}}}}, "/api/v1/documents/{id}/completed_pdf/": {"get": {"summary": "Completed PDF", "description": "Gets a completed document PDF. Supply the unique document ID from either a document creation request or document page URL.", "tags": ["Document"], "security": [{"api_key": []}], "parameters": [{"name": "id", "in": "path", "schema": {"type": "string", "format": "uuid", "description": "Unique identifier for a completed document."}, "required": true}, {"name": "url_only", "in": "query", "schema": {"type": "boolean", "default": false, "description": "Whether to return the URL of the completed PDF or the actual PDF content. Defaults to `false`."}}, {"name": "audit_page", "in": "query", "schema": {"type": "boolean", "default": true, "description": "Whether to include the audit page as part of the document. Defaults to `true`"}}], "responses": {"200": {"description": "successful, returns the final completed PDF, or if url_only is set to true, a JSON object is returned.", "content": {"application/json": {"example": {"file_url": "https://www.signwell.com/signed/***************************.pdf?audit_page=true"}, "schema": {"anyOf": [{"type": "object", "properties": {"file_url": {"type": "string", "format": "url"}}}, {"type": "string", "format": "binary"}]}}}}, "404": {"description": "not_found", "content": {"application/json": {"example": {"message": "Not found", "meta": {"error": "record_not_found", "message": "Couldn't find the document requested", "messages": ["Couldn't find the document requested"]}}}}}}}}, "/api/v1/document_templates/{id}/": {"get": {"summary": "Get Template", "description": "Returns a template and all associated template data. Supply the unique template ID from either a Create Template request or template page URL.", "tags": ["Template"], "security": [{"api_key": []}], "parameters": [{"name": "id", "in": "path", "schema": {"type": "string", "format": "uuid", "description": "Unique identifier for a template."}, "required": true}], "responses": {"200": {"description": "successful", "content": {"application/json": {"example": {"id": "3735dffb-6c0c-4f2f-b431-5e958583532b", "archived": false, "embedded_edit_url": "https://www.signwell.com/edit/template/21484c56-b8b9-4218-9513-319069bec2ab/", "name": "dictate_bless/sit.txt", "requester_email_address": "<EMAIL>", "status": "Available", "created_at": "2024-09-03T12:29:24Z", "updated_at": "2024-09-03T12:29:24Z", "template_link": "https://www.signwell.com/new_doc/KZuZxLJW5o4d8bkn/", "allow_decline": null, "allow_reassign": null, "api_application_id": null, "decline_redirect_url": null, "expires_in": null, "redirect_url": null, "reminders": null, "metadata": null, "apply_signing_order": true, "message": "Optio possimus quas ea.", "subject": "atque", "fields": [[{"api_id": "Signature_28", "height": "32.0", "page": 1, "required": true, "type": "signature", "value": null, "width": "112.0", "x": 1.1, "y": 1.1, "placeholder_name": "Cloak Knight 1"}, {"api_id": "Signature_29", "height": "32.0", "page": 2, "required": true, "type": "signature", "value": null, "width": "112.0", "x": 1.1, "y": 1.1, "placeholder_name": "Junkpile 2"}]], "files": [{"name": "accusamus.jpg", "pages_number": 2}], "copied_placeholders": [{"id": "3", "name": "Penguin 3", "subject": null, "message": null}, {"id": "4", "name": "Dark Flash Ivy 4", "subject": null, "message": null, "preassigned_recipient_name": "Maple Prosacco", "preassigned_recipient_email": "<EMAIL>"}], "placeholders": [{"id": "1", "name": "Cloak Knight 1", "subject": null, "message": null, "preassigned_recipient_name": "Camilla Pfannerstill", "preassigned_recipient_email": "<EMAIL>", "signing_order": 1, "attachment_requests": [{"name": "kidnap_defend/autem.xls", "required": true, "url": "https://www.signwell.com/document_attachments/4rk9yWgbWaCzkJw6PVdmH0dVEr9/?access=ed680cfd-ec11-4b96-8f2f-407453bf9dbd"}]}, {"id": "2", "name": "Junkpile 2", "subject": null, "message": null, "signing_order": 2, "attachment_requests": []}], "checkbox_groups": [{"id": "a507d44e-07f4-417f-83e4-9aed6bd8815e", "group_name": null, "recipient_id": null, "checkbox_ids": [], "validation": null, "required": false}]}}}}, "404": {"description": "not_found", "content": {"application/json": {"example": {"message": "Not found", "meta": {"error": "record_not_found", "message": "Couldn't find the template requested", "messages": ["Couldn't find the template requested"]}}}}}}}, "put": {"summary": "Update Template", "description": "Updates an existing template.", "tags": ["Template"], "security": [{"api_key": []}], "parameters": [{"name": "id", "in": "path", "schema": {"type": "string", "format": "uuid", "description": "Unique identifier for a template."}, "required": true}], "responses": {"200": {"description": "ok", "content": {"application/json": {"example": {"id": "5a98bff5-6651-467c-92eb-f7a81e3fd97e", "archived": false, "embedded_edit_url": "https://www.signwell.com/edit/template/65e56a91-a1db-4070-a52c-60b32fee9832/", "name": "<PERSON><PERSON>", "requester_email_address": "<EMAIL>", "status": "Available", "created_at": "2024-09-03T12:29:26Z", "updated_at": "2024-09-03T12:29:27Z", "template_link": "https://www.signwell.com/new_doc/MN5Z9w9CkXxf2SR7/", "allow_decline": true, "allow_reassign": true, "api_application_id": "e822efd6-7d0d-420c-9ed7-4c3d87b35e43", "decline_redirect_url": "http://lesch.example/robbyn.hirthe", "expires_in": 1, "redirect_url": "http://schiller.test/stan_rohan", "reminders": true, "metadata": {"some": "metadata"}, "apply_signing_order": true, "message": "Quia nobis dolorem et.", "subject": "Consequuntur est ipsum est.", "fields": [[{"api_id": "Signature_36", "height": "32.0", "page": 1, "required": true, "type": "signature", "value": null, "width": "112.0", "x": 1.1, "y": 1.1, "placeholder_name": "Agent Iron Fist Brain 9"}, {"api_id": "Signature_37", "height": "32.0", "page": 2, "required": true, "type": "signature", "value": null, "width": "112.0", "x": 1.1, "y": 1.1, "placeholder_name": "The Ammo Brain 10"}]], "files": [{"name": "non.doc", "pages_number": 2}], "copied_placeholders": [{"id": "3", "name": "Captain <PERSON><PERSON><PERSON> X 11", "subject": null, "message": null}, {"id": "4", "name": "Ammo Claw 12", "subject": null, "message": null, "preassigned_recipient_name": "<PERSON><PERSON>", "preassigned_recipient_email": "<EMAIL>"}], "placeholders": [{"id": "1", "name": "Agent Iron Fist Brain 9", "subject": null, "message": null, "preassigned_recipient_name": "<PERSON>", "preassigned_recipient_email": "<EMAIL>", "signing_order": 1, "attachment_requests": [{"name": "cruelty_stuff/sunt.docx", "required": true, "url": "https://www.signwell.com/document_attachments/dbogn3W1K0HE6kROg0LNf13VLGm/?access=d592faad-f3a9-4520-97a9-b7b478d1cf0a"}]}, {"id": "2", "name": "The Ammo Brain 10", "subject": null, "message": null, "signing_order": 2, "attachment_requests": []}], "checkbox_groups": [{"id": "e3d3b499-692f-4624-8f09-299bd5bd60a5", "group_name": null, "recipient_id": null, "checkbox_ids": [], "validation": null, "required": false}]}}}}, "400": {"description": "bad request", "content": {"application/json": {"example": {"errors": {"message": "The request contains invalid key values.", "invalid_keys": ["name"]}}}}}, "422": {"description": "unprocessable entity", "content": {"application/json": {"example": {"errors": {"decline_redirect_url": ["is not a valid URL"], "redirect_url": ["is not a valid URL"], "expires_in": ["must be less than or equal to 365"], "metadata": ["The key/value pairs must not exceed 50", "Check the following keys: [qgtxtpsnyxjwzvbzhnmrjziscxsmgcugpqanuygve], length needs to be less than 40 characters", "Check the value of the following keys: [ke50], length needs to be less than 500 characters and must be a string"], "api_application_id": ["The provided API application id is invalid"], "message": ["is too long (maximum is 4000 characters)"]}}}}}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string", "description": "The name of the template."}, "subject": {"type": "string", "description": "Email subject for the signature request that recipients will see. Defaults to the default system subject or a template subject (if the document is created from a template)."}, "message": {"type": "string", "description": "Email message for the signature request that recipients will see. Defaults to the default system message or a template message (if the document is created from a template)."}, "draft": {"type": "boolean", "default": false, "description": "Whether the template can still be updated before it is ready for usage. If set to `false` the template is marked as `Available` and it will be ready for use. Defaults to `false`."}, "expires_in": {"type": "integer", "minimum": 1, "description": "Number of days before the signature request expires. Defaults to the account expiration setting or template expiration (if the document is created from a template)."}, "reminders": {"type": "boolean", "default": true, "description": "Whether to send signing reminders to recipients. Reminders are sent on day 3, day 6, and day 10 if set to `true`. Defaults to `true`."}, "apply_signing_order": {"type": "boolean", "default": false, "description": "When set to `true` recipients will sign one at a time in the order of the `recipients` collection of this request."}, "api_application_id": {"type": "string", "format": "uuid", "description": "Unique identifier for API Application settings to use. API Applications are optional and mainly used when isolating OAuth apps or for more control over embedded API settings"}, "redirect_url": {"type": "string", "format": "url", "description": "A URL that recipients are redirected to after successfully signing a document."}, "allow_decline": {"type": "boolean", "default": true, "description": "Whether to allow recipients the option to decline signing a document. If multiple signers are involved in a document, any single recipient can cancel the entire document signing process by declining to sign."}, "allow_reassign": {"type": "boolean", "default": true, "description": "In some cases a signer is not the right person to sign and may need to reassign their signing responsibilities to another person. This feature allows them to reassign the document to someone else."}, "decline_redirect_url": {"type": "string", "format": "url", "description": "A URL that recipients are redirected to if the document is declined."}, "metadata": {"type": "object", "description": "Optional key-value data that can be associated with the document. If set, will be available every time the document data is returned."}, "labels": {"type": "array", "description": "Labels can be used to organize documents in a way that can make it easy to find using the document search in SignWell. A document can have multiple labels. Updating labels on a document will replace any existing labels for that document.", "items": {"type": "object", "description": "Labels can be used to organize documents and templates in a way that can make it easy to find using the document search/template search in SignWell. Labels can be used to organize documents in a way that can make it easy to find using the document search in SignWell.", "properties": {"name": {"type": "string"}}, "required": ["name"]}}, "checkbox_groups": {"type": "array", "description": "Checkbox fields that are placed on a document can be grouped with selection requirements. At least 2 checkbox fields in an array of fields must be assigned to the same recipient.", "items": {"type": "object", "properties": {"group_name": {"type": "string", "description": "A unique identifier for the checkbox group."}, "placeholder_id": {"type": "string", "description": "The recipient ID associated with the checkbox group."}, "checkbox_ids": {"type": "array", "items": {"type": "string", "description": "A unique identifier for each checkbox in a group. ID must match the api_id of the checkbox field."}}, "validation": {"type": "string", "enum": ["minimum", "maximum", "range", "exact"], "description": "Set requirements for the group of one or multiple selections by the recipient. Defaults to minimum. Validation values: minimum, maximum, exact, range."}, "required": {"type": "boolean", "default": false, "description": "Whether the group must be completed by the recipient. Defaults to false."}, "min_value": {"type": "integer", "description": "The minimum number of checkboxes that must be checked in the group. (Only for validation: minimum and range)"}, "max_value": {"type": "integer", "description": "The maximum number of checkboxes that can be checked in the group. (Only for validation: maximum and range)"}, "exact_value": {"type": "integer", "description": "The exact number of checkboxes that must be checked in the group. (Only for validation: exact)"}}, "required": ["group_name", "placeholder_id", "checkbox_ids"]}}}, "required": ["files", "placeholders"]}}}}}, "delete": {"summary": "Delete Template", "description": "Deletes a template. Supply the unique template ID from either a Create Template request or template page URL.", "tags": ["Template"], "security": [{"api_key": []}], "parameters": [{"name": "id", "in": "path", "schema": {"type": "string", "format": "uuid", "description": "Unique identifier for a template."}, "required": true}], "responses": {"204": {"description": "no content"}, "404": {"description": "not found", "content": {"application/json": {"example": {"message": "Not found", "meta": {"error": "record_not_found", "message": "Couldn't find the template requested", "messages": ["Couldn't find the template requested"]}}}}}}}}, "/api/v1/document_templates/": {"post": {"summary": "Create Template", "description": "Creates a new template.", "tags": ["Template"], "security": [{"api_key": []}], "parameters": [], "responses": {"201": {"description": "created", "content": {"application/json": {"example": {"id": "bb6a2577-78d7-4bef-be6c-bb589138334c", "archived": false, "embedded_edit_url": "https://www.signwell.com/edit/template/57309933-83c8-4073-8dea-85cc9787ef0f/", "name": "Agreement", "requester_email_address": "vincent_56_jan_mi<PERSON><PERSON>@dicki.example", "status": "Created", "created_at": "2024-09-03T12:29:25Z", "updated_at": "2024-09-03T12:29:25Z", "template_link": "https://www.signwell.com/new_doc/Ea2GE5qxtoNniUOj/", "allow_decline": true, "allow_reassign": null, "api_application_id": null, "decline_redirect_url": "https://www.domain.com/decline_path/", "expires_in": 365, "redirect_url": "https://www.domain.com/redirect_path/", "reminders": true, "metadata": {"key1": "value1", "key2": "value2"}, "apply_signing_order": false, "message": "Normal Body", "subject": "Normal subject", "labels": [{"id": "f9773575-068a-4ec4-a1e6-b1e1352e92df", "name": "My First Label"}, {"id": "ec856b11-5c18-44b4-a4f6-f81c839c885f", "name": "My Second Label"}, {"id": "fb963586-acc8-42b3-bff1-cf6f68a50231", "name": "My Third Label"}], "fields": [[{"api_id": "DateField_1", "height": "19.0", "page": 1, "required": true, "type": "date", "value": "2021/05/06", "width": "86.0", "x": 230, "y": 60, "date_format": "YYYY/MM/DD", "formula": "", "lock_sign_date": true, "placeholder_name": "Placeholder One"}], [{"api_id": "Signature_2", "height": "32.0", "page": 1, "required": true, "type": "signature", "value": null, "width": "112.0", "x": 260, "y": 80, "placeholder_name": "Placeholder 2"}, {"api_id": "Initials_4", "height": "32.0", "page": 1, "required": true, "type": "initials", "value": null, "width": "44.0", "x": 260, "y": 80, "placeholder_name": "Placeholder 2"}, {"api_id": "TextField_5", "height": "19.0", "page": 1, "required": true, "type": "text", "value": "1", "width": "86.0", "x": 260, "y": 80, "fixed_width": true, "label": "label", "validation": "numbers", "placeholder_name": "Placeholder 2"}, {"api_id": "CheckBox_3", "height": "13.0", "page": 1, "required": false, "type": "checkbox", "value": "t", "width": "13.0", "x": 260, "y": 80, "name": null, "placeholder_name": "Placeholder 2", "signing_elements_group_id": "6dc8ae55-f67c-49b9-8f93-87212ad652ad"}, {"api_id": "CheckBox_4", "height": "13.0", "page": 1, "required": false, "type": "checkbox", "value": "t", "width": "13.0", "x": 270, "y": 80, "name": null, "placeholder_name": "Placeholder 2", "signing_elements_group_id": "6dc8ae55-f67c-49b9-8f93-87212ad652ad"}]], "files": [{"name": "filename1.pdf", "pages_number": 0}, {"name": "filename2.pdf", "pages_number": 0}], "copied_placeholders": [{"id": "1", "name": "Copied Placeholder 1", "subject": null, "message": null}, {"id": "2", "name": "Copied Placeholder 2", "subject": null, "message": null, "preassigned_recipient_name": "Nelson Copied 2", "preassigned_recipient_email": "<EMAIL>"}], "placeholders": [{"id": "placeholder_id_1", "name": "Placeholder One", "subject": null, "message": null, "preassigned_recipient_name": "recipient 1", "preassigned_recipient_email": "<EMAIL>", "signing_order": 1, "attachment_requests": [{"name": "normal name", "required": true, "url": "https://www.signwell.com/document_attachments/mqxLkjVlaKsqNZjN1DXGurLaX7k/?access=8b99f177-03fe-4580-b01f-c3bfcb1bbc92"}]}, {"id": "placeholder_id_2", "name": "Placeholder 2", "subject": null, "message": null, "signing_order": 2, "attachment_requests": [{"name": "Driver License", "required": false, "url": "https://www.signwell.com/document_attachments/e791rygkO0hDVDoZR6Zgfz10OZZ/?access=8b99f177-03fe-4580-b01f-c3bfcb1bbc92"}]}], "checkbox_groups": [{"id": "6dc8ae55-f67c-49b9-8f93-87212ad652ad", "group_name": "Group 1", "recipient_id": null, "checkbox_ids": ["CheckBox_3", "CheckBox_4"], "validation": "minimum", "required": true, "min_value": 1}]}}}}, "400": {"description": "bad request", "content": {"application/json": {"example": {"errors": {"message": "The request contains invalid key values.", "invalid_keys": ["name"]}}}}}, "422": {"description": "unprocessable entity", "content": {"application/json": {"example": {"errors": {"decline_redirect_url": ["is not a valid URL"], "redirect_url": ["is not a valid URL"], "expires_in": ["must be less than or equal to 365"], "metadata": ["The key/value pairs must not exceed 50", "Check the following keys: [qgtxtpsnyxjwzvbzhnmrjziscxsmgcugpqanuygve], length needs to be less than 40 characters", "Check the value of the following keys: [ke50], length needs to be less than 500 characters and must be a string"], "api_application_id": ["The provided API application id is invalid"], "message": ["is too long (maximum is 4000 characters)"], "placeholders": {"duplicated_ids": "These ids are duplicated: placeholder_id_1.", "placeholder_1": {"name": ["is too long (maximum is 255 characters)"], "preassigned_recipient_email": ["format is invalid."], "preassigned_recipient_name": ["is too long (maximum is 255 characters)"]}, "placeholder_2": {"preassigned_recipient_email": ["format is invalid."]}, "all_preassigned": "At least one placeholder should be left unassigned"}, "attachment_requests": {"invalid_ids": "These placeholder ids are not present in the placeholders array: [placeholder_id_3]"}, "fields": {"duplicated_api_ids": "These api ids are duplicated: CheckBox_2.", "invalid_ids": "These placeholder ids are not present in the placeholders array: [placeholder_id_2, placeholder_id_3]", "file_2": {"field_2": {"invalid_date_value": "DateField value must be in Iso8601 format.", "date_format": ["Allowed formats for date fields are: MM/DD/YYYY | DD/MM/YYYY | YYYY/MM/DD | Month DD, YYYY | MM/DD/YYYY hh:mm:ss a"]}}}, "files": {"file_1": {"file_data": ["At least one of file_url or file_base64 should be present"]}, "file_2": {"file_data": ["Only one of file_url or file_base64 should be present, not both"]}, "file_3": {"file_url": ["is not a valid URL"]}, "file_4": {"file_base64": ["the file type is unsupported, we support the following formats: application/msword, application/pdf, application/octet-stream, application/x-ole-storage, application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/vnd.ms-powerpoint, application/vnd.openxmlformats-officedocument.presentationml.presentation, application/vnd.ms-excel, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/x-iwork-keynote-sffkey, application/x-iwork-numbers-sffnumbers, application/x-iwork-pages-sffpages, image/jpeg, image/png, image/tiff, image/webp"]}, "file_5": {"name": ["The file extension is invalid."]}, "file_6": {"name": ["can't be blank"]}}, "checkbox_groups": {"invalid_element_type": "The checkbox group Group 1 contains elements that are not checkboxes: CheckBox_4_test.", "missing_checkbox_ids": "Missing checkbox ids: CheckBox_4_test", "missing_placeholder_id": "Missing placeholder_id in the checkbox group 'Group 1'", "fields_not_in_same_document_or_page": "The fields are not in the same document or page for the checkbox group Group 1", "min_value_negative": "The min value must be greater than or equal to 0 in the checkbox group Group 1"}}}}}}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"files": {"type": "array", "description": "Document files can be uploaded by specifying a file URL or base64 string. Either `file_url` or `file_base64` must be present (not both). Valid file types are: .pdf, .doc, .docx, .pages, .ppt, .pptx, .key, .xls, .xlsx, .numbers, .jpg, .jpeg, .png, .tiff, .tif, and .webp", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the file that will be uploaded."}, "file_url": {"type": "string", "format": "url", "description": "Publicly available URL of the file to be uploaded."}, "file_base64": {"type": "string", "format": "byte", "description": "A RFC 4648 base64 string of the file to be uploaded."}}, "required": ["name"]}}, "name": {"type": "string", "description": "The name of the template."}, "subject": {"type": "string", "description": "Email subject for the signature request that recipients will see. Defaults to the default system subject or a template subject (if the document is created from a template)."}, "message": {"type": "string", "description": "Email message for the signature request that recipients will see. Defaults to the default system message or a template message (if the document is created from a template)."}, "placeholders": {"type": "array", "description": "Placeholders are generally job roles that must complete and/or sign the document. For example, a placeholder might be “Client” or “Legal Department”. When a document is created from the template, you assign a person to each placeholder.", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "A unique identifier that you will give to each placeholder. We recommend numbering sequentially from 1 to X. IDs are required for associating recipients to fields and more."}, "name": {"type": "string", "description": "Name of the placeholder."}, "preassigned_recipient_name": {"type": "string", "description": "In some cases, it may be necessary to pre-fill the name and email for a placeholder because it will always be the same person for all documents created from this template. This sets the name."}, "preassigned_recipient_email": {"type": "string", "format": "email", "description": "In some cases, it may be necessary to pre-fill the name and email for a placeholder because it will always be the same person for all documents created from this template. This sets the email."}}, "required": ["id", "name"]}}, "copied_placeholders": {"type": "array", "description": "Copied placeholders are emailed the final document once it has been completed by all recipients.", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the placeholder."}, "preassigned_recipient_name": {"type": "string", "description": "In some cases, it may be necessary to pre-fill the name and email for a placeholder because it will always be the same person for all documents created from this template. This sets the name."}, "preassigned_recipient_email": {"type": "string", "format": "email", "description": "In some cases, it may be necessary to pre-fill the name and email for a placeholder because it will always be the same person for all documents created from this template. This sets the email."}}, "required": ["name"]}}, "draft": {"type": "boolean", "default": false, "description": "Whether the template can still be updated before it is ready for usage. If set to `false` the template is marked as `Available` and it will be ready for use. Defaults to `false`."}, "expires_in": {"type": "integer", "minimum": 1, "description": "Number of days before the signature request expires. Defaults to the account expiration setting or template expiration (if the document is created from a template)."}, "reminders": {"type": "boolean", "default": true, "description": "Whether to send signing reminders to recipients. Reminders are sent on day 3, day 6, and day 10 if set to `true`. Defaults to `true`."}, "apply_signing_order": {"type": "boolean", "default": false, "description": "When set to `true` recipients will sign one at a time in the order of the `recipients` collection of this request."}, "api_application_id": {"type": "string", "format": "uuid", "description": "Unique identifier for API Application settings to use. API Applications are optional and mainly used when isolating OAuth apps or for more control over embedded API settings"}, "text_tags": {"type": "boolean", "default": false, "description": "An alternative way (if you can’t use the recommended way) of placing fields in specific locations of your document by using special text tags. Useful when changing the content of your files changes the location of fields. See API documentation for “Text Tags” for details. Defaults to false."}, "redirect_url": {"type": "string", "format": "url", "description": "A URL that recipients are redirected to after successfully signing a document."}, "allow_decline": {"type": "boolean", "default": true, "description": "Whether to allow recipients the option to decline signing a document. If multiple signers are involved in a document, any single recipient can cancel the entire document signing process by declining to sign."}, "allow_reassign": {"type": "boolean", "default": true, "description": "In some cases a signer is not the right person to sign and may need to reassign their signing responsibilities to another person. This feature allows them to reassign the document to someone else."}, "decline_redirect_url": {"type": "string", "format": "url", "description": "A URL that recipients are redirected to if the document is declined."}, "metadata": {"type": "object", "description": "Optional key-value data that can be associated with the document. If set, will be available every time the document data is returned."}, "fields": {"type": "array", "description": "Document fields placed on a document for collecting data or signatures from recipients. At least one field must be present in the Create Document request if `draft` is `false` (unless adding a signature page by using `with_signature_page`). Field data should be sent as a 2-dimensional JSON array. One array of fields is needed for each file in the files array. An array of fields can be empty if you have a file that does not contain any fields.", "items": {"type": "array", "description": "Array of Fields you're adding to each file.", "items": {"type": "object", "properties": {"x": {"type": "number", "format": "float", "description": "Horizontal value in the coordinates of the field (in pixels). Coordinates are specific to the page where fields are located."}, "y": {"type": "number", "format": "float", "description": "Vertical value in the coordinates of the field (in pixels). Coordinates are specific to the page where fields are located."}, "page": {"type": "integer", "description": "The page number within the file. If the page does not exist within the file then the field won't be created."}, "placeholder_id": {"type": "string", "description": "Unique identifier of the placeholder assigned to the field."}, "type": {"type": "string", "enum": ["initials", "signature", "checkbox", "date", "text", "autofill_company", "autofill_email", "autofill_first_name", "autofill_last_name", "autofill_name", "autofill_phone", "autofill_title", "autofill_date_signed"], "description": "Field type of the field. Valid field types: initials, signatures, checkbox, date, and text. To autofill fields with contact data, use an autofill field type. To group checkbox fields, enter an api_id for each checkbox and add the checkbox_groups parameter."}, "required": {"type": "boolean", "default": true, "description": "Whether the field must be completed by the recipient. Defaults to `true` except for checkbox type fields."}, "label": {"type": "string", "description": "Text and Date fields only: label that is displayed when the field is empty."}, "value": {"description": "Varies according to the field type. Text fields accept strings or numbers. Date fields accept Iso8601 date strings. CheckBoxes accept booleans. Signature and Initials fields can't be signed through API requests. Autofill text fields accept strings or numbers."}, "api_id": {"type": "string", "description": "Unique identifier of the field. Useful when needing to reference specific field values or update a document and its fields."}, "name": {"type": "string", "description": "Checkbox fields only. At least 2 checkbox fields in an array of fields must be assigned to the same recipient and grouped with selection requirements."}, "validation": {"type": "string", "enum": ["no_text_validation", "numbers", "letters", "email_address", "us_phone_number", "us_zip_code", "us_ssn", "us_age", "alphanumeric", "us_bank_routing_number", "us_bank_account_number"], "description": "Text fields only: optional validation for field values. Valid values: numbers, letters, email_address, us_phone_number, us_zip_code, us_ssn, us_age, alphanumeric, us_bank_routing_number, us_bank_account."}, "fixed_width": {"type": "boolean", "default": false, "description": "Text fields only: whether the field width will stay fixed and text will display in multiple lines, rather than one long line. If set to `false` the field width will automatically grow horizontally to fit text on one line. Defaults to `false`."}, "lock_sign_date": {"type": "boolean", "default": false, "description": "Date fields only: makes fields readonly and automatically populates with the date the recipient signed. Defaults to `false`."}, "date_format": {"type": "string", "enum": ["MM/DD/YYYY", "DD/MM/YYYY", "YYYY/MM/DD", "Month DD, YYYY", "MM/DD/YYYY hh:mm:ss a"], "description": "Date fields only: date format to use for the field. Valid values: MM/DD/YYYY, DD/MM/YYYY, YYYY/MM/DD, Month DD, YYYY, and MM/DD/YYYY hh:mm:ss a. Defaults to MM/DD/YYYY."}}, "required": ["x", "y", "page", "placeholder_id", "type"]}}}, "attachment_requests": {"type": "array", "description": "Attachments that a recipient must upload to complete the signing process. Attachment requests are shown after all document fields have been completed.", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the requested attachment."}, "placeholder_id": {"type": "string", "description": "Unique identifier of the recipient that will view the attachment request."}, "required": {"type": "boolean", "default": true, "description": "Whether the recipient will need to upload the attachment to successfully complete/sign the document. Defaults to `true`."}}, "required": ["name", "placeholder_id"]}}, "labels": {"type": "array", "description": "Labels can be used to organize documents in a way that can make it easy to find using the document search in SignWell. A document can have multiple labels.", "items": {"type": "object", "description": "Labels can be used to organize documents and templates in a way that can make it easy to find using the document search/template search in SignWell. Labels can be used to organize documents in a way that can make it easy to find using the document search in SignWell.", "properties": {"name": {"type": "string"}}, "required": ["name"]}}, "checkbox_groups": {"type": "array", "description": "Checkbox fields that are placed on a document can be grouped with selection requirements. At least 2 checkbox fields in an array of fields must be assigned to the same recipient.", "items": {"type": "object", "properties": {"group_name": {"type": "string", "description": "A unique identifier for the checkbox group."}, "placeholder_id": {"type": "string", "description": "The recipient ID associated with the checkbox group."}, "checkbox_ids": {"type": "array", "items": {"type": "string", "description": "A unique identifier for each checkbox in a group. ID must match the api_id of the checkbox field."}}, "validation": {"type": "string", "enum": ["minimum", "maximum", "range", "exact"], "description": "Set requirements for the group of one or multiple selections by the recipient. Defaults to minimum. Validation values: minimum, maximum, exact, range."}, "required": {"type": "boolean", "default": false, "description": "Whether the group must be completed by the recipient. Defaults to false."}, "min_value": {"type": "integer", "description": "The minimum number of checkboxes that must be checked in the group. (Only for validation: minimum and range)"}, "max_value": {"type": "integer", "description": "The maximum number of checkboxes that can be checked in the group. (Only for validation: maximum and range)"}, "exact_value": {"type": "integer", "description": "The exact number of checkboxes that must be checked in the group. (Only for validation: exact)"}}, "required": ["group_name", "placeholder_id", "checkbox_ids"]}}}, "required": ["files", "placeholders"]}}}}}}, "/api/v1/api_applications/{id}/": {"get": {"summary": "Get API Application", "description": "Gets the details of a specific API Application within an account. Supply the unique Application ID from either the Create API Application response or the API Application edit page.", "tags": ["API Application"], "security": [{"api_key": []}], "parameters": [{"name": "id", "in": "path", "schema": {"type": "string", "format": "uuid", "description": "Unique identifier for the API Application."}, "required": true}], "responses": {"200": {"description": "successful", "content": {"application/json": {"example": {"id": "6ff73b20-4057-4c57-8500-901d32f7da62", "callback_urls": [], "created_at": "2024-09-03T12:29:28Z", "name": "Opela", "updated_at": "2024-09-03T12:29:28Z", "owner": {"id": "baee7e95-c31d-49ec-958c-32017edd38f5", "account_id": "9c89e8d4-b62d-436e-8026-13cca3f13e1f", "name": "Snowball", "email": "<EMAIL>"}, "preferences": {"button_text_color": "6B476B", "buttons_border_radius": 20, "custom_logo_file": "/Users/<USER>/docsketch/tmp/test_files/0/api_application_preferences/000/000/003/original.png", "link_text_color": "461B46", "primary_color": "B3C2C2"}}}}}, "404": {"description": "not_found", "content": {"application/json": {"example": {"message": "Not found", "meta": {"error": "record_not_found", "message": "Couldn't find the api_application requested", "messages": ["Couldn't find the api_application requested"]}}}}}}}, "delete": {"summary": "Delete API Application", "description": "Deletes an API Application from an account. Supply the unique Application ID from either the Create API Application response or the API Application edit page", "tags": ["API Application"], "security": [{"api_key": []}], "parameters": [{"name": "id", "in": "path", "schema": {"type": "string", "format": "uuid", "description": "Unique identifier for the API Application."}, "required": true}], "responses": {"204": {"description": "no content"}, "404": {"description": "not found", "content": {"application/json": {"example": {"message": "Not found", "meta": {"error": "record_not_found", "message": "Couldn't find the api_application requested", "messages": ["Couldn't find the api_application requested"]}}}}}}}}, "/api/v1/hooks/": {"get": {"summary": "List Webhooks", "description": "List all the webhooks in the account.", "tags": ["Webhooks"], "security": [{"api_key": []}], "responses": {"200": {"description": "ok", "content": {"application/json": {"example": [{"id": "14c824df-c679-4450-9b6e-271a61f7e7b7", "callback_url": "http://goyette.example/shelton.bins"}, {"id": "a83b7842-2408-43a6-81ad-************", "callback_url": "http://senger-leusch<PERSON>.test/song"}, {"id": "a539e6e5-2c94-454d-87f2-44bf65c86d96", "callback_url": "http://reichert.example/aron"}, {"id": "183d320d-8c00-45f7-afdf-92d1e4fc47ae", "callback_url": "http://koch-cormier.example/olene_doyle", "api_application_id": "be1f6b4f-7915-4c4d-8f35-77d65beaec2e"}, {"id": "8a56f4a7-8a76-4ae7-8b2a-8ff299359a96", "callback_url": "http://beatty.example/delisa.dietrich", "api_application_id": "be1f6b4f-7915-4c4d-8f35-77d65beaec2e"}, {"id": "b05f58e5-8ee3-498d-a557-98b83d05ce33", "callback_url": "http://wolff.test/normand", "api_application_id": "be1f6b4f-7915-4c4d-8f35-77d65beaec2e"}, {"id": "89a41602-8adb-4dcd-bf5c-9e1c84f8023c", "callback_url": "http://mccullough.example/carmon", "api_application_id": "be1f6b4f-7915-4c4d-8f35-77d65beaec2e"}, {"id": "c45bee87-8dfc-44f8-84fc-3933ab2dec51", "callback_url": "http://windler-romaguera.example/elmo_doyle", "api_application_id": "be1f6b4f-7915-4c4d-8f35-77d65beaec2e"}]}}}}}, "post": {"summary": "Create Webhook", "description": "Register a callback URL that we will post document events to.", "tags": ["Webhooks"], "security": [{"api_key": []}], "parameters": [], "responses": {"201": {"description": "created", "content": {"application/json": {"example": {"id": "7e2b9a67-11dc-4c6d-855e-c049e69e6ff0", "callback_url": "http://gorczany.test/danika.mclaughlin"}}}}, "400": {"description": "bad request", "content": {"application/json": {"example": {"message": "Bad request (bad request data)", "meta": {"error": "invalid_parameter", "message": "Invalid parameter: callback_url must be present", "messages": ["Invalid parameter: callback_url must be present"]}}}}}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"callback_url": {"type": "string", "format": "url", "description": "URL that we will post document events to."}, "api_application_id": {"type": "string", "format": "uuid", "description": "Unique identifier for the API Application."}}, "required": ["callback_url"]}}}}}}, "/api/v1/hooks/{id}/": {"delete": {"summary": "Delete Webhook", "description": "Deletes a registered callback URL that we are posting document events to.", "tags": ["Webhooks"], "security": [{"api_key": []}], "parameters": [{"name": "id", "in": "path", "schema": {"type": "string", "format": "uuid", "description": "Unique identifier for a webhook."}, "required": true}], "responses": {"204": {"description": "no content"}, "404": {"description": "not found", "content": {"application/json": {"example": {"message": "Not found", "meta": {"error": "record_not_found", "message": "Couldn't find the hook requested", "messages": ["Couldn't find the hook requested"]}}}}}}}}, "/api/v1/me/": {"get": {"summary": "Get credentials", "description": "Retrieves the account information associated with the API key being used.", "tags": ["Me"], "security": [{"api_key": []}], "responses": {"200": {"description": "successful", "content": {"application/json": {"example": {"id": "85b28505-6e9a-47fd-ad06-1c79f8966835", "role": "owner", "archived": false, "user": {"id": "4a3e50b2-54c3-489b-97d4-0c1d18a2123c", "name": "King <PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "has_google_registration": false, "first_name": "King"}, "account": {"id": "2f39e945-3288-4032-bc4f-a147a2706b67", "name": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON>", "plan_tier": "free", "active_templates": 0, "can_create_template": true, "can_create_tracking_document": true, "can_create_completion_document": true, "active_users": [{"id": "4a3e50b2-54c3-489b-97d4-0c1d18a2123c", "name": "King <PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "has_google_registration": false}]}, "contact": {"id": "04dd398a-b50f-48bc-9466-3060c59cac4c", "email": "<EMAIL>", "name": "King <PERSON><PERSON><PERSON><PERSON>", "company_name": "Johnston Inc", "phone_number": "************", "alt_phone_number": "(*************", "website": "http://prohaska-bahringer.example/cruz", "initials": "KJ", "archived": false}}}}}, "401": {"description": "unauthorized", "content": {"application/json": {"example": {"message": "Missing or invalid authorization key", "meta": {"error": "api_key_unauthorized_error", "message": "Not valid authorization token", "messages": ["Not valid authorization token"]}}}}}}}}, "/api/v1/bulk_sends/{id}": {"get": {"summary": "Get Bulk Send", "description": "Returns information about the Bulk Send.", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string", "format": "uuid", "description": "Unique identifier for a bulk send."}, "required": true}], "tags": ["Bulk Send"], "security": [{"api_key": []}], "responses": {"200": {"description": "successful", "content": {"application/json": {"example": {"id": "925c86d3-e433-4495-84e5-421dca35d425", "name": "<PERSON><PERSON>", "api_application_id": null, "documents_count": 0, "documents_completed": 0, "documents_not_completed": 0, "created_at": "2024-09-03T12:29:30Z", "user_id": null, "status": "Created", "templates": [{"id": "633d00f5-8479-43a1-a2d0-30a124afcc9c", "name": "offensive-bless/a.xls"}]}}}}, "401": {"description": "unauthorized", "content": {"application/json": {"example": {"message": "Missing or invalid authorization key", "meta": {"error": "api_key_unauthorized_error", "message": "Not valid authorization token", "messages": ["Not valid authorization token"]}}}}}, "404": {"description": "not found", "content": {"application/json": {"example": {"message": "Not found", "meta": {"error": "record_not_found", "message": "Couldn't find the bulk_send requested", "messages": ["Couldn't find the bulk_send requested"]}}}}}}}}, "/api/v1/bulk_sends": {"get": {"summary": "List Bulk Sendings", "description": "Returns information about the Bulk Send.", "tags": ["Bulk Send"], "security": [{"api_key": []}], "parameters": [{"name": "user_email", "in": "query", "required": false, "schema": {"type": "string", "format": "email", "description": "The email address of the user that sent the Bulk Send. Must have the `admin` or `manager` role to view Bulk Sends of other users. Defaults to the user that the API key belongs to."}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 50, "default": 10, "description": "The number of documents to fetch. Defaults to 10, max is 50."}}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "description": "The page number for pagination. Defaults to the first page."}}, {"name": "api_application_id", "in": "query", "required": false, "schema": {"type": "string", "format": "uuid", "description": "Unique identifier for API Application settings to use. API Applications are optional and mainly used when isolating OAuth apps or for more control over embedded API settings"}}], "responses": {"200": {"description": "successful", "content": {"application/json": {"example": {"bulk_sends": [{"id": "194a5002-5eb1-46e3-b385-7b268b4ca0f6", "name": "<PERSON><PERSON>", "api_application_id": null, "documents_count": 0, "documents_completed": 0, "documents_not_completed": 0, "created_at": "2024-09-03T12:29:31Z", "user_id": null, "status": "Created", "template_ids": ["5cefc2e4-9d9f-485a-8317-9c3636338a1d"]}, {"id": "d57805e6-33d4-4d44-b8e9-0970d08ccf57", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_application_id": null, "documents_count": 0, "documents_completed": 0, "documents_not_completed": 0, "created_at": "2024-09-03T12:29:31Z", "user_id": null, "status": "Created", "template_ids": ["5cefc2e4-9d9f-485a-8317-9c3636338a1d"]}, {"id": "f576eb73-701e-4419-985f-7d4008b68078", "name": "Bridge of Khazad-dûm", "api_application_id": null, "documents_count": 0, "documents_completed": 0, "documents_not_completed": 0, "created_at": "2024-09-03T12:29:31Z", "user_id": null, "status": "Created", "template_ids": ["5cefc2e4-9d9f-485a-8317-9c3636338a1d"]}, {"id": "41eb4321-1237-495c-833b-c8ed6b962e0c", "name": "Bag End", "api_application_id": null, "documents_count": 0, "documents_completed": 0, "documents_not_completed": 0, "created_at": "2024-09-03T12:29:31Z", "user_id": null, "status": "Created", "template_ids": ["5cefc2e4-9d9f-485a-8317-9c3636338a1d"]}, {"id": "6c819d98-9069-4dab-8a82-160f1e5fc49e", "name": "<PERSON><PERSON>'s Deep", "api_application_id": null, "documents_count": 0, "documents_completed": 0, "documents_not_completed": 0, "created_at": "2024-09-03T12:29:31Z", "user_id": null, "status": "Created", "template_ids": ["5cefc2e4-9d9f-485a-8317-9c3636338a1d"]}], "current_page": 1, "next_page": null, "previous_page": null, "total_count": 5, "total_pages": 1}}}}, "401": {"description": "unauthorized", "content": {"application/json": {"example": {"message": "Missing or invalid authorization key", "meta": {"error": "api_key_unauthorized_error", "message": "Not valid authorization token", "messages": ["Not valid authorization token"]}}}}}}}, "post": {"summary": "Create Bulk Send", "description": "Creates a bulk send, and it validates the CSV file before creating the bulk send.", "tags": ["Bulk Send"], "security": [{"api_key": []}], "parameters": [], "responses": {"201": {"description": "successful", "content": {"application/json": {"example": {"id": "61d77496-d714-4a29-b55f-423ff9dad9b2", "template_ids": ["f5f90172-d674-4b69-bad7-1e28dab7d097"], "api_application_id": "070e9170-0f94-4cf0-9a02-edf4cdd87d14", "documents_count": 0, "created_at": "2024-09-03T12:29:32Z", "user_id": "ea6c6325-80e9-4580-afd6-b2482d70e58a", "status": "Enqueuing"}}}}, "401": {"description": "unauthorized", "content": {"application/json": {"example": {"message": "Missing or invalid authorization key", "meta": {"error": "api_key_unauthorized_error", "message": "Not valid authorization token", "messages": ["Not valid authorization token"]}}}}}, "422": {"description": "unprocessable entity", "content": {"application/json": {"example": {"errors": {"bulk_send_csv": [{"row": 2, "data": {"document_sender_email": "not an email", "document_sender_name": "<EMAIL>", "document_sender_label": "Label", "recipient_email": "<EMAIL>", "recipient_name": "Recipient", "recipient_label": "Recipient"}, "errors": ["The following columns have an email address invalid: document_sender_email."]}]}}}}}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"template_ids": {"type": "array", "items": {"type": "string", "format": "uuid"}, "description": "Unique identifiers for a list of templates."}, "bulk_send_csv": {"type": "string", "format": "byte", "description": "A RFC 4648 base64 string of the template CSV file to be validated."}, "skip_row_errors": {"type": "boolean", "default": false, "description": "Whether to skip errors in the rows. Defaults to `false`."}, "api_application_id": {"type": "string", "format": "uuid", "description": "Unique identifier for API Application settings to use. API Applications are optional and mainly used when isolating OAuth apps or for more control over embedded API settings"}, "name": {"type": "string", "description": "The name of the Bulk Send. Will be used as the document name for each of the documents."}, "subject": {"type": "string", "description": "Email subject for the signature request that recipients will see. Defaults to the default system subject or a template subject."}, "message": {"type": "string", "description": "Email message for the signature request that recipients will see. Defaults to the default system message or a template message."}, "apply_signing_order": {"type": "boolean", "default": false, "description": "When set to `true` recipients will sign one at a time in the order of the `recipients` collection of this request."}, "custom_requester_name": {"type": "string", "description": "Sets the custom requester name for the document. When set, this is the name used for all email communications, signing notifications, and in the audit file."}, "custom_requester_email": {"type": "string", "format": "email", "description": "Sets the custom requester email for the document. When set, this is the email used for all email communications, signing notifications, and in the audit file."}}, "required": ["template_ids", "bulk_send_csv"]}}}}}}, "/api/v1/bulk_sends/csv_template": {"get": {"summary": "Get Bulk Send CSV Template", "description": "Fetches a CSV template that corresponds to the provided document template IDs. CSV templates are blank CSV files that have columns containing required and optional data that can be sent when creating a bulk send. Fields can be referenced by the field label. Example: [placeholder name]_[field label] could be something like customer_address or signer_company_name (if \"Customer\" and \"Signer\" were placeholder names for templates set up in SignWell).", "parameters": [{"name": "template_ids[]", "in": "query", "required": true, "schema": {"type": "array", "description": "Specify one or more templates to generate a single blank CSV file that will contain available columns for your recipient data. The template_ids[] parameter is an array of template IDs (e.g.,`/?template_ids[]=5a67dbd7-928a-4ea0-a7e2-e476a0eb045f&template_ids[]=d7315111-c671-4b15-8354-c9a19bbaefa0`). Each ID should be a separate parameter in the query string.", "items": {"type": "string", "format": "uuid"}}}, {"name": "base64", "in": "query", "schema": {"type": "string", "format": "byte", "description": "A RFC 4648 base64 string of the template CSV file to be validated."}}], "tags": ["Bulk Send"], "security": [{"api_key": []}], "responses": {"200": {"description": "successful", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}, "example": "document_sender_email,document_sender_name\n<EMAIL>,King <PERSON><PERSON><PERSON>an\n"}, "application/json": {"example": {"data": "********************************************************************************************************************************************"}}}}, "401": {"description": "unauthorized", "content": {"application/json": {"example": {"message": "Missing or invalid authorization key", "meta": {"error": "api_key_unauthorized_error", "message": "Not valid authorization token", "messages": ["Not valid authorization token"]}}}}}, "404": {"description": "not found", "content": {"application/json": {"example": {"message": "Not found", "meta": {"error": "record_not_found", "message": "Couldn't find the template requested", "messages": ["Couldn't find the template requested"]}}}}}}}}, "/api/v1/bulk_sends/validate_csv": {"post": {"summary": "Validate Bulk Send CSV", "description": "Validates a Bulk Send CSV file before creating the Bulk Send. It will check the structure of the CSV and the data it contains, and return any errors found.", "parameters": [], "tags": ["Bulk Send"], "security": [{"api_key": []}], "responses": {"200": {"description": "successful", "content": {"application/json": {"example": {"bulk_send_csv": "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "template_ids": ["923dbf20-5323-4d11-8f7d-0720deeb26e8"], "skip_row_errors": false, "api_application_id": null, "name": "freckle-arena/dolor.flac", "subject": "Please complete freckle-arena/dolor.flac", "message": "<p>Hey there,</p><p>&nbsp;</p><p>Please review and complete this document. You can click on the document below to get started.</p>", "apply_signing_order": false}}}}, "401": {"description": "unauthorized", "content": {"application/json": {"example": {"message": "Missing or invalid authorization key", "meta": {"error": "api_key_unauthorized_error", "message": "Not valid authorization token", "messages": ["Not valid authorization token"]}}}}}, "422": {"description": "unprocessable entity", "content": {"application/json": {"example": {"errors": {"bulk_send_csv": [{"row": 2, "data": {"document_sender_email": "not an email", "document_sender_name": "<EMAIL>", "document_sender_label": "Label", "recipient_email": "<EMAIL>", "recipient_name": "Recipient", "recipient_label": "Recipient"}, "errors": ["The following columns have an email address invalid: document_sender_email."]}]}}}}}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"template_ids": {"type": "array", "description": "Specify one or more templates to generate a single blank CSV file that will contain available columns for your recipient data. The template_ids[] parameter is an array of template IDs (e.g.,`/?template_ids[]=5a67dbd7-928a-4ea0-a7e2-e476a0eb045f&template_ids[]=d7315111-c671-4b15-8354-c9a19bbaefa0`). Each ID should be a separate parameter in the query string.", "items": {"type": "string", "format": "uuid"}}, "bulk_send_csv": {"type": "string", "format": "byte", "description": "A RFC 4648 base64 string of the template CSV file to be validated."}}, "required": ["template_ids", "bulk_send_csv"]}}}}}}, "/api/v1/bulk_sends/{id}/documents": {"get": {"summary": "Get Bulk Send Documents", "description": "Returns information about the Bulk Send.", "tags": ["Bulk Send"], "security": [{"api_key": []}], "parameters": [{"name": "id", "in": "path", "schema": {"type": "string", "format": "uuid", "description": "Unique identifier for a bulk send."}, "required": true}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 50, "default": 10, "description": "The number of documents to fetch. Defaults to 10, max is 50."}}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "description": "The page number for pagination. Defaults to the first page."}}], "responses": {"200": {"description": "successful", "content": {"application/json": {"example": {"id": "63480f3f-9222-4ad6-bf20-1c56623969c9", "name": "Houses of Healing", "api_application_id": null, "created_at": "2024-09-03T12:29:35Z", "user_id": null, "status": "Created", "documents": [{"id": "d65c2193-591a-4eaf-9e06-3c801e4b07bd", "archived": false, "embedded_edit_url": "https://www.signwell.com/edit/document/de033061-2502-4e1f-a12e-f8f0d22ee50f/", "embedded_preview_url": null, "name": "offensive-galaxy/placeat.css", "requester_email_address": "<EMAIL>", "status": "Created", "test_mode": false, "created_at": "2024-09-03T12:29:35Z", "updated_at": "2024-09-03T12:29:35Z", "error_message": null, "decline_message": null, "allow_decline": true, "allow_reassign": true, "api_application_id": null, "custom_requester_email": "micha<PERSON>@haley-zulauf.example", "custom_requester_name": "<PERSON><PERSON>", "decline_redirect_url": "http://mante.test/randall_hoeger", "embedded_signing": false, "expires_in": 10, "metadata": {"est": "et", "illo": "quaerat"}, "redirect_url": "http://champlin.example/pablo", "reminders": true, "apply_signing_order": false, "message": "<p>Hey there,</p><p>&nbsp;</p><p>Please review and complete this document. You can click on the document below to get started.</p>", "subject": "Please complete offensive-galaxy/placeat.css", "labels": [{"id": "998b68f3-8fc8-45c0-99be-1e846766fe0d", "name": "<PERSON><PERSON>"}, {"id": "e5740d36-d105-4ad4-a2f6-c47b6b71594d", "name": "<PERSON><PERSON><PERSON>"}], "fields": [[{"api_id": "Signature_51", "height": "32.0", "page": 1, "required": true, "type": "signature", "value": null, "width": "112.0", "x": 1.1, "y": 1.1, "recipient_id": "f60dd990-38a1-4160-a91c-3726d29d12b6"}, {"api_id": "CheckBox_52", "height": "13.0", "page": 1, "required": true, "type": "checkbox", "value": null, "width": "13.0", "x": 1.1, "y": 1.1, "name": null, "recipient_id": null, "signing_elements_group_id": "3ba28c83-a9b9-474d-bae2-cab5ecf737fa"}, {"api_id": "CheckBox_53", "height": "13.0", "page": 1, "required": true, "type": "checkbox", "value": null, "width": "13.0", "x": 1.1, "y": 1.1, "name": null, "recipient_id": null, "signing_elements_group_id": "3ba28c83-a9b9-474d-bae2-cab5ecf737fa"}]], "files": [{"name": "sunt.odp", "pages_number": 1}], "copied_contacts": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "recipients": [{"email": "<EMAIL>", "id": "f60dd990-38a1-4160-a91c-3726d29d12b6", "message": null, "name": "Dr. <PERSON>", "passcode": null, "send_email": null, "send_email_delay": null, "status": "created", "subject": null, "signing_order": 31, "signing_url": "https://www.signwell.com/docs/41f626d75f/", "bounced": null, "bounced_details": null, "attachment_requests": [{"name": "academy-corn/itaque.jpeg", "required": true, "url": "https://www.signwell.com/document_attachments/bklkXrXrmOI68gDpjN8khK9DX4D/?access=62cd0a55-5aa4-498c-a552-2cc618ffcb72"}]}, {"email": "ernes<PERSON><EMAIL>", "id": "da387bdd-f2b4-4574-872a-670019eb6077", "message": null, "name": "<PERSON><PERSON>", "passcode": null, "send_email": null, "send_email_delay": null, "status": "created", "subject": null, "signing_order": 32, "signing_url": "https://www.signwell.com/docs/704202841b/", "bounced": null, "bounced_details": null, "attachment_requests": []}], "checkbox_groups": [{"id": "3ba28c83-a9b9-474d-bae2-cab5ecf737fa", "group_name": null, "recipient_id": null, "checkbox_ids": ["CheckBox_52", "CheckBox_53"], "validation": null, "required": false}]}], "current_page": 1, "next_page": null, "previous_page": null, "total_count": 1, "total_pages": 1}}}}, "401": {"description": "unauthorized", "content": {"application/json": {"example": {"message": "Missing or invalid authorization key", "meta": {"error": "api_key_unauthorized_error", "message": "Not valid authorization token", "messages": ["Not valid authorization token"]}}}}}, "404": {"description": "not found", "content": {"application/json": {"example": {"message": "Not found", "meta": {"error": "record_not_found", "message": "Couldn't find the bulk_send requested", "messages": ["Couldn't find the bulk_send requested"]}}}}}}}}}, "components": {"securitySchemes": {"api_key": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "X-Api-Key", "in": "header"}}, "schemas": {"additional_fields_map": {"type": "array", "description": "Fields to be added to any appended files (not existing files). Document fields placed on a document for collecting data or signatures from recipients. Field data should be sent as a 2-dimensional JSON array. One array of fields is needed for each file in the files array. An array of fields can be empty if you have a file that does not contain any fields.", "items": {"type": "array", "description": "Array of Fields you're adding to each file.", "items": {"type": "object", "properties": {"x": {"type": "number", "format": "float", "description": "Horizontal value in the coordinates of the field (in pixels). Coordinates are specific to the page where fields are located."}, "y": {"type": "number", "format": "float", "description": "Vertical value in the coordinates of the field (in pixels). Coordinates are specific to the page where fields are located."}, "page": {"type": "integer", "description": "The page number within the file. If the page does not exist within the file then the field won't be created."}, "recipient_id": {"type": "string", "description": "Unique identifier of the recipient assigned to the field. Recipients assigned to fields will be the only ones that will see and be able to complete those fields."}, "type": {"type": "string", "enum": ["initials", "signature", "checkbox", "date", "text", "autofill_company", "autofill_email", "autofill_first_name", "autofill_last_name", "autofill_name", "autofill_phone", "autofill_title", "autofill_date_signed"], "description": "Field type of the field. Valid field types: initials, signatures, checkbox, date, and text. To autofill fields with contact data, use an autofill field type. To group checkbox fields, enter an api_id for each checkbox and add the checkbox_groups parameter."}, "required": {"type": "boolean", "default": true, "description": "Whether the field must be completed by the recipient. Defaults to `true` except for checkbox type fields."}, "label": {"type": "string", "description": "Text and Date fields only: label that is displayed when the field is empty."}, "value": {"description": "Varies according to the field type. Text fields accept strings or numbers. Date fields accept Iso8601 date strings. CheckBoxes accept booleans. Signature and Initials fields can't be signed through API requests. Autofill text fields accept strings or numbers."}, "api_id": {"type": "string", "description": "Unique identifier of the field. Useful when needing to reference specific field values or update a document and its fields."}, "name": {"type": "string", "description": "Checkbox fields only. At least 2 checkbox fields in an array of fields must be assigned to the same recipient and grouped with selection requirements."}, "validation": {"type": "string", "enum": ["no_text_validation", "numbers", "letters", "email_address", "us_phone_number", "us_zip_code", "us_ssn", "us_age", "alphanumeric", "us_bank_routing_number", "us_bank_account_number"], "description": "Text fields only: optional validation for field values. Valid values: numbers, letters, email_address, us_phone_number, us_zip_code, us_ssn, us_age, alphanumeric, us_bank_routing_number, us_bank_account."}, "fixed_width": {"type": "boolean", "default": false, "description": "Text fields only: whether the field width will stay fixed and text will display in multiple lines, rather than one long line. If set to `false` the field width will automatically grow horizontally to fit text on one line. Defaults to `false`."}, "lock_sign_date": {"type": "boolean", "default": false, "description": "Date fields only: makes fields readonly and automatically populates with the date the recipient signed. Defaults to `false`."}, "date_format": {"type": "string", "enum": ["MM/DD/YYYY", "DD/MM/YYYY", "YYYY/MM/DD", "Month DD, YYYY", "MM/DD/YYYY hh:mm:ss a"], "description": "Date fields only: date format to use for the field. Valid values: MM/DD/YYYY, DD/MM/YYYY, YYYY/MM/DD, Month DD, YYYY, and MM/DD/YYYY hh:mm:ss a. Defaults to MM/DD/YYYY."}}, "required": ["x", "y", "page", "recipient_id", "type"]}}}, "additional_files_map": {"type": "array", "items": {"type": "object", "description": "Additional files to be appended to the document. Will not replace existing files from the template. Document files can be uploaded by specifying a file URL or base64 string. Either `file_url` or `file_base64` must be present (not both). Valid file types are: .pdf, .docx, .jpg, .png, .ppt, .xls, .pages, and .txt.", "properties": {"name": {"type": "string", "description": "Name of the file that will be uploaded."}, "file_url": {"type": "string", "format": "url", "description": "Publicly available URL of the file to be uploaded."}, "file_base64": {"type": "string", "format": "byte", "description": "A RFC 4648 base64 string of the file to be uploaded."}}, "required": ["name"]}}, "allow_decline_map": {"type": "boolean", "default": true, "description": "Whether to allow recipients the option to decline signing a document. If multiple signers are involved in a document, any single recipient can cancel the entire document signing process by declining to sign."}, "allow_reassign_map": {"type": "boolean", "default": true, "description": "In some cases a signer is not the right person to sign and may need to reassign their signing responsibilities to another person. This feature allows them to reassign the document to someone else."}, "api_application_id_map": {"type": "string", "format": "uuid", "description": "Unique identifier for API Application settings to use. API Applications are optional and mainly used when isolating OAuth apps or for more control over embedded API settings"}, "apply_sending_order_map": {"type": "boolean", "default": false, "description": "When set to `true` recipients will sign one at a time in the order of the `recipients` collection of this request."}, "attachment_requests_for_template_map": {"type": "array", "description": "Attachments that a recipient must upload to complete the signing process. Attachment requests are shown after all document fields have been completed.", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the requested attachment."}, "placeholder_id": {"type": "string", "description": "Unique identifier of the recipient that will view the attachment request."}, "required": {"type": "boolean", "default": true, "description": "Whether the recipient will need to upload the attachment to successfully complete/sign the document. Defaults to `true`."}}, "required": ["name", "placeholder_id"]}}, "attachment_requests_map": {"type": "array", "description": "Attachments that a recipient must upload to complete the signing process. Attachment requests are shown after all document fields have been completed.", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the requested attachment."}, "recipient_id": {"type": "string", "description": "Unique identifier of the recipient that will view the attachment request."}, "required": {"type": "boolean", "default": true, "description": "Whether the recipient will need to upload the attachment to successfully complete/sign the document. Defaults to `true`."}}, "required": ["name", "recipient_id"]}}, "checkbox_groups_map_for_template": {"type": "array", "description": "Checkbox fields that are placed on a document can be grouped with selection requirements. At least 2 checkbox fields in an array of fields must be assigned to the same recipient.", "items": {"type": "object", "properties": {"group_name": {"type": "string", "description": "A unique identifier for the checkbox group."}, "placeholder_id": {"type": "string", "description": "The recipient ID associated with the checkbox group."}, "checkbox_ids": {"type": "array", "items": {"type": "string", "description": "A unique identifier for each checkbox in a group. ID must match the api_id of the checkbox field."}}, "validation": {"type": "string", "enum": ["minimum", "maximum", "range", "exact"], "description": "Set requirements for the group of one or multiple selections by the recipient. Defaults to minimum. Validation values: minimum, maximum, exact, range."}, "required": {"type": "boolean", "default": false, "description": "Whether the group must be completed by the recipient. Defaults to false."}, "min_value": {"type": "integer", "description": "The minimum number of checkboxes that must be checked in the group. (Only for validation: minimum and range)"}, "max_value": {"type": "integer", "description": "The maximum number of checkboxes that can be checked in the group. (Only for validation: maximum and range)"}, "exact_value": {"type": "integer", "description": "The exact number of checkboxes that must be checked in the group. (Only for validation: exact)"}}, "required": ["group_name", "placeholder_id", "checkbox_ids"]}}, "checkbox_groups_map_for_document": {"type": "array", "description": "Checkbox fields that are placed on a document can be grouped with selection requirements. At least 2 checkbox fields in an array of fields must be assigned to the same recipient.", "items": {"type": "object", "properties": {"group_name": {"type": "string", "description": "A unique identifier for the checkbox group."}, "recipient_id": {"type": "string", "description": "The recipient ID associated with the checkbox group."}, "checkbox_ids": {"type": "array", "items": {"type": "string", "description": "A unique identifier for each checkbox in a group. ID must match the api_id of the checkbox field."}}, "validation": {"type": "string", "enum": ["minimum", "maximum", "range", "exact"], "description": "Set requirements for the group of one or multiple selections by the recipient. Defaults to minimum. Validation values: minimum, maximum, exact, range."}, "required": {"type": "boolean", "default": false, "description": "Whether the group must be completed by the recipient. Defaults to false."}, "min_value": {"type": "integer", "description": "The minimum number of checkboxes that must be checked in the group. (Only for validation: minimum and range)"}, "max_value": {"type": "integer", "description": "The maximum number of checkboxes that can be checked in the group. (Only for validation: maximum and range)"}, "exact_value": {"type": "integer", "description": "The exact number of checkboxes that must be checked in the group. (Only for validation: exact)"}}, "required": ["group_name", "recipient_id", "checkbox_ids"]}}, "bulk_send_base64_param_map": {"type": "boolean", "default": false, "description": "Whether to return a base64 string containing the template file. <PERSON><PERSON><PERSON> is `false` and returns the CSV template file."}, "bulk_send_csv_map": {"type": "string", "format": "byte", "description": "A RFC 4648 base64 string of the template CSV file to be validated."}, "bulk_send_id_param_map": {"type": "string", "format": "uuid", "description": "Unique identifier for a bulk send."}, "bulk_send_message_map": {"type": "string", "description": "Email message for the signature request that recipients will see. Defaults to the default system message or a template message."}, "bulk_send_name_map": {"type": "string", "description": "The name of the Bulk Send. Will be used as the document name for each of the documents."}, "bulk_send_subject_map": {"type": "string", "description": "Email subject for the signature request that recipients will see. Defaults to the default system subject or a template subject."}, "bulk_send_template_ids_param_map": {"type": "array", "description": "Specify one or more templates to generate a single blank CSV file that will contain available columns for your recipient data. The template_ids[] parameter is an array of template IDs (e.g.,`/?template_ids[]=5a67dbd7-928a-4ea0-a7e2-e476a0eb045f&template_ids[]=d7315111-c671-4b15-8354-c9a19bbaefa0`). Each ID should be a separate parameter in the query string.", "items": {"type": "string", "format": "uuid"}}, "copied_contacts_map": {"type": "array", "description": "Copied contacts are emailed the final document once it has been completed by all recipients.", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the copied contact."}, "email": {"type": "string", "format": "email", "description": "Email for the copied contact."}}, "required": ["email"]}}, "copied_placeholders_map": {"type": "array", "description": "Copied placeholders are emailed the final document once it has been completed by all recipients.", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the placeholder."}, "preassigned_recipient_name": {"type": "string", "description": "In some cases, it may be necessary to pre-fill the name and email for a placeholder because it will always be the same person for all documents created from this template. This sets the name."}, "preassigned_recipient_email": {"type": "string", "format": "email", "description": "In some cases, it may be necessary to pre-fill the name and email for a placeholder because it will always be the same person for all documents created from this template. This sets the email."}}, "required": ["name"]}}, "custom_requester_name_map": {"type": "string", "description": "Sets the custom requester name for the document. When set, this is the name used for all email communications, signing notifications, and in the audit file."}, "custom_requester_email_map": {"type": "string", "format": "email", "description": "Sets the custom requester email for the document. When set, this is the email used for all email communications, signing notifications, and in the audit file."}, "decline_redirect_url_map": {"type": "string", "format": "url", "description": "A URL that recipients are redirected to if the document is declined."}, "document_completed_id_map": {"type": "string", "format": "uuid", "description": "Unique identifier for a completed document."}, "document_id_param_map": {"type": "string", "format": "uuid", "description": "Unique identifier for a document."}, "document_name_map": {"type": "string", "description": "The name of the document."}, "document_recipients_map": {"type": "array", "description": "Document recipients are people that must complete and/or sign a document.", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "A unique identifier that you will give to each recipient. We recommend numbering sequentially from 1 to X. IDs are required for associating recipients to fields and more."}, "name": {"type": "string", "description": "Name of the recipient."}, "email": {"type": "string", "format": "email", "description": "Email address for the recipient."}, "passcode": {"type": "string", "description": "If set, signers assigned with a passcode will be required to enter the passcode before they’re able to view and complete the document."}, "subject": {"type": "string", "description": "Email subject for the signature request that the recipient will see. Overrides the general subject for the document."}, "message": {"type": "string", "description": "Email message for the signature request that the recipient will see. Overrides the general message for the document."}, "send_email": {"type": "boolean", "default": false, "description": "Applies on when `embedded_signing` is `true`. By default, recipients are not notified through email to sign when doing embedded signing. Setting this to `true`  will send a notification email to the recipient. Default is `false`."}, "send_email_delay": {"type": "integer", "default": 0, "description": "If `send_email` is `true` recipients will receive a new document notification immediately. In the case of embedded signing, you can delay this notification to only send if the document is not completed within a few minutes. The email notification will not go out if the document is completed before the delay time is over. Valid values are in minutes ranging from `0` to `60`. Defaults to `0`."}}, "required": ["id", "email"]}}, "draft_map": {"type": "boolean", "default": false, "description": "Whether the document can still be updated before sending a signature request. If set to `false` the document is sent for signing as part of this request. Defaults to `false`."}, "template_draft_map": {"type": "boolean", "default": false, "description": "Whether the template can still be updated before it is ready for usage. If set to `false` the template is marked as `Available` and it will be ready for use. Defaults to `false`."}, "embedded_signing_map": {"type": "boolean", "default": false, "description": "When set to `true` it enables embedded signing in your website/web application. Embedded functionality works with an iFrame and email authentication is disabled. :embedded_signinig defaults to `false`."}, "embedded_signing_notifications_map": {"type": "boolean", "default": false, "description": "On embedding signing, document owners (and CC'd contacts) do not get a notification email when documents have been completed. Setting this param to `true` will send out those final completed notifications. Default is `false`"}, "expires_in_map": {"type": "integer", "minimum": 1, "description": "Number of days before the signature request expires. Defaults to the account expiration setting or template expiration (if the document is created from a template)."}, "fields_map": {"type": "array", "description": "Document fields placed on a document for collecting data or signatures from recipients. At least one field must be present in the Create Document request if `draft` is `false` (unless adding a signature page by using `with_signature_page`). Field data should be sent as a 2-dimensional JSON array. One array of fields is needed for each file in the files array. An array of fields can be empty if you have a file that does not contain any fields.", "items": {"type": "array", "description": "Array of Fields you're adding to each file.", "items": {"type": "object", "properties": {"x": {"type": "number", "format": "float", "description": "Horizontal value in the coordinates of the field (in pixels). Coordinates are specific to the page where fields are located."}, "y": {"type": "number", "format": "float", "description": "Vertical value in the coordinates of the field (in pixels). Coordinates are specific to the page where fields are located."}, "page": {"type": "integer", "description": "The page number within the file. If the page does not exist within the file then the field won't be created."}, "recipient_id": {"type": "string", "description": "Unique identifier of the recipient assigned to the field. Recipients assigned to fields will be the only ones that will see and be able to complete those fields."}, "type": {"type": "string", "enum": ["initials", "signature", "checkbox", "date", "text", "autofill_company", "autofill_email", "autofill_first_name", "autofill_last_name", "autofill_name", "autofill_phone", "autofill_title", "autofill_date_signed"], "description": "Field type of the field. Valid field types: initials, signatures, checkbox, date, and text. To autofill fields with contact data, use an autofill field type. To group checkbox fields, enter an api_id for each checkbox and add the checkbox_groups parameter."}, "required": {"type": "boolean", "default": true, "description": "Whether the field must be completed by the recipient. Defaults to `true` except for checkbox type fields."}, "label": {"type": "string", "description": "Text and Date fields only: label that is displayed when the field is empty."}, "value": {"description": "Varies according to the field type. Text fields accept strings or numbers. Date fields accept Iso8601 date strings. CheckBoxes accept booleans. Signature and Initials fields can't be signed through API requests. Autofill text fields accept strings or numbers."}, "api_id": {"type": "string", "description": "Unique identifier of the field. Useful when needing to reference specific field values or update a document and its fields."}, "name": {"type": "string", "description": "Checkbox fields only. At least 2 checkbox fields in an array of fields must be assigned to the same recipient and grouped with selection requirements."}, "validation": {"type": "string", "enum": ["no_text_validation", "numbers", "letters", "email_address", "us_phone_number", "us_zip_code", "us_ssn", "us_age", "alphanumeric", "us_bank_routing_number", "us_bank_account_number"], "description": "Text fields only: optional validation for field values. Valid values: numbers, letters, email_address, us_phone_number, us_zip_code, us_ssn, us_age, alphanumeric, us_bank_routing_number, us_bank_account."}, "fixed_width": {"type": "boolean", "default": false, "description": "Text fields only: whether the field width will stay fixed and text will display in multiple lines, rather than one long line. If set to `false` the field width will automatically grow horizontally to fit text on one line. Defaults to `false`."}, "lock_sign_date": {"type": "boolean", "default": false, "description": "Date fields only: makes fields readonly and automatically populates with the date the recipient signed. Defaults to `false`."}, "date_format": {"type": "string", "enum": ["MM/DD/YYYY", "DD/MM/YYYY", "YYYY/MM/DD", "Month DD, YYYY", "MM/DD/YYYY hh:mm:ss a"], "description": "Date fields only: date format to use for the field. Valid values: MM/DD/YYYY, DD/MM/YYYY, YYYY/MM/DD, Month DD, YYYY, and MM/DD/YYYY hh:mm:ss a. Defaults to MM/DD/YYYY."}, "formula": {"type": "string", "description": "Date fields only (text field formulas coming soon): formulas are a way to prefill fields with calculated future or past dates. Addition, subtraction, and parentheses are allowed. Valid event dates are `created_date`, `sent_date`, and `signed_date`. Valid time periods are `day`, `days`, `week`, `weeks`, `month`, and `months`. Example: `formula: \"sent_date + 10 days\"`. Use with `lock_sign_date` if you'd like to make the field readonly and prevent signers from choosing a different date."}}, "required": ["x", "y", "page", "recipient_id", "type"]}}}, "fields_for_template_map": {"type": "array", "description": "Document fields placed on a document for collecting data or signatures from recipients. At least one field must be present in the Create Document request if `draft` is `false` (unless adding a signature page by using `with_signature_page`). Field data should be sent as a 2-dimensional JSON array. One array of fields is needed for each file in the files array. An array of fields can be empty if you have a file that does not contain any fields.", "items": {"type": "array", "description": "Array of Fields you're adding to each file.", "items": {"type": "object", "properties": {"x": {"type": "number", "format": "float", "description": "Horizontal value in the coordinates of the field (in pixels). Coordinates are specific to the page where fields are located."}, "y": {"type": "number", "format": "float", "description": "Vertical value in the coordinates of the field (in pixels). Coordinates are specific to the page where fields are located."}, "page": {"type": "integer", "description": "The page number within the file. If the page does not exist within the file then the field won't be created."}, "placeholder_id": {"type": "string", "description": "Unique identifier of the placeholder assigned to the field."}, "type": {"type": "string", "enum": ["initials", "signature", "checkbox", "date", "text", "autofill_company", "autofill_email", "autofill_first_name", "autofill_last_name", "autofill_name", "autofill_phone", "autofill_title", "autofill_date_signed"], "description": "Field type of the field. Valid field types: initials, signatures, checkbox, date, and text. To autofill fields with contact data, use an autofill field type. To group checkbox fields, enter an api_id for each checkbox and add the checkbox_groups parameter."}, "required": {"type": "boolean", "default": true, "description": "Whether the field must be completed by the recipient. Defaults to `true` except for checkbox type fields."}, "label": {"type": "string", "description": "Text and Date fields only: label that is displayed when the field is empty."}, "value": {"description": "Varies according to the field type. Text fields accept strings or numbers. Date fields accept Iso8601 date strings. CheckBoxes accept booleans. Signature and Initials fields can't be signed through API requests. Autofill text fields accept strings or numbers."}, "api_id": {"type": "string", "description": "Unique identifier of the field. Useful when needing to reference specific field values or update a document and its fields."}, "name": {"type": "string", "description": "Checkbox fields only. At least 2 checkbox fields in an array of fields must be assigned to the same recipient and grouped with selection requirements."}, "validation": {"type": "string", "enum": ["no_text_validation", "numbers", "letters", "email_address", "us_phone_number", "us_zip_code", "us_ssn", "us_age", "alphanumeric", "us_bank_routing_number", "us_bank_account_number"], "description": "Text fields only: optional validation for field values. Valid values: numbers, letters, email_address, us_phone_number, us_zip_code, us_ssn, us_age, alphanumeric, us_bank_routing_number, us_bank_account."}, "fixed_width": {"type": "boolean", "default": false, "description": "Text fields only: whether the field width will stay fixed and text will display in multiple lines, rather than one long line. If set to `false` the field width will automatically grow horizontally to fit text on one line. Defaults to `false`."}, "lock_sign_date": {"type": "boolean", "default": false, "description": "Date fields only: makes fields readonly and automatically populates with the date the recipient signed. Defaults to `false`."}, "date_format": {"type": "string", "enum": ["MM/DD/YYYY", "DD/MM/YYYY", "YYYY/MM/DD", "Month DD, YYYY", "MM/DD/YYYY hh:mm:ss a"], "description": "Date fields only: date format to use for the field. Valid values: MM/DD/YYYY, DD/MM/YYYY, YYYY/MM/DD, Month DD, YYYY, and MM/DD/YYYY hh:mm:ss a. Defaults to MM/DD/YYYY."}}, "required": ["x", "y", "page", "placeholder_id", "type"]}}}, "files_map": {"type": "array", "description": "Document files can be uploaded by specifying a file URL or base64 string. Either `file_url` or `file_base64` must be present (not both). Valid file types are: .pdf, .doc, .docx, .pages, .ppt, .pptx, .key, .xls, .xlsx, .numbers, .jpg, .jpeg, .png, .tiff, .tif, and .webp", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the file that will be uploaded."}, "file_url": {"type": "string", "format": "url", "description": "Publicly available URL of the file to be uploaded."}, "file_base64": {"type": "string", "format": "byte", "description": "A RFC 4648 base64 string of the file to be uploaded."}}, "required": ["name"]}}, "limit_map": {"type": "integer", "minimum": 1, "maximum": 50, "default": 10, "description": "The number of documents to fetch. Defaults to 10, max is 50."}, "message_map": {"type": "string", "description": "Email message for the signature request that recipients will see. Defaults to the default system message or a template message (if the document is created from a template)."}, "metadata_map": {"type": "object", "description": "Optional key-value data that can be associated with the document. If set, will be available every time the document data is returned."}, "page_offset_map": {"type": "integer", "minimum": 1, "default": 1, "description": "The page number for pagination. Defaults to the first page."}, "placeholders_map": {"type": "array", "description": "Placeholders are generally job roles that must complete and/or sign the document. For example, a placeholder might be “Client” or “Legal Department”. When a document is created from the template, you assign a person to each placeholder.", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "A unique identifier that you will give to each placeholder. We recommend numbering sequentially from 1 to X. IDs are required for associating recipients to fields and more."}, "name": {"type": "string", "description": "Name of the placeholder."}, "preassigned_recipient_name": {"type": "string", "description": "In some cases, it may be necessary to pre-fill the name and email for a placeholder because it will always be the same person for all documents created from this template. This sets the name."}, "preassigned_recipient_email": {"type": "string", "format": "email", "description": "In some cases, it may be necessary to pre-fill the name and email for a placeholder because it will always be the same person for all documents created from this template. This sets the email."}}, "required": ["id", "name"]}}, "placeholder_message_map": {"type": "string", "description": "Email message for the signature request that the recipient will see. Overrides the general message for the template."}, "placeholder_subject_map": {"type": "string", "description": "Email subject for the signature request that the recipient will see. Overrides the general subject for the template."}, "redirect_url_map": {"type": "string", "format": "url", "description": "A URL that recipients are redirected to after successfully signing a document."}, "reminders_map": {"type": "boolean", "default": true, "description": "Whether to send signing reminders to recipients. Reminders are sent on day 3, day 6, and day 10 if set to `true`. Defaults to `true`."}, "recipient_message_map": {"type": "string", "description": "Email message for the signature request that the recipient will see. Overrides the general message for the document."}, "recipient_subject_map": {"type": "string", "description": "Email subject for the signature request that the recipient will see. Overrides the general subject for the document."}, "remind_recipients_map": {"type": "array", "description": "Optional list if recipients within the document to send a reminder email to. If none are specified, all recipients that have not signed yet will receive a reminder email.", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "Recipient's name (required if multiple recipients share the same email)."}, "email": {"type": "string", "format": "email", "description": "Recipient's email address."}}}}, "send_email_map": {"type": "boolean", "default": false, "description": "Applies on when `embedded_signing` is `true`. By default, recipients are not notified through email to sign when doing embedded signing. Setting this to `true`  will send a notification email to the recipient. Default is `false`."}, "send_email_delay_map": {"type": "integer", "default": 0, "description": "If `send_email` is `true` recipients will receive a new document notification immediately. In the case of embedded signing, you can delay this notification to only send if the document is not completed within a few minutes. The email notification will not go out if the document is completed before the delay time is over. Valid values are in minutes ranging from `0` to `60`. Defaults to `0`."}, "skip_row_errors_map": {"type": "boolean", "default": false, "description": "Whether to skip errors in the rows. Defaults to `false`."}, "subject_map": {"type": "string", "description": "Email subject for the signature request that recipients will see. Defaults to the default system subject or a template subject (if the document is created from a template)."}, "template_fields_map": {"type": "array", "description": "Fields of your template(s) that you can prepopulate with values. Signature and Initials fields cannot be signed through the API.", "items": {"type": "object", "properties": {"api_id": {"type": "string", "description": "The API ID of the field in your template. This field is case sensitive."}, "value": {"description": "TextField value must be a string or a number."}}, "required": ["api_id", "value"]}}, "template_id_param_map": {"type": "string", "format": "uuid", "description": "Unique identifier for a template."}, "template_ids_param_map": {"type": "array", "items": {"type": "string", "format": "uuid"}, "description": "Unique identifiers for a list of templates."}, "template_id_map": {"type": "string", "format": "uuid", "description": "Use when you have to create a document from a single template. Either :template_id or :template_ids must be present in the request, not both."}, "template_ids_map": {"type": "array", "description": "Use when you have to create a document from multiple templates. Either :template_id or :template_ids must be present in the request, not both.", "items": {"type": "string"}}, "template_name_map": {"type": "string", "description": "The name of the template."}, "template_recipients_map": {"type": "array", "description": "Document recipients are people that must complete and/or sign a document. Recipients of the document must be assigned to a placeholder of the template. Recipients will inherit all placeholder fields and settings.", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "A unique identifier that you will give to each recipient. We recommend numbering sequentially from 1 to X. IDs are required for associating recipients to fields and more."}, "name": {"type": "string", "description": "Name of the recipient."}, "email": {"type": "string", "format": "email", "description": "Email address for the recipient."}, "placeholder_name": {"type": "string", "description": "The name of the placeholder you want this recipient assigned to."}, "passcode": {"type": "string", "description": "If set, signers assigned with a passcode will be required to enter the passcode before they’re able to view and complete the document."}, "subject": {"type": "string", "description": "Email subject for the signature request that the recipient will see. Overrides the general subject for the template."}, "message": {"type": "string", "description": "Email message for the signature request that the recipient will see. Overrides the general message for the template."}, "send_email": {"type": "boolean", "default": false, "description": "Applies on when `embedded_signing` is `true`. By default, recipients are not notified through email to sign when doing embedded signing. Setting this to `true`  will send a notification email to the recipient. Default is `false`."}, "send_email_delay": {"type": "integer", "default": 0, "description": "If `send_email` is `true` recipients will receive a new document notification immediately. In the case of embedded signing, you can delay this notification to only send if the document is not completed within a few minutes. The email notification will not go out if the document is completed before the delay time is over. Valid values are in minutes ranging from `0` to `60`. Defaults to `0`."}}, "required": ["id", "email"]}}, "test_mode_map": {"type": "boolean", "default": false, "description": "Set to `true` to enable Test Mode. Documents created with Test Mode do not count towards API billing and are not legally binding. Defaults to `false`"}, "text_tags_map": {"type": "boolean", "default": false, "description": "An alternative way (if you can’t use the recommended way) of placing fields in specific locations of your document by using special text tags. Useful when changing the content of your files changes the location of fields. See API documentation for “Text Tags” for details. Defaults to false."}, "url_only_map": {"type": "boolean", "default": false, "description": "Whether to return the URL of the completed PDF or the actual PDF content. Defaults to `false`."}, "audit_page_map": {"type": "boolean", "default": true, "description": "Whether to include the audit page as part of the document. Defaults to `true`"}, "user_email_map": {"type": "string", "format": "email", "description": "The email address of the user that sent the Bulk Send. Must have the `admin` or `manager` role to view Bulk Sends of other users. Defaults to the user that the API key belongs to."}, "with_signature_page_map": {"type": "boolean", "default": false, "description": "When set to `true` the document will have a signature page added to the end, and all signers will be required to add their signature on that page."}, "labels_map_create": {"type": "array", "description": "Labels can be used to organize documents in a way that can make it easy to find using the document search in SignWell. A document can have multiple labels.", "items": {"type": "object", "description": "Labels can be used to organize documents and templates in a way that can make it easy to find using the document search/template search in SignWell. Labels can be used to organize documents in a way that can make it easy to find using the document search in SignWell.", "properties": {"name": {"type": "string"}}, "required": ["name"]}}, "labels_map_update": {"type": "array", "description": "Labels can be used to organize documents in a way that can make it easy to find using the document search in SignWell. A document can have multiple labels. Updating labels on a document will replace any existing labels for that document.", "items": {"type": "object", "description": "Labels can be used to organize documents and templates in a way that can make it easy to find using the document search/template search in SignWell. Labels can be used to organize documents in a way that can make it easy to find using the document search in SignWell.", "properties": {"name": {"type": "string"}}, "required": ["name"]}}, "labels_map_response": {"type": "array", "description": "Labels can be used to organize documents in a way that can make it easy to find using the document search in SignWell. A document can have multiple labels. Updating labels on a document will replace any existing labels for that document.", "items": {"type": "object", "description": "Labels can be used to organize documents and templates in a way that can make it easy to find using the document search/template search in SignWell. Labels can be used to organize documents in a way that can make it easy to find using the document search in SignWell.", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}}, "required": ["name"]}}, "label_response": {"type": "object", "description": "Labels can be used to organize documents and templates in a way that can make it easy to find using the document search/template search in SignWell. Labels can be used to organize documents in a way that can make it easy to find using the document search in SignWell.", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}}, "required": ["name"]}, "label_request": {"type": "object", "description": "Labels can be used to organize documents and templates in a way that can make it easy to find using the document search/template search in SignWell. Labels can be used to organize documents in a way that can make it easy to find using the document search in SignWell.", "properties": {"name": {"type": "string"}}, "required": ["name"]}, "create_bulk_send_request": {"type": "object", "properties": {"template_ids": {"type": "array", "items": {"type": "string", "format": "uuid"}, "description": "Unique identifiers for a list of templates."}, "bulk_send_csv": {"type": "string", "format": "byte", "description": "A RFC 4648 base64 string of the template CSV file to be validated."}, "skip_row_errors": {"type": "boolean", "default": false, "description": "Whether to skip errors in the rows. Defaults to `false`."}, "api_application_id": {"type": "string", "format": "uuid", "description": "Unique identifier for API Application settings to use. API Applications are optional and mainly used when isolating OAuth apps or for more control over embedded API settings"}, "name": {"type": "string", "description": "The name of the Bulk Send. Will be used as the document name for each of the documents."}, "subject": {"type": "string", "description": "Email subject for the signature request that recipients will see. Defaults to the default system subject or a template subject."}, "message": {"type": "string", "description": "Email message for the signature request that recipients will see. Defaults to the default system message or a template message."}, "apply_signing_order": {"type": "boolean", "default": false, "description": "When set to `true` recipients will sign one at a time in the order of the `recipients` collection of this request."}, "custom_requester_name": {"type": "string", "description": "Sets the custom requester name for the document. When set, this is the name used for all email communications, signing notifications, and in the audit file."}, "custom_requester_email": {"type": "string", "format": "email", "description": "Sets the custom requester email for the document. When set, this is the email used for all email communications, signing notifications, and in the audit file."}}, "required": ["template_ids", "bulk_send_csv"]}, "bulk_send_csv_request": {"type": "object", "properties": {"template_ids": {"type": "array", "description": "Specify one or more templates to generate a single blank CSV file that will contain available columns for your recipient data. The template_ids[] parameter is an array of template IDs (e.g.,`/?template_ids[]=5a67dbd7-928a-4ea0-a7e2-e476a0eb045f&template_ids[]=d7315111-c671-4b15-8354-c9a19bbaefa0`). Each ID should be a separate parameter in the query string.", "items": {"type": "string", "format": "uuid"}}, "bulk_send_csv": {"type": "string", "format": "byte", "description": "A RFC 4648 base64 string of the template CSV file to be validated."}}, "required": ["template_ids", "bulk_send_csv"]}, "document_request": {"type": "object", "properties": {"test_mode": {"type": "boolean", "default": false, "description": "Set to `true` to enable Test Mode. Documents created with Test Mode do not count towards API billing and are not legally binding. Defaults to `false`"}, "files": {"type": "array", "description": "Document files can be uploaded by specifying a file URL or base64 string. Either `file_url` or `file_base64` must be present (not both). Valid file types are: .pdf, .doc, .docx, .pages, .ppt, .pptx, .key, .xls, .xlsx, .numbers, .jpg, .jpeg, .png, .tiff, .tif, and .webp", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the file that will be uploaded."}, "file_url": {"type": "string", "format": "url", "description": "Publicly available URL of the file to be uploaded."}, "file_base64": {"type": "string", "format": "byte", "description": "A RFC 4648 base64 string of the file to be uploaded."}}, "required": ["name"]}}, "name": {"type": "string", "description": "The name of the document."}, "subject": {"type": "string", "description": "Email subject for the signature request that recipients will see. Defaults to the default system subject or a template subject (if the document is created from a template)."}, "message": {"type": "string", "description": "Email message for the signature request that recipients will see. Defaults to the default system message or a template message (if the document is created from a template)."}, "recipients": {"type": "array", "description": "Document recipients are people that must complete and/or sign a document.", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "A unique identifier that you will give to each recipient. We recommend numbering sequentially from 1 to X. IDs are required for associating recipients to fields and more."}, "name": {"type": "string", "description": "Name of the recipient."}, "email": {"type": "string", "format": "email", "description": "Email address for the recipient."}, "passcode": {"type": "string", "description": "If set, signers assigned with a passcode will be required to enter the passcode before they’re able to view and complete the document."}, "subject": {"type": "string", "description": "Email subject for the signature request that the recipient will see. Overrides the general subject for the document."}, "message": {"type": "string", "description": "Email message for the signature request that the recipient will see. Overrides the general message for the document."}, "send_email": {"type": "boolean", "default": false, "description": "Applies on when `embedded_signing` is `true`. By default, recipients are not notified through email to sign when doing embedded signing. Setting this to `true`  will send a notification email to the recipient. Default is `false`."}, "send_email_delay": {"type": "integer", "default": 0, "description": "If `send_email` is `true` recipients will receive a new document notification immediately. In the case of embedded signing, you can delay this notification to only send if the document is not completed within a few minutes. The email notification will not go out if the document is completed before the delay time is over. Valid values are in minutes ranging from `0` to `60`. Defaults to `0`."}}, "required": ["id", "email"]}}, "draft": {"type": "boolean", "default": false, "description": "Whether the document can still be updated before sending a signature request. If set to `false` the document is sent for signing as part of this request. Defaults to `false`."}, "with_signature_page": {"type": "boolean", "default": false, "description": "When set to `true` the document will have a signature page added to the end, and all signers will be required to add their signature on that page."}, "expires_in": {"type": "integer", "minimum": 1, "description": "Number of days before the signature request expires. Defaults to the account expiration setting or template expiration (if the document is created from a template)."}, "reminders": {"type": "boolean", "default": true, "description": "Whether to send signing reminders to recipients. Reminders are sent on day 3, day 6, and day 10 if set to `true`. Defaults to `true`."}, "apply_signing_order": {"type": "boolean", "default": false, "description": "When set to `true` recipients will sign one at a time in the order of the `recipients` collection of this request."}, "api_application_id": {"type": "string", "format": "uuid", "description": "Unique identifier for API Application settings to use. API Applications are optional and mainly used when isolating OAuth apps or for more control over embedded API settings"}, "embedded_signing": {"type": "boolean", "default": false, "description": "When set to `true` it enables embedded signing in your website/web application. Embedded functionality works with an iFrame and email authentication is disabled. :embedded_signinig defaults to `false`."}, "embedded_signing_notifications": {"type": "boolean", "default": false, "description": "On embedding signing, document owners (and CC'd contacts) do not get a notification email when documents have been completed. Setting this param to `true` will send out those final completed notifications. Default is `false`"}, "text_tags": {"type": "boolean", "default": false, "description": "An alternative way (if you can’t use the recommended way) of placing fields in specific locations of your document by using special text tags. Useful when changing the content of your files changes the location of fields. See API documentation for “Text Tags” for details. Defaults to false."}, "custom_requester_name": {"type": "string", "description": "Sets the custom requester name for the document. When set, this is the name used for all email communications, signing notifications, and in the audit file."}, "custom_requester_email": {"type": "string", "format": "email", "description": "Sets the custom requester email for the document. When set, this is the email used for all email communications, signing notifications, and in the audit file."}, "redirect_url": {"type": "string", "format": "url", "description": "A URL that recipients are redirected to after successfully signing a document."}, "allow_decline": {"type": "boolean", "default": true, "description": "Whether to allow recipients the option to decline signing a document. If multiple signers are involved in a document, any single recipient can cancel the entire document signing process by declining to sign."}, "allow_reassign": {"type": "boolean", "default": true, "description": "In some cases a signer is not the right person to sign and may need to reassign their signing responsibilities to another person. This feature allows them to reassign the document to someone else."}, "decline_redirect_url": {"type": "string", "format": "url", "description": "A URL that recipients are redirected to if the document is declined."}, "metadata": {"type": "object", "description": "Optional key-value data that can be associated with the document. If set, will be available every time the document data is returned."}, "fields": {"type": "array", "description": "Document fields placed on a document for collecting data or signatures from recipients. At least one field must be present in the Create Document request if `draft` is `false` (unless adding a signature page by using `with_signature_page`). Field data should be sent as a 2-dimensional JSON array. One array of fields is needed for each file in the files array. An array of fields can be empty if you have a file that does not contain any fields.", "items": {"type": "array", "description": "Array of Fields you're adding to each file.", "items": {"type": "object", "properties": {"x": {"type": "number", "format": "float", "description": "Horizontal value in the coordinates of the field (in pixels). Coordinates are specific to the page where fields are located."}, "y": {"type": "number", "format": "float", "description": "Vertical value in the coordinates of the field (in pixels). Coordinates are specific to the page where fields are located."}, "page": {"type": "integer", "description": "The page number within the file. If the page does not exist within the file then the field won't be created."}, "recipient_id": {"type": "string", "description": "Unique identifier of the recipient assigned to the field. Recipients assigned to fields will be the only ones that will see and be able to complete those fields."}, "type": {"type": "string", "enum": ["initials", "signature", "checkbox", "date", "text", "autofill_company", "autofill_email", "autofill_first_name", "autofill_last_name", "autofill_name", "autofill_phone", "autofill_title", "autofill_date_signed"], "description": "Field type of the field. Valid field types: initials, signatures, checkbox, date, and text. To autofill fields with contact data, use an autofill field type. To group checkbox fields, enter an api_id for each checkbox and add the checkbox_groups parameter."}, "required": {"type": "boolean", "default": true, "description": "Whether the field must be completed by the recipient. Defaults to `true` except for checkbox type fields."}, "label": {"type": "string", "description": "Text and Date fields only: label that is displayed when the field is empty."}, "value": {"description": "Varies according to the field type. Text fields accept strings or numbers. Date fields accept Iso8601 date strings. CheckBoxes accept booleans. Signature and Initials fields can't be signed through API requests. Autofill text fields accept strings or numbers."}, "api_id": {"type": "string", "description": "Unique identifier of the field. Useful when needing to reference specific field values or update a document and its fields."}, "name": {"type": "string", "description": "Checkbox fields only. At least 2 checkbox fields in an array of fields must be assigned to the same recipient and grouped with selection requirements."}, "validation": {"type": "string", "enum": ["no_text_validation", "numbers", "letters", "email_address", "us_phone_number", "us_zip_code", "us_ssn", "us_age", "alphanumeric", "us_bank_routing_number", "us_bank_account_number"], "description": "Text fields only: optional validation for field values. Valid values: numbers, letters, email_address, us_phone_number, us_zip_code, us_ssn, us_age, alphanumeric, us_bank_routing_number, us_bank_account."}, "fixed_width": {"type": "boolean", "default": false, "description": "Text fields only: whether the field width will stay fixed and text will display in multiple lines, rather than one long line. If set to `false` the field width will automatically grow horizontally to fit text on one line. Defaults to `false`."}, "lock_sign_date": {"type": "boolean", "default": false, "description": "Date fields only: makes fields readonly and automatically populates with the date the recipient signed. Defaults to `false`."}, "date_format": {"type": "string", "enum": ["MM/DD/YYYY", "DD/MM/YYYY", "YYYY/MM/DD", "Month DD, YYYY", "MM/DD/YYYY hh:mm:ss a"], "description": "Date fields only: date format to use for the field. Valid values: MM/DD/YYYY, DD/MM/YYYY, YYYY/MM/DD, Month DD, YYYY, and MM/DD/YYYY hh:mm:ss a. Defaults to MM/DD/YYYY."}, "formula": {"type": "string", "description": "Date fields only (text field formulas coming soon): formulas are a way to prefill fields with calculated future or past dates. Addition, subtraction, and parentheses are allowed. Valid event dates are `created_date`, `sent_date`, and `signed_date`. Valid time periods are `day`, `days`, `week`, `weeks`, `month`, and `months`. Example: `formula: \"sent_date + 10 days\"`. Use with `lock_sign_date` if you'd like to make the field readonly and prevent signers from choosing a different date."}}, "required": ["x", "y", "page", "recipient_id", "type"]}}}, "attachment_requests": {"type": "array", "description": "Attachments that a recipient must upload to complete the signing process. Attachment requests are shown after all document fields have been completed.", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the requested attachment."}, "recipient_id": {"type": "string", "description": "Unique identifier of the recipient that will view the attachment request."}, "required": {"type": "boolean", "default": true, "description": "Whether the recipient will need to upload the attachment to successfully complete/sign the document. Defaults to `true`."}}, "required": ["name", "recipient_id"]}}, "copied_contacts": {"type": "array", "description": "Copied contacts are emailed the final document once it has been completed by all recipients.", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the copied contact."}, "email": {"type": "string", "format": "email", "description": "Email for the copied contact."}}, "required": ["email"]}}, "labels": {"type": "array", "description": "Labels can be used to organize documents in a way that can make it easy to find using the document search in SignWell. A document can have multiple labels.", "items": {"type": "object", "description": "Labels can be used to organize documents and templates in a way that can make it easy to find using the document search/template search in SignWell. Labels can be used to organize documents in a way that can make it easy to find using the document search in SignWell.", "properties": {"name": {"type": "string"}}, "required": ["name"]}}, "checkbox_groups": {"type": "array", "description": "Checkbox fields that are placed on a document can be grouped with selection requirements. At least 2 checkbox fields in an array of fields must be assigned to the same recipient.", "items": {"type": "object", "properties": {"group_name": {"type": "string", "description": "A unique identifier for the checkbox group."}, "recipient_id": {"type": "string", "description": "The recipient ID associated with the checkbox group."}, "checkbox_ids": {"type": "array", "items": {"type": "string", "description": "A unique identifier for each checkbox in a group. ID must match the api_id of the checkbox field."}}, "validation": {"type": "string", "enum": ["minimum", "maximum", "range", "exact"], "description": "Set requirements for the group of one or multiple selections by the recipient. Defaults to minimum. Validation values: minimum, maximum, exact, range."}, "required": {"type": "boolean", "default": false, "description": "Whether the group must be completed by the recipient. Defaults to false."}, "min_value": {"type": "integer", "description": "The minimum number of checkboxes that must be checked in the group. (Only for validation: minimum and range)"}, "max_value": {"type": "integer", "description": "The maximum number of checkboxes that can be checked in the group. (Only for validation: maximum and range)"}, "exact_value": {"type": "integer", "description": "The exact number of checkboxes that must be checked in the group. (Only for validation: exact)"}}, "required": ["group_name", "recipient_id", "checkbox_ids"]}}}, "required": ["files", "recipients"]}, "document_from_template_request": {"type": "object", "properties": {"test_mode": {"type": "boolean", "default": false, "description": "Set to `true` to enable Test Mode. Documents created with Test Mode do not count towards API billing and are not legally binding. Defaults to `false`"}, "template_id": {"type": "string", "format": "uuid", "description": "Use when you have to create a document from a single template. Either :template_id or :template_ids must be present in the request, not both."}, "template_ids": {"type": "array", "description": "Use when you have to create a document from multiple templates. Either :template_id or :template_ids must be present in the request, not both.", "items": {"type": "string"}}, "name": {"type": "string", "description": "The name of the document."}, "subject": {"type": "string", "description": "Email subject for the signature request that recipients will see. Defaults to the default system subject or a template subject (if the document is created from a template)."}, "message": {"type": "string", "description": "Email message for the signature request that recipients will see. Defaults to the default system message or a template message (if the document is created from a template)."}, "recipients": {"type": "array", "description": "Document recipients are people that must complete and/or sign a document. Recipients of the document must be assigned to a placeholder of the template. Recipients will inherit all placeholder fields and settings.", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "A unique identifier that you will give to each recipient. We recommend numbering sequentially from 1 to X. IDs are required for associating recipients to fields and more."}, "name": {"type": "string", "description": "Name of the recipient."}, "email": {"type": "string", "format": "email", "description": "Email address for the recipient."}, "placeholder_name": {"type": "string", "description": "The name of the placeholder you want this recipient assigned to."}, "passcode": {"type": "string", "description": "If set, signers assigned with a passcode will be required to enter the passcode before they’re able to view and complete the document."}, "subject": {"type": "string", "description": "Email subject for the signature request that the recipient will see. Overrides the general subject for the template."}, "message": {"type": "string", "description": "Email message for the signature request that the recipient will see. Overrides the general message for the template."}, "send_email": {"type": "boolean", "default": false, "description": "Applies on when `embedded_signing` is `true`. By default, recipients are not notified through email to sign when doing embedded signing. Setting this to `true`  will send a notification email to the recipient. Default is `false`."}, "send_email_delay": {"type": "integer", "default": 0, "description": "If `send_email` is `true` recipients will receive a new document notification immediately. In the case of embedded signing, you can delay this notification to only send if the document is not completed within a few minutes. The email notification will not go out if the document is completed before the delay time is over. Valid values are in minutes ranging from `0` to `60`. Defaults to `0`."}}, "required": ["id", "email"]}}, "draft": {"type": "boolean", "default": false, "description": "Whether the document can still be updated before sending a signature request. If set to `false` the document is sent for signing as part of this request. Defaults to `false`."}, "with_signature_page": {"type": "boolean", "default": false, "description": "When set to `true` the document will have a signature page added to the end, and all signers will be required to add their signature on that page."}, "expires_in": {"type": "integer", "minimum": 1, "description": "Number of days before the signature request expires. Defaults to the account expiration setting or template expiration (if the document is created from a template)."}, "reminders": {"type": "boolean", "default": true, "description": "Whether to send signing reminders to recipients. Reminders are sent on day 3, day 6, and day 10 if set to `true`. Defaults to `true`."}, "apply_signing_order": {"type": "boolean", "default": false, "description": "When set to `true` recipients will sign one at a time in the order of the `recipients` collection of this request."}, "api_application_id": {"type": "string", "format": "uuid", "description": "Unique identifier for API Application settings to use. API Applications are optional and mainly used when isolating OAuth apps or for more control over embedded API settings"}, "embedded_signing": {"type": "boolean", "default": false, "description": "When set to `true` it enables embedded signing in your website/web application. Embedded functionality works with an iFrame and email authentication is disabled. :embedded_signinig defaults to `false`."}, "embedded_signing_notifications": {"type": "boolean", "default": false, "description": "On embedding signing, document owners (and CC'd contacts) do not get a notification email when documents have been completed. Setting this param to `true` will send out those final completed notifications. Default is `false`"}, "text_tags": {"type": "boolean", "default": false, "description": "An alternative way (if you can’t use the recommended way) of placing fields in specific locations of your document by using special text tags. Useful when changing the content of your files changes the location of fields. See API documentation for “Text Tags” for details. Defaults to false."}, "custom_requester_name": {"type": "string", "description": "Sets the custom requester name for the document. When set, this is the name used for all email communications, signing notifications, and in the audit file."}, "custom_requester_email": {"type": "string", "format": "email", "description": "Sets the custom requester email for the document. When set, this is the email used for all email communications, signing notifications, and in the audit file."}, "redirect_url": {"type": "string", "format": "url", "description": "A URL that recipients are redirected to after successfully signing a document."}, "allow_decline": {"type": "boolean", "default": true, "description": "Whether to allow recipients the option to decline signing a document. If multiple signers are involved in a document, any single recipient can cancel the entire document signing process by declining to sign."}, "allow_reassign": {"type": "boolean", "default": true, "description": "In some cases a signer is not the right person to sign and may need to reassign their signing responsibilities to another person. This feature allows them to reassign the document to someone else."}, "decline_redirect_url": {"type": "string", "format": "url", "description": "A URL that recipients are redirected to if the document is declined."}, "metadata": {"type": "object", "description": "Optional key-value data that can be associated with the document. If set, will be available every time the document data is returned."}, "template_fields": {"type": "array", "description": "Fields of your template(s) that you can prepopulate with values. Signature and Initials fields cannot be signed through the API.", "items": {"type": "object", "properties": {"api_id": {"type": "string", "description": "The API ID of the field in your template. This field is case sensitive."}, "value": {"description": "TextField value must be a string or a number."}}, "required": ["api_id", "value"]}}, "files": {"type": "array", "items": {"type": "object", "description": "Additional files to be appended to the document. Will not replace existing files from the template. Document files can be uploaded by specifying a file URL or base64 string. Either `file_url` or `file_base64` must be present (not both). Valid file types are: .pdf, .docx, .jpg, .png, .ppt, .xls, .pages, and .txt.", "properties": {"name": {"type": "string", "description": "Name of the file that will be uploaded."}, "file_url": {"type": "string", "format": "url", "description": "Publicly available URL of the file to be uploaded."}, "file_base64": {"type": "string", "format": "byte", "description": "A RFC 4648 base64 string of the file to be uploaded."}}, "required": ["name"]}}, "fields": {"type": "array", "description": "Fields to be added to any appended files (not existing files). Document fields placed on a document for collecting data or signatures from recipients. Field data should be sent as a 2-dimensional JSON array. One array of fields is needed for each file in the files array. An array of fields can be empty if you have a file that does not contain any fields.", "items": {"type": "array", "description": "Array of Fields you're adding to each file.", "items": {"type": "object", "properties": {"x": {"type": "number", "format": "float", "description": "Horizontal value in the coordinates of the field (in pixels). Coordinates are specific to the page where fields are located."}, "y": {"type": "number", "format": "float", "description": "Vertical value in the coordinates of the field (in pixels). Coordinates are specific to the page where fields are located."}, "page": {"type": "integer", "description": "The page number within the file. If the page does not exist within the file then the field won't be created."}, "recipient_id": {"type": "string", "description": "Unique identifier of the recipient assigned to the field. Recipients assigned to fields will be the only ones that will see and be able to complete those fields."}, "type": {"type": "string", "enum": ["initials", "signature", "checkbox", "date", "text", "autofill_company", "autofill_email", "autofill_first_name", "autofill_last_name", "autofill_name", "autofill_phone", "autofill_title", "autofill_date_signed"], "description": "Field type of the field. Valid field types: initials, signatures, checkbox, date, and text. To autofill fields with contact data, use an autofill field type. To group checkbox fields, enter an api_id for each checkbox and add the checkbox_groups parameter."}, "required": {"type": "boolean", "default": true, "description": "Whether the field must be completed by the recipient. Defaults to `true` except for checkbox type fields."}, "label": {"type": "string", "description": "Text and Date fields only: label that is displayed when the field is empty."}, "value": {"description": "Varies according to the field type. Text fields accept strings or numbers. Date fields accept Iso8601 date strings. CheckBoxes accept booleans. Signature and Initials fields can't be signed through API requests. Autofill text fields accept strings or numbers."}, "api_id": {"type": "string", "description": "Unique identifier of the field. Useful when needing to reference specific field values or update a document and its fields."}, "name": {"type": "string", "description": "Checkbox fields only. At least 2 checkbox fields in an array of fields must be assigned to the same recipient and grouped with selection requirements."}, "validation": {"type": "string", "enum": ["no_text_validation", "numbers", "letters", "email_address", "us_phone_number", "us_zip_code", "us_ssn", "us_age", "alphanumeric", "us_bank_routing_number", "us_bank_account_number"], "description": "Text fields only: optional validation for field values. Valid values: numbers, letters, email_address, us_phone_number, us_zip_code, us_ssn, us_age, alphanumeric, us_bank_routing_number, us_bank_account."}, "fixed_width": {"type": "boolean", "default": false, "description": "Text fields only: whether the field width will stay fixed and text will display in multiple lines, rather than one long line. If set to `false` the field width will automatically grow horizontally to fit text on one line. Defaults to `false`."}, "lock_sign_date": {"type": "boolean", "default": false, "description": "Date fields only: makes fields readonly and automatically populates with the date the recipient signed. Defaults to `false`."}, "date_format": {"type": "string", "enum": ["MM/DD/YYYY", "DD/MM/YYYY", "YYYY/MM/DD", "Month DD, YYYY", "MM/DD/YYYY hh:mm:ss a"], "description": "Date fields only: date format to use for the field. Valid values: MM/DD/YYYY, DD/MM/YYYY, YYYY/MM/DD, Month DD, YYYY, and MM/DD/YYYY hh:mm:ss a. Defaults to MM/DD/YYYY."}}, "required": ["x", "y", "page", "recipient_id", "type"]}}}, "attachment_requests": {"type": "array", "description": "Attachments that a recipient must upload to complete the signing process. Attachment requests are shown after all document fields have been completed.", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the requested attachment."}, "recipient_id": {"type": "string", "description": "Unique identifier of the recipient that will view the attachment request."}, "required": {"type": "boolean", "default": true, "description": "Whether the recipient will need to upload the attachment to successfully complete/sign the document. Defaults to `true`."}}, "required": ["name", "recipient_id"]}}, "copied_contacts": {"type": "array", "description": "Copied contacts are emailed the final document once it has been completed by all recipients.", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the copied contact."}, "email": {"type": "string", "format": "email", "description": "Email for the copied contact."}}, "required": ["email"]}}, "labels": {"type": "array", "description": "Labels can be used to organize documents in a way that can make it easy to find using the document search in SignWell. A document can have multiple labels. Updating labels on a document will replace any existing labels for that document.", "items": {"type": "object", "description": "Labels can be used to organize documents and templates in a way that can make it easy to find using the document search/template search in SignWell. Labels can be used to organize documents in a way that can make it easy to find using the document search in SignWell.", "properties": {"name": {"type": "string"}}, "required": ["name"]}}, "checkbox_groups": {"type": "array", "description": "Checkbox fields that are placed on a document can be grouped with selection requirements. At least 2 checkbox fields in an array of fields must be assigned to the same recipient.", "items": {"type": "object", "properties": {"group_name": {"type": "string", "description": "A unique identifier for the checkbox group."}, "recipient_id": {"type": "string", "description": "The recipient ID associated with the checkbox group."}, "checkbox_ids": {"type": "array", "items": {"type": "string", "description": "A unique identifier for each checkbox in a group. ID must match the api_id of the checkbox field."}}, "validation": {"type": "string", "enum": ["minimum", "maximum", "range", "exact"], "description": "Set requirements for the group of one or multiple selections by the recipient. Defaults to minimum. Validation values: minimum, maximum, exact, range."}, "required": {"type": "boolean", "default": false, "description": "Whether the group must be completed by the recipient. Defaults to false."}, "min_value": {"type": "integer", "description": "The minimum number of checkboxes that must be checked in the group. (Only for validation: minimum and range)"}, "max_value": {"type": "integer", "description": "The maximum number of checkboxes that can be checked in the group. (Only for validation: maximum and range)"}, "exact_value": {"type": "integer", "description": "The exact number of checkboxes that must be checked in the group. (Only for validation: exact)"}}, "required": ["group_name", "recipient_id", "checkbox_ids"]}}}, "required": ["recipients"]}, "update_document_and_send_request": {"type": "object", "properties": {"test_mode": {"type": "boolean", "default": false, "description": "Set to `true` to enable Test Mode. Documents created with Test Mode do not count towards API billing and are not legally binding. Defaults to `false`"}, "name": {"type": "string", "description": "The name of the document."}, "subject": {"type": "string", "description": "Email subject for the signature request that recipients will see. Defaults to the default system subject or a template subject (if the document is created from a template)."}, "message": {"type": "string", "description": "Email message for the signature request that recipients will see. Defaults to the default system message or a template message (if the document is created from a template)."}, "expires_in": {"type": "integer", "minimum": 1, "description": "Number of days before the signature request expires. Defaults to the account expiration setting or template expiration (if the document is created from a template)."}, "reminders": {"type": "boolean", "default": true, "description": "Whether to send signing reminders to recipients. Reminders are sent on day 3, day 6, and day 10 if set to `true`. Defaults to `true`."}, "apply_signing_order": {"type": "boolean", "default": false, "description": "When set to `true` recipients will sign one at a time in the order of the `recipients` collection of this request."}, "api_application_id": {"type": "string", "format": "uuid", "description": "Unique identifier for API Application settings to use. API Applications are optional and mainly used when isolating OAuth apps or for more control over embedded API settings"}, "embedded_signing": {"type": "boolean", "default": false, "description": "When set to `true` it enables embedded signing in your website/web application. Embedded functionality works with an iFrame and email authentication is disabled. :embedded_signinig defaults to `false`."}, "embedded_signing_notifications": {"type": "boolean", "default": false, "description": "On embedding signing, document owners (and CC'd contacts) do not get a notification email when documents have been completed. Setting this param to `true` will send out those final completed notifications. Default is `false`"}, "custom_requester_name": {"type": "string", "description": "Sets the custom requester name for the document. When set, this is the name used for all email communications, signing notifications, and in the audit file."}, "custom_requester_email": {"type": "string", "format": "email", "description": "Sets the custom requester email for the document. When set, this is the email used for all email communications, signing notifications, and in the audit file."}, "redirect_url": {"type": "string", "format": "url", "description": "A URL that recipients are redirected to after successfully signing a document."}, "allow_decline": {"type": "boolean", "default": true, "description": "Whether to allow recipients the option to decline signing a document. If multiple signers are involved in a document, any single recipient can cancel the entire document signing process by declining to sign."}, "allow_reassign": {"type": "boolean", "default": true, "description": "In some cases a signer is not the right person to sign and may need to reassign their signing responsibilities to another person. This feature allows them to reassign the document to someone else."}, "decline_redirect_url": {"type": "string", "format": "url", "description": "A URL that recipients are redirected to if the document is declined."}, "metadata": {"type": "object", "description": "Optional key-value data that can be associated with the document. If set, will be available every time the document data is returned."}, "labels": {"type": "array", "description": "Labels can be used to organize documents in a way that can make it easy to find using the document search in SignWell. A document can have multiple labels. Updating labels on a document will replace any existing labels for that document.", "items": {"type": "object", "description": "Labels can be used to organize documents and templates in a way that can make it easy to find using the document search/template search in SignWell. Labels can be used to organize documents in a way that can make it easy to find using the document search in SignWell.", "properties": {"name": {"type": "string"}}, "required": ["name"]}}, "checkbox_groups": {"type": "array", "description": "Checkbox fields that are placed on a document can be grouped with selection requirements. At least 2 checkbox fields in an array of fields must be assigned to the same recipient.", "items": {"type": "object", "properties": {"group_name": {"type": "string", "description": "A unique identifier for the checkbox group."}, "recipient_id": {"type": "string", "description": "The recipient ID associated with the checkbox group."}, "checkbox_ids": {"type": "array", "items": {"type": "string", "description": "A unique identifier for each checkbox in a group. ID must match the api_id of the checkbox field."}}, "validation": {"type": "string", "enum": ["minimum", "maximum", "range", "exact"], "description": "Set requirements for the group of one or multiple selections by the recipient. Defaults to minimum. Validation values: minimum, maximum, exact, range."}, "required": {"type": "boolean", "default": false, "description": "Whether the group must be completed by the recipient. Defaults to false."}, "min_value": {"type": "integer", "description": "The minimum number of checkboxes that must be checked in the group. (Only for validation: minimum and range)"}, "max_value": {"type": "integer", "description": "The maximum number of checkboxes that can be checked in the group. (Only for validation: maximum and range)"}, "exact_value": {"type": "integer", "description": "The exact number of checkboxes that must be checked in the group. (Only for validation: exact)"}}, "required": ["group_name", "recipient_id", "checkbox_ids"]}}}}, "send_reminder_request": {"type": "object", "properties": {"recipients": {"type": "array", "description": "Optional list if recipients within the document to send a reminder email to. If none are specified, all recipients that have not signed yet will receive a reminder email.", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "Recipient's name (required if multiple recipients share the same email)."}, "email": {"type": "string", "format": "email", "description": "Recipient's email address."}}}}}}, "document_template_request": {"type": "object", "properties": {"files": {"type": "array", "description": "Document files can be uploaded by specifying a file URL or base64 string. Either `file_url` or `file_base64` must be present (not both). Valid file types are: .pdf, .doc, .docx, .pages, .ppt, .pptx, .key, .xls, .xlsx, .numbers, .jpg, .jpeg, .png, .tiff, .tif, and .webp", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the file that will be uploaded."}, "file_url": {"type": "string", "format": "url", "description": "Publicly available URL of the file to be uploaded."}, "file_base64": {"type": "string", "format": "byte", "description": "A RFC 4648 base64 string of the file to be uploaded."}}, "required": ["name"]}}, "name": {"type": "string", "description": "The name of the template."}, "subject": {"type": "string", "description": "Email subject for the signature request that recipients will see. Defaults to the default system subject or a template subject (if the document is created from a template)."}, "message": {"type": "string", "description": "Email message for the signature request that recipients will see. Defaults to the default system message or a template message (if the document is created from a template)."}, "placeholders": {"type": "array", "description": "Placeholders are generally job roles that must complete and/or sign the document. For example, a placeholder might be “Client” or “Legal Department”. When a document is created from the template, you assign a person to each placeholder.", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "A unique identifier that you will give to each placeholder. We recommend numbering sequentially from 1 to X. IDs are required for associating recipients to fields and more."}, "name": {"type": "string", "description": "Name of the placeholder."}, "preassigned_recipient_name": {"type": "string", "description": "In some cases, it may be necessary to pre-fill the name and email for a placeholder because it will always be the same person for all documents created from this template. This sets the name."}, "preassigned_recipient_email": {"type": "string", "format": "email", "description": "In some cases, it may be necessary to pre-fill the name and email for a placeholder because it will always be the same person for all documents created from this template. This sets the email."}}, "required": ["id", "name"]}}, "copied_placeholders": {"type": "array", "description": "Copied placeholders are emailed the final document once it has been completed by all recipients.", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the placeholder."}, "preassigned_recipient_name": {"type": "string", "description": "In some cases, it may be necessary to pre-fill the name and email for a placeholder because it will always be the same person for all documents created from this template. This sets the name."}, "preassigned_recipient_email": {"type": "string", "format": "email", "description": "In some cases, it may be necessary to pre-fill the name and email for a placeholder because it will always be the same person for all documents created from this template. This sets the email."}}, "required": ["name"]}}, "draft": {"type": "boolean", "default": false, "description": "Whether the template can still be updated before it is ready for usage. If set to `false` the template is marked as `Available` and it will be ready for use. Defaults to `false`."}, "expires_in": {"type": "integer", "minimum": 1, "description": "Number of days before the signature request expires. Defaults to the account expiration setting or template expiration (if the document is created from a template)."}, "reminders": {"type": "boolean", "default": true, "description": "Whether to send signing reminders to recipients. Reminders are sent on day 3, day 6, and day 10 if set to `true`. Defaults to `true`."}, "apply_signing_order": {"type": "boolean", "default": false, "description": "When set to `true` recipients will sign one at a time in the order of the `recipients` collection of this request."}, "api_application_id": {"type": "string", "format": "uuid", "description": "Unique identifier for API Application settings to use. API Applications are optional and mainly used when isolating OAuth apps or for more control over embedded API settings"}, "text_tags": {"type": "boolean", "default": false, "description": "An alternative way (if you can’t use the recommended way) of placing fields in specific locations of your document by using special text tags. Useful when changing the content of your files changes the location of fields. See API documentation for “Text Tags” for details. Defaults to false."}, "redirect_url": {"type": "string", "format": "url", "description": "A URL that recipients are redirected to after successfully signing a document."}, "allow_decline": {"type": "boolean", "default": true, "description": "Whether to allow recipients the option to decline signing a document. If multiple signers are involved in a document, any single recipient can cancel the entire document signing process by declining to sign."}, "allow_reassign": {"type": "boolean", "default": true, "description": "In some cases a signer is not the right person to sign and may need to reassign their signing responsibilities to another person. This feature allows them to reassign the document to someone else."}, "decline_redirect_url": {"type": "string", "format": "url", "description": "A URL that recipients are redirected to if the document is declined."}, "metadata": {"type": "object", "description": "Optional key-value data that can be associated with the document. If set, will be available every time the document data is returned."}, "fields": {"type": "array", "description": "Document fields placed on a document for collecting data or signatures from recipients. At least one field must be present in the Create Document request if `draft` is `false` (unless adding a signature page by using `with_signature_page`). Field data should be sent as a 2-dimensional JSON array. One array of fields is needed for each file in the files array. An array of fields can be empty if you have a file that does not contain any fields.", "items": {"type": "array", "description": "Array of Fields you're adding to each file.", "items": {"type": "object", "properties": {"x": {"type": "number", "format": "float", "description": "Horizontal value in the coordinates of the field (in pixels). Coordinates are specific to the page where fields are located."}, "y": {"type": "number", "format": "float", "description": "Vertical value in the coordinates of the field (in pixels). Coordinates are specific to the page where fields are located."}, "page": {"type": "integer", "description": "The page number within the file. If the page does not exist within the file then the field won't be created."}, "placeholder_id": {"type": "string", "description": "Unique identifier of the placeholder assigned to the field."}, "type": {"type": "string", "enum": ["initials", "signature", "checkbox", "date", "text", "autofill_company", "autofill_email", "autofill_first_name", "autofill_last_name", "autofill_name", "autofill_phone", "autofill_title", "autofill_date_signed"], "description": "Field type of the field. Valid field types: initials, signatures, checkbox, date, and text. To autofill fields with contact data, use an autofill field type. To group checkbox fields, enter an api_id for each checkbox and add the checkbox_groups parameter."}, "required": {"type": "boolean", "default": true, "description": "Whether the field must be completed by the recipient. Defaults to `true` except for checkbox type fields."}, "label": {"type": "string", "description": "Text and Date fields only: label that is displayed when the field is empty."}, "value": {"description": "Varies according to the field type. Text fields accept strings or numbers. Date fields accept Iso8601 date strings. CheckBoxes accept booleans. Signature and Initials fields can't be signed through API requests. Autofill text fields accept strings or numbers."}, "api_id": {"type": "string", "description": "Unique identifier of the field. Useful when needing to reference specific field values or update a document and its fields."}, "name": {"type": "string", "description": "Checkbox fields only. At least 2 checkbox fields in an array of fields must be assigned to the same recipient and grouped with selection requirements."}, "validation": {"type": "string", "enum": ["no_text_validation", "numbers", "letters", "email_address", "us_phone_number", "us_zip_code", "us_ssn", "us_age", "alphanumeric", "us_bank_routing_number", "us_bank_account_number"], "description": "Text fields only: optional validation for field values. Valid values: numbers, letters, email_address, us_phone_number, us_zip_code, us_ssn, us_age, alphanumeric, us_bank_routing_number, us_bank_account."}, "fixed_width": {"type": "boolean", "default": false, "description": "Text fields only: whether the field width will stay fixed and text will display in multiple lines, rather than one long line. If set to `false` the field width will automatically grow horizontally to fit text on one line. Defaults to `false`."}, "lock_sign_date": {"type": "boolean", "default": false, "description": "Date fields only: makes fields readonly and automatically populates with the date the recipient signed. Defaults to `false`."}, "date_format": {"type": "string", "enum": ["MM/DD/YYYY", "DD/MM/YYYY", "YYYY/MM/DD", "Month DD, YYYY", "MM/DD/YYYY hh:mm:ss a"], "description": "Date fields only: date format to use for the field. Valid values: MM/DD/YYYY, DD/MM/YYYY, YYYY/MM/DD, Month DD, YYYY, and MM/DD/YYYY hh:mm:ss a. Defaults to MM/DD/YYYY."}}, "required": ["x", "y", "page", "placeholder_id", "type"]}}}, "attachment_requests": {"type": "array", "description": "Attachments that a recipient must upload to complete the signing process. Attachment requests are shown after all document fields have been completed.", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the requested attachment."}, "placeholder_id": {"type": "string", "description": "Unique identifier of the recipient that will view the attachment request."}, "required": {"type": "boolean", "default": true, "description": "Whether the recipient will need to upload the attachment to successfully complete/sign the document. Defaults to `true`."}}, "required": ["name", "placeholder_id"]}}, "labels": {"type": "array", "description": "Labels can be used to organize documents in a way that can make it easy to find using the document search in SignWell. A document can have multiple labels.", "items": {"type": "object", "description": "Labels can be used to organize documents and templates in a way that can make it easy to find using the document search/template search in SignWell. Labels can be used to organize documents in a way that can make it easy to find using the document search in SignWell.", "properties": {"name": {"type": "string"}}, "required": ["name"]}}, "checkbox_groups": {"type": "array", "description": "Checkbox fields that are placed on a document can be grouped with selection requirements. At least 2 checkbox fields in an array of fields must be assigned to the same recipient.", "items": {"type": "object", "properties": {"group_name": {"type": "string", "description": "A unique identifier for the checkbox group."}, "placeholder_id": {"type": "string", "description": "The recipient ID associated with the checkbox group."}, "checkbox_ids": {"type": "array", "items": {"type": "string", "description": "A unique identifier for each checkbox in a group. ID must match the api_id of the checkbox field."}}, "validation": {"type": "string", "enum": ["minimum", "maximum", "range", "exact"], "description": "Set requirements for the group of one or multiple selections by the recipient. Defaults to minimum. Validation values: minimum, maximum, exact, range."}, "required": {"type": "boolean", "default": false, "description": "Whether the group must be completed by the recipient. Defaults to false."}, "min_value": {"type": "integer", "description": "The minimum number of checkboxes that must be checked in the group. (Only for validation: minimum and range)"}, "max_value": {"type": "integer", "description": "The maximum number of checkboxes that can be checked in the group. (Only for validation: maximum and range)"}, "exact_value": {"type": "integer", "description": "The exact number of checkboxes that must be checked in the group. (Only for validation: exact)"}}, "required": ["group_name", "placeholder_id", "checkbox_ids"]}}}, "required": ["files", "placeholders"]}, "document_template_update_request": {"type": "object", "properties": {"name": {"type": "string", "description": "The name of the template."}, "subject": {"type": "string", "description": "Email subject for the signature request that recipients will see. Defaults to the default system subject or a template subject (if the document is created from a template)."}, "message": {"type": "string", "description": "Email message for the signature request that recipients will see. Defaults to the default system message or a template message (if the document is created from a template)."}, "draft": {"type": "boolean", "default": false, "description": "Whether the template can still be updated before it is ready for usage. If set to `false` the template is marked as `Available` and it will be ready for use. Defaults to `false`."}, "expires_in": {"type": "integer", "minimum": 1, "description": "Number of days before the signature request expires. Defaults to the account expiration setting or template expiration (if the document is created from a template)."}, "reminders": {"type": "boolean", "default": true, "description": "Whether to send signing reminders to recipients. Reminders are sent on day 3, day 6, and day 10 if set to `true`. Defaults to `true`."}, "apply_signing_order": {"type": "boolean", "default": false, "description": "When set to `true` recipients will sign one at a time in the order of the `recipients` collection of this request."}, "api_application_id": {"type": "string", "format": "uuid", "description": "Unique identifier for API Application settings to use. API Applications are optional and mainly used when isolating OAuth apps or for more control over embedded API settings"}, "redirect_url": {"type": "string", "format": "url", "description": "A URL that recipients are redirected to after successfully signing a document."}, "allow_decline": {"type": "boolean", "default": true, "description": "Whether to allow recipients the option to decline signing a document. If multiple signers are involved in a document, any single recipient can cancel the entire document signing process by declining to sign."}, "allow_reassign": {"type": "boolean", "default": true, "description": "In some cases a signer is not the right person to sign and may need to reassign their signing responsibilities to another person. This feature allows them to reassign the document to someone else."}, "decline_redirect_url": {"type": "string", "format": "url", "description": "A URL that recipients are redirected to if the document is declined."}, "metadata": {"type": "object", "description": "Optional key-value data that can be associated with the document. If set, will be available every time the document data is returned."}, "labels": {"type": "array", "description": "Labels can be used to organize documents in a way that can make it easy to find using the document search in SignWell. A document can have multiple labels. Updating labels on a document will replace any existing labels for that document.", "items": {"type": "object", "description": "Labels can be used to organize documents and templates in a way that can make it easy to find using the document search/template search in SignWell. Labels can be used to organize documents in a way that can make it easy to find using the document search in SignWell.", "properties": {"name": {"type": "string"}}, "required": ["name"]}}, "checkbox_groups": {"type": "array", "description": "Checkbox fields that are placed on a document can be grouped with selection requirements. At least 2 checkbox fields in an array of fields must be assigned to the same recipient.", "items": {"type": "object", "properties": {"group_name": {"type": "string", "description": "A unique identifier for the checkbox group."}, "placeholder_id": {"type": "string", "description": "The recipient ID associated with the checkbox group."}, "checkbox_ids": {"type": "array", "items": {"type": "string", "description": "A unique identifier for each checkbox in a group. ID must match the api_id of the checkbox field."}}, "validation": {"type": "string", "enum": ["minimum", "maximum", "range", "exact"], "description": "Set requirements for the group of one or multiple selections by the recipient. Defaults to minimum. Validation values: minimum, maximum, exact, range."}, "required": {"type": "boolean", "default": false, "description": "Whether the group must be completed by the recipient. Defaults to false."}, "min_value": {"type": "integer", "description": "The minimum number of checkboxes that must be checked in the group. (Only for validation: minimum and range)"}, "max_value": {"type": "integer", "description": "The maximum number of checkboxes that can be checked in the group. (Only for validation: maximum and range)"}, "exact_value": {"type": "integer", "description": "The exact number of checkboxes that must be checked in the group. (Only for validation: exact)"}}, "required": ["group_name", "placeholder_id", "checkbox_ids"]}}}, "required": ["files", "placeholders"]}, "document_pdf_json": {"type": "object", "properties": {"file_url": {"type": "string", "format": "url"}}}, "document_pdf_file": {"type": "string", "format": "binary"}, "document_response": {"type": "object", "properties": {"test_mode": {"type": "boolean"}, "id": {"type": "string"}, "api_application_id": {"type": "string", "format": "uuid"}, "requester_email_address": {"type": "string", "format": "email"}, "custom_requester_name": {"type": "string"}, "custom_requester_email": {"type": "string", "format": "email"}, "name": {"type": "string"}, "subject": {"type": "string"}, "message": {"type": "string"}, "metadata": {"type": "object"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}, "recipients": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "email": {"type": "string"}, "body": {"type": "string"}, "message": {"type": "string"}, "subject": {"type": "string"}, "send_email": {"type": "boolean"}, "send_email_delay": {"type": "integer"}, "attachment_requests": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "url": {"type": "string", "format": "url"}, "required": {"type": "boolean"}}, "required": ["name", "required"]}}, "passcode": {"type": "string"}, "status": {"type": "string"}}, "required": ["name", "email"]}}, "status": {"type": "string"}, "reminders": {"type": "boolean"}, "archived": {"type": "boolean"}, "embedded": {"type": "boolean"}, "embedded_edit_url": {"type": "string", "format": "url"}, "embedded_preview_url": {"type": "string", "format": "url"}, "apply_signing_order": {"type": "boolean"}, "redirect_url": {"type": "string", "format": "url"}, "decline_redirect_url": {"type": "string", "format": "url"}, "expires_in": {"type": "integer"}, "attachment_requests": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "recipient_id": {"type": "string"}, "required": {"type": "boolean"}}, "required": ["name", "recipient_id"]}}, "files": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "pages_number": {"type": "integer"}}, "required": ["name", "pages_number"]}}, "copied_contacts": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "email": {"type": "string"}}, "required": ["email"]}}, "fields": {"type": "array", "items": {"type": "array", "items": {"type": "object", "properties": {"x": {"type": "number", "format": "float"}, "y": {"type": "number", "format": "float"}, "page": {"type": "integer"}, "recipient": {"type": "object", "properties": {"email": {"type": "string", "format": "email"}, "name": {"type": "string"}}, "required": ["email", "name"]}, "api_id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "date_format": {"type": "string"}, "fixed_width": {"type": "boolean"}, "label": {"type": "string"}, "lock_sign_date": {"type": "boolean"}, "required": {"type": "boolean"}, "type": {"type": "string"}, "validation": {"type": "string"}, "value": {}}, "required": ["x", "y", "page"]}}}, "allow_decline": {"type": "boolean"}, "allow_reassign": {"type": "boolean"}, "labels": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}}}}, "checkbox_groups": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "group_name": {"type": "string"}, "recipient_id": {"type": "string"}, "checkbox_ids": {"type": "array", "items": {"type": "string"}}, "validation": {"type": "string"}, "required": {"type": "boolean"}, "min_value": {"type": "integer"}}, "required": ["id", "group_name", "recipient_id", "checkbox_ids", "validation", "required"]}}}, "required": ["id", "test_mode"]}, "document_template_response": {"type": "object", "properties": {"id": {"type": "string"}, "api_application_id": {"type": "string", "format": "uuid"}, "requester_email_address": {"type": "string", "format": "email"}, "custom_requester_name": {"type": "string"}, "custom_requester_email": {"type": "string", "format": "email"}, "name": {"type": "string"}, "subject": {"type": "string"}, "message": {"type": "string"}, "metadata": {"type": "object"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}, "placeholders": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "preassigned_recipient_name": {"type": "string"}, "preassigned_recipient_email": {"type": "string"}, "attachment_requests": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "url": {"type": "string", "format": "url"}, "required": {"type": "boolean"}}, "required": ["name", "required"]}}}, "required": ["name"]}}, "copied_placeholders": {"type": "array", "items": {"type": "object", "properties": {"placeholder_id": {"type": "string"}, "name": {"type": "string"}, "preassigned_recipient_name": {"type": "string"}, "preassigned_recipient_email": {"type": "string"}}, "required": ["name"]}}, "status": {"type": "string"}, "reminders": {"type": "boolean"}, "archived": {"type": "boolean"}, "template_link": {"type": "string", "format": "url"}, "apply_signing_order": {"type": "boolean"}, "redirect_url": {"type": "string", "format": "url"}, "decline_redirect_url": {"type": "string", "format": "url"}, "expires_in": {"type": "integer"}, "files": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "pages_number": {"type": "integer"}}, "required": ["name", "pages_number"]}}, "fields": {"type": "array", "items": {"type": "array", "items": {"type": "object", "properties": {"x": {"type": "number", "format": "float"}, "y": {"type": "number", "format": "float"}, "page": {"type": "integer"}, "recipient": {"type": "object", "properties": {"email": {"type": "string", "format": "email"}, "name": {"type": "string"}}, "required": ["email", "name"]}, "api_id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "date_format": {"type": "string"}, "fixed_width": {"type": "boolean"}, "label": {"type": "string"}, "lock_sign_date": {"type": "boolean"}, "required": {"type": "boolean"}, "type": {"type": "string"}, "validation": {"type": "string"}, "value": {}}, "required": ["x", "y", "page"]}}}, "allow_decline": {"type": "boolean"}, "allow_reassign": {"type": "boolean"}, "labels": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}}}}, "checkbox_groups": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "group_name": {"type": "string"}, "recipient_id": {"type": "string"}, "checkbox_ids": {"type": "array", "items": {"type": "string"}}, "validation": {"type": "string"}, "required": {"type": "boolean"}, "min_value": {"type": "integer"}}, "required": ["id", "group_name", "recipient_id", "checkbox_ids", "validation", "required"]}}}, "required": ["id"]}, "document_from_template_response": {"type": "object", "properties": {"test_mode": {"type": "boolean"}, "id": {"type": "string"}, "template_id": {"type": "string"}, "template_ids": {"type": "array", "items": {"type": "string"}}, "api_application_id": {"type": "string", "format": "uuid"}, "requester_email_address": {"type": "string", "format": "email"}, "custom_requester_name": {"type": "string"}, "custom_requester_email": {"type": "string", "format": "email"}, "name": {"type": "string"}, "subject": {"type": "string"}, "message": {"type": "string"}, "metadata": {"type": "object"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}, "recipients": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "email": {"type": "string"}, "role": {"type": "string"}, "attachment_requests": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "url": {"type": "string", "format": "url"}, "required": {"type": "boolean"}}, "required": ["name", "required"]}}, "passcode": {"type": "string"}, "status": {"type": "string"}}, "required": ["email"]}}, "status": {"type": "string"}, "reminders": {"type": "boolean"}, "archived": {"type": "boolean"}, "embedded": {"type": "boolean"}, "embedded_edit_url": {"type": "string", "format": "url"}, "apply_signing_order": {"type": "boolean"}, "redirect_url": {"type": "string", "format": "url"}, "decline_redirect_url": {"type": "string", "format": "url"}, "expires_in": {"type": "integer"}, "attachment_requests": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "recipient_id": {"type": "string"}, "required": {"type": "boolean"}}, "required": ["name", "recipient_id"]}}, "files": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "pages_number": {"type": "integer"}}, "required": ["name", "pages_number"]}}, "copied_contacts": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "email": {"type": "string"}}, "required": ["email"]}}, "fields": {"type": "array", "items": {"type": "array", "items": {"type": "object", "properties": {"x": {"type": "number", "format": "float"}, "y": {"type": "number", "format": "float"}, "page": {"type": "integer"}, "recipient": {"type": "object", "properties": {"email": {"type": "string", "format": "email"}, "name": {"type": "string"}, "role": {"type": "string"}}, "required": ["email", "name"]}, "api_id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "date_format": {"type": "string"}, "fixed_width": {"type": "boolean"}, "label": {"type": "string"}, "lock_sign_date": {"type": "boolean"}, "required": {"type": "boolean"}, "type": {"type": "string"}, "validation": {"type": "string"}, "value": {}}, "required": ["x", "y", "page"]}}}, "allow_decline": {"type": "boolean"}, "allow_reassign": {"type": "boolean"}, "labels": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}}}}}, "required": ["id", "test_mode"]}}}, "servers": [{"url": "https://www.signwell.com"}]}