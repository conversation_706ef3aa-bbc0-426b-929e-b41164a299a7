{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 133.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-10 22:24:11"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-10 22:24:11"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 133.0.0","description":"New organization created","ip_address":"::1","method":"POST","new_value":"{\"first_name\":\"allu\",\"last_name\":\"arjun\",\"middle_name\":\"\",\"email\":\"<EMAIL>\",\"status\":\"Screened\",\"gender\":\"male\",\"address\":\"india\",\"contact_number\":\"10000000000\",\"date_of_birth\":\"02/10/1998\",\"stipend\":\"33\",\"study_enrolled_ids\":\"1\",\"notification\":\"some notification\",\"note\":\"Note for admin\",\"role_id\":\"10\",\"organization_detail_id\":\"1\",\"ecrf_id\":\"A101-688\",\"timezone\":\"Asia/Karachi\"}","old_value":"null","operation":"CREATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-10 22:24:13"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/371","browser":"Chrome 133.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-03-10 22:25:44"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-10 22:25:44"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/371","browser":"Chrome 133.0.0","description":"Registration status updated to Blocked for ID 371","ip_address":"::1","method":"PUT","new_value":"{\"id\":\"371\",\"status\":\"Blocked\",\"reason\":\"Accepted!\",\"user_id\":478}","old_value":"{}","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-03-10 22:25:45"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/348","browser":"Chrome 133.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-03-10 22:25:59"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-10 22:26:00"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/348","browser":"Chrome 133.0.0","description":"Registration status updated to Blocked for ID 348","ip_address":"::1","method":"PUT","new_value":"{\"id\":\"348\",\"status\":\"Blocked\",\"reason\":\"sad\",\"user_id\":455}","old_value":"{}","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-03-10 22:26:01"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 133.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-10 22:28:56"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-10 22:28:56"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 133.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-10 22:29:05"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-10 22:29:05"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 133.0.0","description":"New organization created","ip_address":"::1","method":"POST","new_value":"{\"first_name\":\"prabu\",\"last_name\":\"manjeshwar\",\"middle_name\":\"\",\"email\":\"<EMAIL>\",\"status\":\"Screened\",\"gender\":\"male\",\"address\":\"prabu\",\"contact_number\":\"10000000000\",\"date_of_birth\":\"02/14/1995\",\"stipend\":\"3\",\"study_enrolled_ids\":\"1\",\"notification\":\"some notification\",\"note\":\"Note for admin\",\"role_id\":\"10\",\"organization_detail_id\":\"1\",\"ecrf_id\":\"A343-570\",\"timezone\":\"Asia/Karachi\"}","old_value":"null","operation":"CREATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-10 22:29:07"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 133.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-10 22:30:51"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-10 22:30:51"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 133.0.0","description":"New organization created","ip_address":"::1","method":"POST","new_value":"{\"first_name\":\"sikandar\",\"last_name\":\"Azam\",\"middle_name\":\"\",\"email\":\"<EMAIL>\",\"status\":\"Screened\",\"gender\":\"male\",\"address\":\"misar\",\"contact_number\":\"10000000000\",\"date_of_birth\":\"01/10/1999\",\"stipend\":\"3\",\"study_enrolled_ids\":\"1\",\"notification\":\"some notification\",\"note\":\"Note for admin\",\"role_id\":\"10\",\"organization_detail_id\":\"1\",\"ecrf_id\":\"A454-232\",\"timezone\":\"Pacific/Honolulu\"}","old_value":"null","operation":"CREATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-10 22:30:53"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 133.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-10 22:36:31"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-10 22:36:32"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 133.0.0","description":"New organization created","ip_address":"::1","method":"POST","new_value":"{\"first_name\":\"ciber\",\"last_name\":\"crime\",\"middle_name\":\"\",\"email\":\"<EMAIL>\",\"status\":\"Screened\",\"gender\":\"male\",\"address\":\"ciber\",\"contact_number\":\"10000000000\",\"date_of_birth\":\"01/01/1999\",\"stipend\":\"3\",\"study_enrolled_ids\":\"1\",\"notification\":\"some notification\",\"note\":\"Note for admin\",\"role_id\":\"10\",\"organization_detail_id\":\"1\",\"ecrf_id\":\"A202-028\",\"timezone\":\"Pacific/Midway\"}","old_value":"null","operation":"CREATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-10 22:36:34"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 133.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-10 22:38:09"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-10 22:38:10"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 133.0.0","description":"New organization created","ip_address":"::1","method":"POST","new_value":"{\"first_name\":\"multi\",\"last_name\":\"function\",\"middle_name\":\"\",\"email\":\"<EMAIL>\",\"status\":\"Screened\",\"gender\":\"male\",\"address\":\"multi\",\"contact_number\":\"10000000000\",\"date_of_birth\":\"01/01/1999\",\"stipend\":\"23\",\"study_enrolled_ids\":\"1\",\"notification\":\"some notification\",\"note\":\"Note for admin\",\"role_id\":\"10\",\"organization_detail_id\":\"1\",\"ecrf_id\":\"A202-022\",\"timezone\":\"Pacific/Midway\"}","old_value":"null","operation":"CREATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-10 22:38:12"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 133.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-10 22:40:01"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-10 22:40:01"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 133.0.0","description":"New organization created","ip_address":"::1","method":"POST","new_value":"{\"first_name\":\"laptop\",\"last_name\":\"keyboard\",\"middle_name\":\"\",\"email\":\"<EMAIL>\",\"status\":\"Screened\",\"gender\":\"male\",\"address\":\"laptop\",\"contact_number\":\"1000000000\",\"date_of_birth\":\"01/01/1999\",\"stipend\":\"45\",\"study_enrolled_ids\":\"1\",\"notification\":\"some notification\",\"note\":\"Note for admin\",\"role_id\":\"10\",\"organization_detail_id\":\"1\",\"ecrf_id\":\"A566-565\",\"timezone\":\"Pacific/Midway\"}","old_value":"null","operation":"CREATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-10 22:40:04"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 133.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-10 22:52:32"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-10 22:52:32"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 133.0.0","description":"New organization created","ip_address":"::1","method":"POST","new_value":"{\"first_name\":\"mouse\",\"last_name\":\"button\",\"middle_name\":\"\",\"email\":\"<EMAIL>\",\"status\":\"Screened\",\"gender\":\"male\",\"address\":\"mouse\",\"contact_number\":\"19000000000\",\"date_of_birth\":\"01/01/1999\",\"stipend\":\"3\",\"study_enrolled_ids\":\"1\",\"notification\":\"some notification\",\"note\":\"Note for admin\",\"role_id\":\"10\",\"organization_detail_id\":\"1\",\"ecrf_id\":\"A435-545\",\"timezone\":\"Pacific/Midway\"}","old_value":"null","operation":"CREATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-10 22:52:35"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 133.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-10 23:03:40"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-10 23:03:40"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 133.0.0","description":"New organization created","ip_address":"::1","method":"POST","new_value":"{\"first_name\":\"button\",\"last_name\":\"bee\",\"middle_name\":\"\",\"email\":\"<EMAIL>\",\"status\":\"Screened\",\"gender\":\"male\",\"address\":\"bee\",\"contact_number\":\"10000000000\",\"date_of_birth\":\"01/01/1999\",\"stipend\":\"34\",\"study_enrolled_ids\":\"1\",\"notification\":\"some notification\",\"note\":\"Note for admin\",\"role_id\":\"10\",\"organization_detail_id\":\"1\",\"ecrf_id\":\"A343-343\",\"timezone\":\"Pacific/Midway\"}","old_value":"null","operation":"CREATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-10 23:03:42"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 133.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-10 23:28:56"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-10 23:28:57"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 133.0.0","description":"New organization created","ip_address":"::1","method":"POST","new_value":"{\"first_name\":\"haha\",\"last_name\":\"haha\",\"middle_name\":\"\",\"email\":\"<EMAIL>\",\"status\":\"Screened\",\"gender\":\"male\",\"address\":\"hahah\",\"contact_number\":\"10000000000\",\"date_of_birth\":\"01/01/1999\",\"stipend\":\"3\",\"study_enrolled_ids\":\"1\",\"notification\":\"some notification\",\"note\":\"Note for admin\",\"role_id\":\"10\",\"organization_detail_id\":\"1\",\"ecrf_id\":\"A454-545\",\"timezone\":\"Pacific/Midway\"}","old_value":"null","operation":"CREATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-10 23:28:59"}
