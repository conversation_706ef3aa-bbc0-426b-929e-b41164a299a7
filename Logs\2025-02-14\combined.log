{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 133.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-02-14 07:50:44"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/419","browser":"Chrome 133.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-02-14 07:51:44"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-14 07:51:44"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/419","browser":"Chrome 133.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-02-14 07:51:59"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-14 07:51:59"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/416","browser":"Chrome 133.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-02-14 07:52:31"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-14 07:52:31"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/416","browser":"Chrome 133.0.0","description":"change","ip_address":"::1","method":"PUT","new_value":"{\"email\":\"<EMAIL>\",\"first_name\":\"Hora cv\",\"reason\":\"change\"}","old_value":"{\"first_name\":\"Hora \"}","operation":"UPDATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-02-14 07:52:32"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/416","browser":"Chrome 133.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-02-14 07:52:45"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-14 07:52:46"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/416","browser":"Chrome 133.0.0","description":"test","ip_address":"::1","method":"PUT","new_value":"{\"email\":\"<EMAIL>\",\"first_name\":\"Hora\",\"reason\":\"test\"}","old_value":"{\"first_name\":\"Hora cv\"}","operation":"UPDATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-02-14 07:52:47"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 133.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-02-14 10:56:06"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 133.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-02-14 11:00:55"}
