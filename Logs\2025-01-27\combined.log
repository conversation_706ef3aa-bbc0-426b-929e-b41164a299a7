{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 131.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-01-27 05:07:50"}
{"level":"info","message":{"api_url":"/organization/logout","browser":"Chrome 131.0.0","description":"User logged out successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"LOGOUT","table_name":"N/A","user":"<EMAIL>"},"timestamp":"2025-01-27 05:10:09"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-01-27 05:10:15"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 131.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-01-27 05:10:30"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 131.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-01-27 05:48:43"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 131.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-01-27 05:51:03"}
{"level":"info","message":{"api_url":"/incident_report/createIncidentReportResponse","browser":"Other 0.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"incident report response","user":"<EMAIL>"},"timestamp":"2025-01-27 09:26:29"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-27 09:26:30"}
{"level":"info","message":{"api_url":"/incident_report/createIncidentReportResponse","browser":"Other 0.0.0","description":"Incident report response created:\n          study_id: 1,\n          user_id: 409,\n          question_id: 45,\n          response_text: Texto de respuesta en español.,\n          description: Este es un informe de incidente en español.,\n          incident_severety: Moderate,\n          start_date: 01/10/2025,\n          start_time: 4:54:00 PM,\n          medical_issue: 1,\n          end_date: 2024-12-25,\n          end_time: 15:00","ip_address":"::1","method":"POST","new_value":"{\"study_id\":1,\"user_id\":409,\"question_id\":45,\"response_text\":\"Texto de respuesta en español.\",\"description\":\"Este es un informe de incidente en español.\",\"incident_severety\":\"Moderate\",\"start_date\":\"01/10/2025\",\"start_time\":\"4:54:00 PM\",\"medical_issue\":1,\"end_date\":\"2024-12-25\",\"end_time\":\"15:00\"}","old_value":"null","operation":"SUBMIT","table_name":"Incident Report Response","user":"<EMAIL>"},"timestamp":"2025-01-27 09:26:31"}
{"level":"error","message":"Failed to log to database: Data too long for column 'description' at row 1","timestamp":"2025-01-27 09:26:31"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-01-27 09:37:36"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 131.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-01-27 09:37:42"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 131.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-01-27 09:55:48"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 131.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-01-27 10:05:22"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Other 0.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-01-27 20:50:40"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 131.0.0","description":"User signed in successfully","ip_address":"::ffff:127.0.0.1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-01-27 21:19:30"}
