{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 131.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-01-14 00:05:20"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Other 0.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-01-14 00:45:59"}
{"level":"info","message":{"api_url":"/complaint/make_compliant/365","browser":"Other 0.0.0","description":"Update Non-Compliant to Compliant","ip_address":"::1","method":"PUT","new_value":"{\"reason\":\"My wish\",\"investigator_id\":403}","old_value":"null","operation":"Update","table_name":"Non-Compliant --> Compliant","user":"<EMAIL>"},"timestamp":"2025-01-14 03:14:38"}
{"level":"info","message":{"api_url":"/complaint/make_compliant/353","browser":"Other 0.0.0","description":"Update Non-Compliant to Compliant","ip_address":"::1","method":"PUT","new_value":"{\"reason\":\"My wish\",\"investigator_id\":403}","old_value":"null","operation":"Update","table_name":"Non-Compliant --> Compliant","user":"<EMAIL>"},"timestamp":"2025-01-14 03:35:35"}
{"level":"info","message":{"api_url":"/complaint/make_compliant/353","browser":"Other 0.0.0","description":"Update Non-Compliant to Compliant","ip_address":"::1","method":"PUT","new_value":"{\"reason\":\"My wish\",\"investigator_id\":403}","old_value":"null","operation":"Update","table_name":"Non-Compliant --> Compliant","user":"<EMAIL>"},"timestamp":"2025-01-14 03:37:45"}
{"level":"info","message":{"api_url":"/complaint/make_compliant/353","browser":"Other 0.0.0","description":"Update Non-Compliant to Compliant","ip_address":"::1","method":"PUT","new_value":"{\"reason\":\"My wish\",\"investigator_id\":403}","old_value":"null","operation":"Update","table_name":"Non-Compliant --> Compliant","user":"<EMAIL>"},"timestamp":"2025-01-14 04:02:40"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 131.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-01-14 22:05:57"}
{"level":"info","message":{"api_url":"/organization/logout","browser":"Chrome 131.0.0","description":"User logged out successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"LOGOUT","table_name":"N/A","user":"<EMAIL>"},"timestamp":"2025-01-14 22:06:05"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-01-14 22:06:15"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 131.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-01-14 22:06:57"}
