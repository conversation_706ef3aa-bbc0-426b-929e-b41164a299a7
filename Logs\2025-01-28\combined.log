{"level":"info","message":{"api_url":"/schedule/create_manual_schedule","browser":"Chrome 131.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"Manual schedule","user":"<EMAIL>"},"timestamp":"2025-01-28 00:12:28"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-28 00:12:29"}
{"level":"info","message":{"api_url":"/schedule/create_manual_schedule","browser":"Chrome 131.0.0","description":"Schedule Created successfully for user: 428","ip_address":"::1","method":"POST","new_value":"{}","old_value":"null","operation":"CREATE","table_name":"Manual Schedule","user":"<EMAIL>"},"timestamp":"2025-01-28 00:12:29"}
{"level":"info","message":{"api_url":"/schedule/create_manual_schedule","browser":"Chrome 131.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"Manual schedule","user":"<EMAIL>"},"timestamp":"2025-01-28 00:13:47"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-28 00:13:47"}
{"level":"info","message":{"api_url":"/schedule/create_manual_schedule","browser":"Chrome 131.0.0","description":"Schedule Created successfully for user: 400","ip_address":"::1","method":"POST","new_value":"{}","old_value":"null","operation":"CREATE","table_name":"Manual Schedule","user":"<EMAIL>"},"timestamp":"2025-01-28 00:13:48"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 131.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-01-28 00:15:18"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-28 00:15:18"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 131.0.0","description":"New organization created","ip_address":"::1","method":"POST","new_value":"{\"first_name\":\"Muhammad\",\"last_name\":\"Khan\",\"middle_name\":\"Anas\",\"email\":\"<EMAIL>\",\"status\":\"Screened\",\"gender\":\"male\",\"address\":\"Mardan\",\"contact_number\":\"12377292048\",\"date_of_birth\":\"02/02/1999\",\"stipend\":\"2\",\"study_enrolled_ids\":\"1\",\"notification\":\"some notification\",\"note\":\"Note for admin\",\"role_id\":\"10\",\"organization_detail_id\":\"1\",\"ecrf_id\":\"A121-121\"}","old_value":"null","operation":"CREATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-01-28 00:15:20"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 131.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-01-28 00:15:39"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/330","browser":"Chrome 131.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-01-28 00:16:19"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-28 00:16:20"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/330","browser":"Chrome 131.0.0","description":"Error updating registration status: No organization found for this user","ip_address":"::1","method":"PUT","new_value":"null","old_value":"{\"id\":\"330\",\"status\":\"Accepted\",\"reason\":\"Accepted By Super Admin \",\"user_id\":433}","operation":"UPDATE_ERROR","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-01-28 00:16:20"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/330","browser":"Chrome 131.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-01-28 00:17:04"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-28 00:17:04"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/330","browser":"Chrome 131.0.0","description":"Error updating registration status: No organization found for this user","ip_address":"::1","method":"PUT","new_value":"null","old_value":"{\"id\":\"330\",\"status\":\"Accepted\",\"reason\":\"Accepted By Super Admin \",\"user_id\":433}","operation":"UPDATE_ERROR","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-01-28 00:17:05"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/330","browser":"Chrome 131.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-01-28 00:18:18"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-28 00:18:19"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/330","browser":"Chrome 131.0.0","description":"Error updating registration status: No organization found for this user","ip_address":"::1","method":"PUT","new_value":"null","old_value":"{\"id\":\"330\",\"status\":\"Accepted\",\"reason\":\"Accepted By Super Admin \",\"user_id\":433}","operation":"UPDATE_ERROR","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-01-28 00:18:19"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/330","browser":"Chrome 131.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-01-28 00:42:08"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-28 00:42:08"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/330","browser":"Chrome 131.0.0","description":"Error updating registration status: No organization found for this user","ip_address":"::1","method":"PUT","new_value":"null","old_value":"{\"id\":\"330\",\"status\":\"Accepted\",\"reason\":\"Accepted By Super Admin \",\"user_id\":433}","operation":"UPDATE_ERROR","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-01-28 00:42:09"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/330","browser":"Chrome 131.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-01-28 00:49:29"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-28 00:49:30"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/330","browser":"Chrome 131.0.0","description":"Error updating registration status: pool is not defined","ip_address":"::1","method":"PUT","new_value":"null","old_value":"{\"id\":\"330\",\"status\":\"Accepted\",\"reason\":\"Accepted By Super Admin \",\"user_id\":433}","operation":"UPDATE_ERROR","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-01-28 00:49:30"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/330","browser":"Chrome 131.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-01-28 00:51:33"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-28 00:51:33"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/330","browser":"Chrome 131.0.0","description":"Welcome email sent to fc696a04114283614e381ae356ec4122:744d103cbe277909608709b08bd7f818 2c5d4daff07042b9d6724507a3d6c85a:27869b76563bf7a99447e44bb56e4d98 (<EMAIL>)","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":433,\"email\":\"<EMAIL>\",\"subject\":\"Welcome to ResearchHero!\"}","old_value":"null","operation":"EMAIL_SENT","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-01-28 00:51:37"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/330","browser":"Chrome 131.0.0","description":"Registration status updated to Accepted for ID 330","ip_address":"::1","method":"PUT","new_value":"{\"id\":\"330\",\"status\":\"Accepted\",\"reason\":\"Accepted By Super Admin \",\"user_id\":433}","old_value":"[{\"account_status_id\":330,\"user_id\":433,\"account_status\":\"Pending\",\"reason\":\"Initial registration\",\"first_time\":\"1\",\"updated_at\":\"2025-01-28T06:15:20.000Z\"}]","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-01-28 00:51:38"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/330","browser":"Chrome 131.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-01-28 00:57:02"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-28 00:57:03"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/330","browser":"Chrome 131.0.0","description":"Registration status updated to Blocked for ID 330","ip_address":"::1","method":"PUT","new_value":"{\"id\":\"330\",\"status\":\"Blocked\",\"reason\":\"Accepted By Super Admin \",\"user_id\":433}","old_value":"[{\"account_status_id\":330,\"user_id\":433,\"account_status\":\"Accepted\",\"reason\":\"Accepted By Super Admin \",\"first_time\":\"0\",\"updated_at\":\"2025-01-28T06:15:20.000Z\"}]","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-01-28 00:57:04"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/330","browser":"Chrome 131.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-01-28 00:57:13"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-28 00:57:13"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/330","browser":"Chrome 131.0.0","description":"Welcome email sent to fc696a04114283614e381ae356ec4122:744d103cbe277909608709b08bd7f818 2c5d4daff07042b9d6724507a3d6c85a:27869b76563bf7a99447e44bb56e4d98 (<EMAIL>)","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":433,\"email\":\"<EMAIL>\",\"subject\":\"Welcome to ResearchHero!\"}","old_value":"null","operation":"EMAIL_SENT","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-01-28 00:57:17"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/330","browser":"Chrome 131.0.0","description":"Registration status updated to Accepted for ID 330","ip_address":"::1","method":"PUT","new_value":"{\"id\":\"330\",\"status\":\"Accepted\",\"reason\":\"Accepted By Super Admin \",\"user_id\":433}","old_value":"[{\"account_status_id\":330,\"user_id\":433,\"account_status\":\"Blocked\",\"reason\":\"Accepted By Super Admin \",\"first_time\":\"0\",\"updated_at\":\"2025-01-28T06:15:20.000Z\"}]","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-01-28 00:57:18"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 131.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-01-28 00:59:43"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-28 00:59:43"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 131.0.0","description":"New organization created","ip_address":"::1","method":"POST","new_value":"{\"first_name\":\"Nik\",\"last_name\":\"John\",\"middle_name\":\"\",\"email\":\"<EMAIL>\",\"status\":\"Screened\",\"gender\":\"male\",\"address\":\"Mardan\",\"contact_number\":\"12377292048\",\"date_of_birth\":\"12/30/1996\",\"stipend\":\"2\",\"study_enrolled_ids\":\"1\",\"notification\":\"some notification\",\"note\":\"Note for admin\",\"role_id\":\"10\",\"organization_detail_id\":\"1\",\"ecrf_id\":\"A232-323\"}","old_value":"null","operation":"CREATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-01-28 00:59:45"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/331","browser":"Chrome 131.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-01-28 01:00:07"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-28 01:00:07"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/331","browser":"Chrome 131.0.0","description":"Welcome email sent to 340f16163e5c7f1ea3bf20de73b05c70:3c2c9bb7a1fc37ef5d6aad1006f98e03 01405aed9caa69d5fd7254dc1e54c78c:32df58b052bd6339e77c434846d095f6 (<EMAIL>)","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":434,\"email\":\"<EMAIL>\",\"subject\":\"Welcome to ResearchHero!\"}","old_value":"null","operation":"EMAIL_SENT","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-01-28 01:00:10"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/331","browser":"Chrome 131.0.0","description":"Registration status updated to Accepted for ID 331","ip_address":"::1","method":"PUT","new_value":"{\"id\":\"331\",\"status\":\"Accepted\",\"reason\":\"Accepted By Super Admin \",\"user_id\":434}","old_value":"[{\"account_status_id\":331,\"user_id\":434,\"account_status\":\"Pending\",\"reason\":\"Initial registration\",\"first_time\":\"1\",\"updated_at\":\"2025-01-28T06:59:45.000Z\"}]","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-01-28 01:00:11"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 131.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-01-28 01:05:11"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-28 01:05:11"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 131.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-01-28 01:05:20"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-28 01:05:20"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 131.0.0","description":"New organization created","ip_address":"::1","method":"POST","new_value":"{\"first_name\":\"jhonson\",\"last_name\":\"roman\",\"middle_name\":\"\",\"email\":\"<EMAIL>\",\"status\":\"Screened\",\"gender\":\"male\",\"address\":\"5959 Bonhomme Rd\",\"contact_number\":\"12377292048\",\"date_of_birth\":\"06/09/1998\",\"stipend\":\"2\",\"study_enrolled_ids\":\"1\",\"notification\":\"some notification\",\"note\":\"Note for admin\",\"role_id\":\"10\",\"organization_detail_id\":\"1\",\"ecrf_id\":\"A232-329\"}","old_value":"null","operation":"CREATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-01-28 01:05:22"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/332","browser":"Chrome 131.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-01-28 01:05:45"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-28 01:05:45"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/332","browser":"Chrome 131.0.0","description":"Welcome email sent to 3ae235a2c0da9cf4235306d1e4bfa13a:d3e99cd39a44cabc1aa20e0feda2ef45 3444002d5de4efd6829e10e2f4e0fb79:4456144740724e3cd428280dade62bdc (<EMAIL>)","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":435,\"email\":\"<EMAIL>\",\"subject\":\"Welcome to ResearchHero!\"}","old_value":"null","operation":"EMAIL_SENT","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-01-28 01:05:47"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/332","browser":"Chrome 131.0.0","description":"Registration status updated to Accepted for ID 332","ip_address":"::1","method":"PUT","new_value":"{\"id\":\"332\",\"status\":\"Accepted\",\"reason\":\"Accepted By Super Admin \",\"user_id\":435}","old_value":"[{\"account_status_id\":332,\"user_id\":435,\"account_status\":\"Pending\",\"reason\":\"Initial registration\",\"first_time\":\"1\",\"updated_at\":\"2025-01-28T07:05:23.000Z\"}]","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-01-28 01:05:48"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 131.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-01-28 01:08:56"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-28 01:08:56"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 131.0.0","description":"New organization created","ip_address":"::1","method":"POST","new_value":"{\"first_name\":\"check\",\"last_name\":\"error\",\"middle_name\":\"\",\"email\":\"<EMAIL>\",\"status\":\"Screened\",\"gender\":\"male\",\"address\":\"5959 Bonhomme Rd\",\"contact_number\":\"12377292048\",\"date_of_birth\":\"03/11/1998\",\"stipend\":\"2\",\"study_enrolled_ids\":\"1\",\"notification\":\"some notification\",\"note\":\"Note for admin\",\"role_id\":\"10\",\"organization_detail_id\":\"1\",\"ecrf_id\":\"A123-443\"}","old_value":"null","operation":"CREATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-01-28 01:08:58"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/333","browser":"Chrome 131.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-01-28 01:09:23"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-28 01:09:23"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/333","browser":"Chrome 131.0.0","description":"Welcome email sent to 51ccffb89d6e0a5df3501b59ec9dd0d6:d83656dc246e5e5eeff4b21971c89bec 886833a5ee711db6d794061cb2922a52:002c39a9657663fea0afd5441bf9b149 (<EMAIL>)","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":436,\"email\":\"<EMAIL>\",\"subject\":\"Welcome to ResearchHero!\"}","old_value":"null","operation":"EMAIL_SENT","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-01-28 01:09:26"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/333","browser":"Chrome 131.0.0","description":"Registration status updated to Accepted for ID 333","ip_address":"::1","method":"PUT","new_value":"{\"id\":\"333\",\"status\":\"Accepted\",\"reason\":\"Accepted By Super Admin \",\"user_id\":436}","old_value":"[{\"account_status_id\":333,\"user_id\":436,\"account_status\":\"Pending\",\"reason\":\"Initial registration\",\"first_time\":\"1\",\"updated_at\":\"2025-01-28T07:08:59.000Z\"}]","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-01-28 01:09:27"}
{"level":"info","message":{"api_url":"/schedule/createSchedule","browser":"Other 0.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-01-28 01:19:00"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-28 01:19:01"}
{"level":"info","message":{"api_url":"/schedule/createSchedule","browser":"Other 0.0.0","description":"Schedule created successfully for user 436","ip_address":"::1","method":"POST","new_value":"{}","old_value":"null","operation":"CREATE","table_name":"Schedule","user":"<EMAIL>"},"timestamp":"2025-01-28 01:19:07"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 131.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-01-28 01:33:01"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-28 01:33:01"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 131.0.0","description":"New organization created","ip_address":"::1","method":"POST","new_value":"{\"first_name\":\"cross\",\"last_name\":\"origin\",\"middle_name\":\"\",\"email\":\"<EMAIL>\",\"status\":\"Screened\",\"gender\":\"male\",\"address\":\"5959 Bonhomme Rd\",\"contact_number\":\"12377292048\",\"date_of_birth\":\"02/01/1994\",\"stipend\":\"2\",\"study_enrolled_ids\":\"1\",\"notification\":\"some notification\",\"note\":\"Note for admin\",\"role_id\":\"10\",\"organization_detail_id\":\"1\",\"ecrf_id\":\"A565-656\"}","old_value":"null","operation":"CREATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-01-28 01:33:03"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/334","browser":"Chrome 131.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-01-28 01:33:25"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-28 01:33:25"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/334","browser":"Chrome 131.0.0","description":"Welcome email sent to dc519ea365c58fdade11c4cbae8d6c9c:c5f6b2a51fcbb17b27d6b350692d9cf6 a7e6dfbcd6eb3731929ee1893b3208f6:23940a2caef5f0fe14a6a95cea207822 (<EMAIL>)","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":437,\"email\":\"<EMAIL>\",\"subject\":\"Welcome to ResearchHero!\"}","old_value":"null","operation":"EMAIL_SENT","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-01-28 01:33:28"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/334","browser":"Chrome 131.0.0","description":"Registration status updated to Accepted for ID 334","ip_address":"::1","method":"PUT","new_value":"{\"id\":\"334\",\"status\":\"Accepted\",\"reason\":\"Accepted By Super Admin \",\"user_id\":437}","old_value":"[{\"account_status_id\":334,\"user_id\":437,\"account_status\":\"Pending\",\"reason\":\"Initial registration\",\"first_time\":\"1\",\"updated_at\":\"2025-01-28T07:33:04.000Z\"}]","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-01-28 01:33:29"}
{"level":"info","message":{"api_url":"/schedule/createSchedule","browser":"Chrome 131.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-01-28 01:33:29"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-28 01:33:29"}
{"level":"info","message":{"api_url":"/schedule/createSchedule","browser":"Chrome 131.0.0","description":"Schedule created successfully for user 437","ip_address":"::1","method":"POST","new_value":"{}","old_value":"null","operation":"CREATE","table_name":"Schedule","user":"<EMAIL>"},"timestamp":"2025-01-28 01:33:37"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1497","browser":"Chrome 131.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-01-28 01:55:39"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-28 01:55:39"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1497","browser":"Chrome 131.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-01-28 02:00:02"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-28 02:00:02"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1497","browser":"Chrome 131.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-01-28 02:22:24"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-28 02:22:25"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1497","browser":"Chrome 131.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-01-28 02:24:45"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-28 02:24:46"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1497","browser":"Chrome 131.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-01-28 02:26:09"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-28 02:26:09"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1497","browser":"Chrome 131.0.0","ip_address":"::ffff:127.0.0.1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-01-28 02:29:53"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-28 02:29:53"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1497","browser":"Chrome 131.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-01-28 02:35:45"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-28 02:35:45"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1497","browser":"Chrome 131.0.0","description":"Auto Create Medicine","ip_address":"::1","method":"PUT","new_value":"{\"medication_name\":\"Sunobinop Or Placebo\",\"dosage\":\"0.5mg or 1.0mg or 2.0mg\",\"dosage_times\":[\"09:00 PM\"],\"frequencyType\":\"QD\",\"frequencyTime\":\"N/A\",\"frequencyCondition\":\"At Bedtime\",\"dosageType\":\"Tablet\",\"allot_medicine\":\"1\",\"route\":\"Oral\",\"note\":\"Auto-created medicine\",\"user_id\":437,\"investigator_id\":0,\"tracker_time\":\"2025-01-28T08:35:47.250Z\"}","old_value":"null","operation":"CREATE","table_name":"Auto Create Medicine Due to Subject Screening Complete","user":"<EMAIL>"},"timestamp":"2025-01-28 02:35:47"}
{"level":"error","message":"Failed to log to database: Cannot read properties of undefined (reading 'headers')","timestamp":"2025-01-28 02:35:49"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1497","browser":"Chrome 131.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-01-28 02:40:37"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-28 02:40:37"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1497","browser":"Chrome 131.0.0","description":"Schedule completed with ID: 1497","ip_address":"::1","method":"PUT","new_value":"{\"schedule_date\":\"2025-01-28\",\"schedule_time\":\"09:00\",\"status\":\"Completed\",\"note\":\"Auto-created schedule\",\"user_id\":437}","old_value":"{\"schedule_date\":\"2025-01-28T06:00:00.000Z\",\"schedule_time\":\"09:00\",\"status\":\"Scheduled\",\"note\":\"Auto-created schedule\",\"user_id\":437}","operation":"UPDATE","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-01-28 02:40:42"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/210","browser":"Chrome 131.0.0","description":"Updated fields: dosageType","ip_address":"::1","method":"PUT","new_value":"{\"dosageType\":\"Capsule\"}","old_value":"{\"dosageType\":\"Tablet\"}","operation":"UPDATE","table_name":"Medication","user":"<EMAIL>"},"timestamp":"2025-01-28 02:41:24"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/210","browser":"Chrome 131.0.0","description":"Error updating medication: No organization found for this user","ip_address":"::1","method":"PUT","new_value":"null","old_value":"{\"medication_id\":\"210\"}","operation":"UPDATE_ERROR","table_name":"Medication","user":"<EMAIL>"},"timestamp":"2025-01-28 02:41:25"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/210","browser":"Chrome 131.0.0","description":"Updated fields: dosageType","ip_address":"::1","method":"PUT","new_value":"{\"dosageType\":\"Syrup\"}","old_value":"{\"dosageType\":\"Capsule\"}","operation":"UPDATE","table_name":"Medication","user":"<EMAIL>"},"timestamp":"2025-01-28 02:41:48"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/210","browser":"Chrome 131.0.0","description":"Error updating medication: No organization found for this user","ip_address":"::1","method":"PUT","new_value":"null","old_value":"{\"medication_id\":\"210\"}","operation":"UPDATE_ERROR","table_name":"Medication","user":"<EMAIL>"},"timestamp":"2025-01-28 02:41:49"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/210","browser":"Chrome 131.0.0","description":"Updated fields: dosageType","ip_address":"::ffff:127.0.0.1","method":"PUT","new_value":"{\"dosageType\":\"Injection\"}","old_value":"{\"dosageType\":\"Syrup\"}","operation":"UPDATE","table_name":"Medication","user":"<EMAIL>"},"timestamp":"2025-01-28 02:46:38"}
{"level":"info","message":{"api_url":"/medicine/createMedicine","browser":"Chrome 131.0.0","description":"Manually medication created","ip_address":"::1","method":"POST","new_value":"{\"medication_name\":\"  CHECK\",\"dosage\":\"12\",\"dosage_times\":[\"02:55\"],\"frequencyType\":\"QD\",\"frequencyTime\":\"Morning\",\"frequencyCondition\":\"Before Meal\",\"dosageType\":\"Granules\",\"allot_medicine\":\"1\",\"route\":\"Oral\",\"note\":\"121212121\",\"investigator_id\":403,\"user_id\":\"437\",\"tracker_time\":\"2025-01-28T08:55:42.240Z\"}","old_value":"null","operation":"CREATE","table_name":"Manually medication Created","user":"<EMAIL>"},"timestamp":"2025-01-28 02:55:44"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 131.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-01-28 02:59:26"}
{"level":"info","message":{"api_url":"/medicine/createMedicine","browser":"Chrome 131.0.0","description":"Manually medication created","ip_address":"::1","method":"POST","new_value":"{\"medication_name\":\"sdsad\",\"dosage\":\"12\",\"dosage_times\":[\"03:05\"],\"frequencyType\":\"QD\",\"frequencyTime\":\"Morning\",\"frequencyCondition\":\"Fasting\",\"dosageType\":\"Cream\",\"allot_medicine\":\"1\",\"route\":\"Oral\",\"note\":\"sdasdsads\",\"investigator_id\":403,\"user_id\":\"437\",\"tracker_time\":\"2025-01-28T09:06:15.147Z\"}","old_value":"null","operation":"CREATE","table_name":"Manually medication Created","user":"<EMAIL>"},"timestamp":"2025-01-28 03:06:17"}
{"level":"info","message":{"api_url":"/medicine/createMedicine","browser":"Chrome 131.0.0","description":"Manually medication created","ip_address":"::1","method":"POST","new_value":"{\"medication_name\":\"check\",\"dosage\":\"23\",\"dosage_times\":[\"03:14\"],\"frequencyType\":\"QD\",\"frequencyTime\":\"Morning\",\"frequencyCondition\":\"Fasting\",\"dosageType\":\"Drops\",\"allot_medicine\":\"1\",\"route\":\"Oral\",\"note\":\"23131ewr\",\"investigator_id\":403,\"user_id\":\"437\",\"tracker_time\":\"2025-01-28T09:14:50.345Z\"}","old_value":"null","operation":"CREATE","table_name":"Manually medication Created","user":"<EMAIL>"},"timestamp":"2025-01-28 03:14:52"}
{"level":"info","message":{"api_url":"/medicine/deleteMedication/211","browser":"Chrome 131.0.0","description":"Accepted By Super Admin ","ip_address":"::1","method":"DELETE","new_value":"null","old_value":"{\"medication_id\":211,\"created_at\":\"2025-01-28T14:55:51.000Z\",\"medication_name\":\"  CHECK\",\"medication_status\":\"Pending\",\"dosage\":\"12\",\"dosage_time\":\"02:55\",\"frequency_type\":\"QD\",\"frequency_time\":\"Morning\",\"frequency_condition\":\"Before Meal\",\"dosageType\":\"Granules\",\"allot_medicine\":\"1\",\"route\":\"Oral\",\"note\":\"121212121\",\"date_of_birth\":\"02/01/1994\",\"gender\":\"male\",\"stipend\":\"2\",\"first_name\":\"cross\",\"last_name\":\"origin\",\"address\":\"5959 Bonhomme Rd\",\"contact_number\":\"12377292048\",\"study_enrolled_id\":\"1\",\"status\":\"Randomized\",\"user_id\":437,\"email\":\"<EMAIL>\",\"study_name\":\"SUN2003A - 102\"}","operation":"DELETE","table_name":"Medication","user":"<EMAIL>"},"timestamp":"2025-01-28 03:15:28"}
{"level":"info","message":{"api_url":"/medicine/deleteMedication/212","browser":"Chrome 131.0.0","description":"Accepted By Super Admin ","ip_address":"::1","method":"DELETE","new_value":"null","old_value":"{\"medication_id\":212,\"created_at\":\"2025-01-28T15:06:24.000Z\",\"medication_name\":\"sdsad\",\"medication_status\":\"Pending\",\"dosage\":\"12\",\"dosage_time\":\"03:05\",\"frequency_type\":\"QD\",\"frequency_time\":\"Morning\",\"frequency_condition\":\"Fasting\",\"dosageType\":\"Cream\",\"allot_medicine\":\"1\",\"route\":\"Oral\",\"note\":\"sdasdsads\",\"date_of_birth\":\"02/01/1994\",\"gender\":\"male\",\"stipend\":\"2\",\"first_name\":\"cross\",\"last_name\":\"origin\",\"address\":\"5959 Bonhomme Rd\",\"contact_number\":\"12377292048\",\"study_enrolled_id\":\"1\",\"status\":\"Randomized\",\"user_id\":437,\"email\":\"<EMAIL>\",\"study_name\":\"SUN2003A - 102\"}","operation":"DELETE","table_name":"Medication","user":"<EMAIL>"},"timestamp":"2025-01-28 03:15:34"}
{"level":"info","message":{"api_url":"/medicine/deleteMedication/213","browser":"Chrome 131.0.0","description":"Accepted By Super Admin ","ip_address":"::1","method":"DELETE","new_value":"null","old_value":"{\"medication_id\":213,\"created_at\":\"2025-01-28T15:14:59.000Z\",\"medication_name\":\"check\",\"medication_status\":\"Pending\",\"dosage\":\"23\",\"dosage_time\":\"03:14\",\"frequency_type\":\"QD\",\"frequency_time\":\"Morning\",\"frequency_condition\":\"Fasting\",\"dosageType\":\"Drops\",\"allot_medicine\":\"1\",\"route\":\"Oral\",\"note\":\"23131ewr\",\"date_of_birth\":\"02/01/1994\",\"gender\":\"male\",\"stipend\":\"2\",\"first_name\":\"cross\",\"last_name\":\"origin\",\"address\":\"5959 Bonhomme Rd\",\"contact_number\":\"12377292048\",\"study_enrolled_id\":\"1\",\"status\":\"Randomized\",\"user_id\":437,\"email\":\"<EMAIL>\",\"study_name\":\"SUN2003A - 102\"}","operation":"DELETE","table_name":"Medication","user":"<EMAIL>"},"timestamp":"2025-01-28 03:15:42"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 131.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-01-28 03:32:56"}
{"level":"info","message":{"api_url":"/ecrf/submissions","browser":"Chrome 131.0.0","description":"Submitted ECRF answers","ip_address":"::1","method":"POST","new_value":"{\"userId\":337,\"ticketId\":\"AE-UONQHVZ\",\"answers\":[{\"questionId\":1,\"answer\":\"No\"},{\"questionId\":2,\"answer\":\"dsf\"},{\"questionId\":3,\"answer\":\"Yes\"},{\"questionId\":4,\"answer\":\"fds\"},{\"questionId\":5,\"answer\":\"sdf\"},{\"questionId\":6,\"answer\":\"fsd\"},{\"questionId\":7,\"answer\":\"sfd\"}]}","old_value":"null","operation":"SUBMIT","table_name":"eCRF Answers","user":"<EMAIL>"},"timestamp":"2025-01-28 03:36:07"}
{"level":"info","message":{"api_url":"/incident_report/aesi-responses","browser":"Chrome 131.0.0","description":"No Reason Provided","ip_address":"::1","method":"POST","new_value":"{\"ticket_id\":\"AE-UONQHVZ\",\"formattedResponses\":[{\"ticket_id\":\"AE-UONQHVZ\",\"question_id\":1,\"option_id\":1,\"description\":\"\"},{\"ticket_id\":\"AE-UONQHVZ\",\"question_id\":3,\"option_id\":\"\",\"description\":\"dfsdf\"},{\"ticket_id\":\"AE-UONQHVZ\",\"question_id\":4,\"option_id\":6,\"description\":\"\"},{\"ticket_id\":\"AE-UONQHVZ\",\"question_id\":5,\"option_id\":\"\",\"description\":\"dsf\"}]}","old_value":"null","operation":"CREATE","table_name":"AESI Reponses","user":"<EMAIL>"},"timestamp":"2025-01-28 03:36:31"}
{"level":"info","message":{"api_url":"/app_survey/submitscalequestionresponse","browser":"Chrome 131.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"submit survey response","user":"<EMAIL>"},"timestamp":"2025-01-28 20:44:51"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-28 20:44:52"}
{"level":"info","message":{"api_url":"/app_survey/submitscalequestionresponse","browser":"Chrome 131.0.0","ip_address":"::ffff:127.0.0.1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"submit survey response","user":"<EMAIL>"},"timestamp":"2025-01-28 20:52:34"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-28 20:52:34"}
