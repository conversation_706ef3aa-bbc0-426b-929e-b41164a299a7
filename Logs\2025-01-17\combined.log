{"level":"info","message":{"api_url":"/organization/logout","browser":"Chrome 131.0.0","description":"User logged out successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"LOGOUT","table_name":"N/A","user":"<EMAIL>"},"timestamp":"2025-01-17 00:55:36"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-01-17 00:55:45"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 131.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-01-17 00:56:04"}
{"level":"info","message":{"api_url":"/organization/logout","browser":"Chrome 131.0.0","description":"User logged out successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"LOGOUT","table_name":"N/A","user":"<EMAIL>"},"timestamp":"2025-01-17 01:40:16"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 131.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-01-17 01:40:26"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Other 0.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-01-17 01:46:04"}
{"level":"info","message":{"api_url":"/medicine/submitMedicineRecord","browser":"Other 0.0.0","description":"No Reason Provided","ip_address":"::1","method":"POST","new_value":"{\"medicine_id\":183,\"intake_quantity\":\"1\",\"user_id\":413,\"study_id\":1,\"date\":\"1/17/2025\",\"time\":\"4:35 PM\"}","old_value":"null","operation":"SUBMIT","table_name":"medicine record","user":"<EMAIL>"},"timestamp":"2025-01-17 03:42:25"}
