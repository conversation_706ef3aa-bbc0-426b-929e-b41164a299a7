const mysql = require("mysql2/promise");

const dbConfig = {
  // ====================Test Live=====================
  // host: "***********",
  // user: "root",
  // password: "l51Qh6kM2vb3npALukrKNMzNAlBogTj0NSH4Gd3IxqMfaP0qfFkp54e7jcknqGNX",
  // database: "live_env_test_research_hero",
  // port: 3306,

  // validation 2
  // host: "***********",
  // user: "root",
  // password: "l51Qh6kM2vb3npALukrKNMzNAlBogTj0NSH4Gd3IxqMfaP0qfFkp54e7jcknqGNX",
  // database: "validation_research_hero_2.0",
  // port: 3306,

  // host: "localhost",
  // user: "root",
  // password: "",
  // database: "research_hero",
  // port: 3306,

  // ============= New Envrioment FOr testing purpose =================
  host: "***********",
  user: "root",
  password: "l51Qh6kM2vb3npALukrKNMzNAlBogTj0NSH4Gd3IxqMfaP0qfFkp54e7jcknqGNX",
  database: "v2_database",
  port: 3306,
};

const pool = mysql.createPool(dbConfig);

module.exports = pool;
