{"level":"info","message":{"api_url":"/organization/logout","browser":"Chrome 134.0.0","description":"User logged out successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"LOGOUT","table_name":"N/A","user":"<EMAIL>"},"timestamp":"2025-03-19 00:54:09"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 134.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-19 00:54:17"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-19 00:57:49"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-19 00:57:49"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","description":"New organization created","ip_address":"::1","method":"POST","new_value":"{\"first_name\":\"ds\",\"last_name\":\"sdf\",\"middle_name\":\"\",\"email\":\"<EMAIL>\",\"status\":\"Screened\",\"gender\":\"male\",\"address\":\"msrdab\",\"contact_number\":\"10000000000\",\"date_of_birth\":\"01/01/1999\",\"stipend\":\"1\",\"study_enrolled_ids\":\"3\",\"notification\":\"some notification\",\"note\":\"Note for admin\",\"role_id\":\"10\",\"organization_detail_id\":\"1\",\"ecrf_id\":\"A101-010\",\"timezone\":\"Asia/Karachi\"}","old_value":"null","operation":"CREATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-19 00:57:52"}
{"level":"info","message":{"api_url":"/organization/logout","browser":"Chrome 134.0.0","description":"User logged out successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"LOGOUT","table_name":"N/A","user":"<EMAIL>"},"timestamp":"2025-03-19 00:58:11"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 134.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-19 00:58:26"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/457","browser":"Chrome 134.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-03-19 00:58:50"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-19 00:58:51"}
{"level":"info","message":{"api_url":"/schedule/createSchedule","browser":"Other 0.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-03-19 00:58:52"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-19 00:58:53"}
{"level":"info","message":{"api_url":"/schedule/createSchedule","browser":"Other 0.0.0","description":"Schedule created successfully for user 569","ip_address":"::1","method":"POST","new_value":"{}","old_value":"null","operation":"CREATE","table_name":"Schedule","user":"<EMAIL>"},"timestamp":"2025-03-19 00:59:01"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/457","browser":"Chrome 134.0.0","description":"Auto-schedule created for user 569","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":569,\"schedule\":{\"schedule_date\":\"2025-03-18\",\"schedule_time\":\"09:00\",\"study_enrolled_id\":\"3\",\"status\":\"Scheduled\",\"user_id\":569,\"note\":\"Auto-Created Schedule\"}}","old_value":"null","operation":"SCHEDULE_CREATED","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-03-19 00:59:03"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/457","browser":"Chrome 134.0.0","description":"Welcome email sent to ds sdf (<EMAIL>)","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":569,\"email\":\"<EMAIL>\",\"subject\":\"Welcome to ResearchHero!\"}","old_value":"null","operation":"EMAIL_SENT","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-03-19 00:59:04"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/457","browser":"Chrome 134.0.0","description":"Registration status updated to Accepted for ID 457","ip_address":"::1","method":"PUT","new_value":"{\"id\":\"457\",\"status\":\"Accepted\",\"reason\":\"Accepted! fcxcvxcvxvxcvxcv\",\"user_id\":569}","old_value":"{}","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-03-19 00:59:04"}
{"level":"info","message":{"api_url":"/organization/logout","browser":"Chrome 134.0.0","description":"User logged out successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"LOGOUT","table_name":"N/A","user":"<EMAIL>"},"timestamp":"2025-03-19 00:59:10"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 134.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-19 00:59:21"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1805","browser":"Chrome 134.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-03-19 01:01:52"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-19 01:01:53"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1805","browser":"Chrome 134.0.0","description":"Schedule scheduled with ID: 1805","ip_address":"::1","method":"PUT","new_value":"{\"schedule_date\":\"2025-03-19\"}","old_value":"{\"schedule_date\":\"2025-03-17T19:00:00.000Z\"}","operation":"UPDATE","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-03-19 01:01:56"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1805","browser":"Chrome 134.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-03-19 01:02:36"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-19 01:02:36"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1805","browser":"Chrome 134.0.0","description":"Schedule scheduled with ID: 1805","ip_address":"::1","method":"PUT","new_value":"{\"schedule_date\":\"2025-03-20\"}","old_value":"{\"schedule_date\":\"2025-03-18T19:00:00.000Z\"}","operation":"UPDATE","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-03-19 01:02:39"}
{"level":"info","message":{"api_url":"/organization/logout","browser":"Chrome 134.0.0","description":"User logged out successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"LOGOUT","table_name":"N/A","user":"<EMAIL>"},"timestamp":"2025-03-19 01:06:20"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 134.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-19 01:06:29"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 134.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-19 20:32:06"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1805","browser":"Chrome 134.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-03-19 23:16:02"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-19 23:16:02"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1805","browser":"Chrome 134.0.0","description":"Schedule scheduled with ID: 1805","ip_address":"::1","method":"PUT","new_value":"{\"schedule_date\":\"2025-03-20\"}","old_value":"{\"schedule_date\":\"2025-03-19T19:00:00.000Z\"}","operation":"UPDATE","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-03-19 23:16:05"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1805","browser":"Chrome 134.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-03-19 23:16:07"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-19 23:16:07"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1805","browser":"Chrome 134.0.0","description":"Schedule scheduled with ID: 1805","ip_address":"::1","method":"PUT","new_value":"{\"schedule_date\":\"2025-03-20\"}","old_value":"{\"schedule_date\":\"2025-03-19T19:00:00.000Z\"}","operation":"UPDATE","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-03-19 23:16:08"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1805","browser":"Chrome 134.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-03-19 23:16:32"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-19 23:16:32"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1805","browser":"Chrome 134.0.0","description":"Schedule scheduled with ID: 1805","ip_address":"::1","method":"PUT","new_value":"{\"schedule_date\":\"2025-03-22\"}","old_value":"{\"schedule_date\":\"2025-03-19T19:00:00.000Z\"}","operation":"UPDATE","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-03-19 23:16:33"}
