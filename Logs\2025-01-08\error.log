{"level":"error","message":"Failed to decode token or email is missing","timestamp":"2025-01-08 17:20:38"}
{"level":"error","message":"Failed to decode token or email is missing","timestamp":"2025-01-08 17:20:38"}
{"level":"error","message":"Failed to decode token or email is missing","timestamp":"2025-01-08 17:20:38"}
{"level":"error","message":"Failed to decode token or email is missing","timestamp":"2025-01-08 17:20:38"}
{"level":"error","message":"Failed to decode token or email is missing","timestamp":"2025-01-08 17:59:57"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-08 18:00:32"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-08 18:01:50"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-08 18:09:58"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-08 18:10:44"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-08 18:11:17"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-08 18:11:59"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-08 18:25:17"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-08 18:26:21"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-08 18:29:29"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-08 18:35:18"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-08 18:39:35"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-08 18:41:03"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-08 18:42:09"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-08 18:42:13"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-08 22:33:04"}
