{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 135.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-05-14 18:51:57"}
{"level":"info","message":{"api_url":"/organization/createPersonnel","browser":"Chrome 135.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-05-14 21:40:48"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-05-14 21:40:48"}
{"level":"info","message":{"api_url":"/organization/createPersonnel","browser":"Chrome 135.0.0","description":"Personnel created with email: <EMAIL>","ip_address":"::1","method":"POST","new_value":"{\"userId\":547}","old_value":"null","operation":"CREATE","table_name":"personnel","user":"<EMAIL>"},"timestamp":"2025-05-14 21:41:08"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/456","browser":"Chrome 135.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-05-14 21:42:06"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-05-14 21:42:06"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/456","browser":"Chrome 135.0.0","description":"Welcome email sent to jjj jj (<EMAIL>)","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":547,\"email\":\"<EMAIL>\",\"subject\":\"Welcome to ResearchHero!\"}","old_value":"null","operation":"EMAIL_SENT","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-05-14 21:42:11"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/456","browser":"Chrome 135.0.0","description":"Registration status updated to Accepted for ID 456","ip_address":"::1","method":"PUT","new_value":"{\"status\":\"Accepted\",\"reason\":\"Accepted By Super Admin \",\"user_id\":547}","old_value":"{}","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-05-14 21:42:11"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/456","browser":"Chrome 135.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-05-14 21:42:44"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-05-14 21:42:44"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/456","browser":"Chrome 135.0.0","description":"Registration status updated to Blocked for ID 456","ip_address":"::1","method":"PUT","new_value":"{\"status\":\"locked\",\"reason\":\"Accepted By Super Admin \",\"user_id\":547}","old_value":"{}","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-05-14 21:42:46"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/456","browser":"Chrome 135.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-05-14 21:42:56"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-05-14 21:42:56"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/456","browser":"Chrome 135.0.0","description":"Welcome email sent to jjj jj (<EMAIL>)","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":547,\"email\":\"<EMAIL>\",\"subject\":\"Welcome to ResearchHero!\"}","old_value":"null","operation":"EMAIL_SENT","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-05-14 21:43:01"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/456","browser":"Chrome 135.0.0","description":"Registration status updated to Accepted for ID 456","ip_address":"::1","method":"PUT","new_value":"{\"status\":\"Accepted\",\"reason\":\"Accepted By Super Admin \",\"user_id\":547}","old_value":"{}","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-05-14 21:43:01"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/456","browser":"Chrome 135.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-05-14 21:58:07"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-05-14 21:58:08"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/456","browser":"Chrome 135.0.0","description":"Registration status updated to Blocked for ID 456","ip_address":"::1","method":"PUT","new_value":"{\"status\":\"locked\",\"reason\":\"Accepted By Super Admin \",\"user_id\":547}","old_value":"{}","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-05-14 21:58:09"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/456","browser":"Chrome 135.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-05-14 21:58:20"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-05-14 21:58:21"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/456","browser":"Chrome 135.0.0","description":"Failed to create auto-schedule: role_id is not defined","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":547,\"error\":\"role_id is not defined\"}","old_value":"null","operation":"SCHEDULE_ERROR","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-05-14 21:58:24"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/456","browser":"Chrome 135.0.0","description":"Welcome email sent to jjj jj (<EMAIL>)","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":547,\"email\":\"<EMAIL>\",\"subject\":\"Welcome to ResearchHero!\"}","old_value":"null","operation":"EMAIL_SENT","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-05-14 21:58:29"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/456","browser":"Chrome 135.0.0","description":"Registration status updated to Accepted for ID 456","ip_address":"::1","method":"PUT","new_value":"{\"status\":\"Accepted\",\"reason\":\"Accepted By Super Admin \",\"user_id\":547}","old_value":"{}","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-05-14 21:58:29"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/456","browser":"Chrome 135.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-05-14 22:00:26"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-05-14 22:00:26"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/456","browser":"Chrome 135.0.0","description":"Registration status updated to Blocked for ID 456","ip_address":"::1","method":"PUT","new_value":"{\"status\":\"locked\",\"reason\":\"Accepted By Super Admin \",\"user_id\":547}","old_value":"{}","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-05-14 22:00:27"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/456","browser":"Chrome 135.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-05-14 22:01:16"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-05-14 22:01:17"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/456","browser":"Chrome 135.0.0","description":"Failed to create auto-schedule: role_id is not defined","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":547,\"error\":\"role_id is not defined\"}","old_value":"null","operation":"SCHEDULE_ERROR","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-05-14 22:01:19"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/456","browser":"Chrome 135.0.0","description":"Welcome email sent to jjj jj (<EMAIL>)","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":547,\"email\":\"<EMAIL>\",\"subject\":\"Welcome to ResearchHero!\"}","old_value":"null","operation":"EMAIL_SENT","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-05-14 22:01:21"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/456","browser":"Chrome 135.0.0","description":"Registration status updated to Accepted for ID 456","ip_address":"::1","method":"PUT","new_value":"{\"status\":\"Accepted\",\"reason\":\"Accepted By Super Admin \",\"user_id\":547}","old_value":"{}","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-05-14 22:01:21"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/456","browser":"Chrome 135.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-05-14 22:03:09"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-05-14 22:03:10"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/456","browser":"Chrome 135.0.0","description":"Registration status updated to Blocked for ID 456","ip_address":"::1","method":"PUT","new_value":"{\"status\":\"locked\",\"reason\":\"Accepted By Super Admin \",\"user_id\":547}","old_value":"{}","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-05-14 22:03:11"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/456","browser":"Chrome 135.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-05-14 22:03:17"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-05-14 22:03:17"}
{"level":"info","message":{"api_url":"/schedule/createSchedule","browser":"Other 0.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-05-14 22:03:19"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-05-14 22:03:19"}
{"level":"info","message":{"api_url":"/schedule/createSchedule","browser":"Other 0.0.0","description":"Schedule created successfully for user 547","ip_address":"::1","method":"POST","new_value":"{}","old_value":"null","operation":"CREATE","table_name":"Schedule","user":"<EMAIL>"},"timestamp":"2025-05-14 22:03:26"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/456","browser":"Chrome 135.0.0","description":"Auto-schedule created for user 547","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":547,\"schedule\":{\"schedule_date\":\"2025-05-14\",\"schedule_time\":\"09:00\",\"study_enrolled_id\":\"1\",\"status\":\"Scheduled\",\"user_id\":547,\"note\":\"Auto-Created Schedule\"}}","old_value":"null","operation":"SCHEDULE_CREATED","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-05-14 22:03:28"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/456","browser":"Chrome 135.0.0","description":"Welcome email sent to jjj jj (<EMAIL>)","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":547,\"email\":\"<EMAIL>\",\"subject\":\"Welcome to ResearchHero!\"}","old_value":"null","operation":"EMAIL_SENT","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-05-14 22:03:29"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/456","browser":"Chrome 135.0.0","description":"Registration status updated to Accepted for ID 456","ip_address":"::1","method":"PUT","new_value":"{\"status\":\"Accepted\",\"reason\":\"Accepted By Super Admin \",\"user_id\":547}","old_value":"{}","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-05-14 22:03:29"}
{"level":"error","message":"Failed to log to database or Excel: Corrupted zip or bug: expected 16 records in central dir, got 0","timestamp":"2025-05-14 22:03:30"}
{"level":"error","message":"Failed to log to database or Excel: Corrupted zip or bug: expected 16 records in central dir, got 0","timestamp":"2025-05-14 22:03:30"}
