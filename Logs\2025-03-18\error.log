{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-18 21:05:29"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-18 21:06:27"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-18 21:08:29"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-18 21:09:48"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-18 21:10:26"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-18 21:10:29"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-18 21:25:33"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-18 21:26:09"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-18 21:26:12"}
{"level":"error","message":"Failed to log to database or Excel: Corrupted zip or bug: expected 16 records in central dir, got 0","timestamp":"2025-03-18 21:26:26"}
{"level":"error","message":"Failed to log to database or Excel: Corrupted zip or bug: expected 16 records in central dir, got 0","timestamp":"2025-03-18 21:26:26"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-18 21:40:59"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-18 21:41:39"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-18 21:41:42"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-18 21:43:33"}
{"level":"error","message":"Failed to log to database or Excel: Cannot read properties of undefined (reading 'headers')","timestamp":"2025-03-18 21:43:36"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-18 21:44:20"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-18 21:44:39"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-18 23:01:44"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-18 23:04:29"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-18 23:04:59"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-18 23:26:05"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-18 23:26:31"}
{"level":"error","message":"Failed to log to database or Excel: Corrupted zip or bug: expected 16 records in central dir, got 0","timestamp":"2025-03-18 23:26:36"}
{"level":"error","message":"Failed to log to database or Excel: Corrupted zip or bug: expected 16 records in central dir, got 0","timestamp":"2025-03-18 23:26:36"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-18 23:34:19"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-18 23:34:43"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-18 23:36:54"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-18 23:38:14"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-18 23:38:50"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-18 23:38:53"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-18 23:39:19"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-18 23:39:21"}
{"level":"error","message":"Failed to log to database or Excel: Corrupted zip or bug: expected 16 records in central dir, got 0","timestamp":"2025-03-18 23:39:34"}
{"level":"error","message":"Failed to log to database or Excel: Corrupted zip or bug: expected 16 records in central dir, got 0","timestamp":"2025-03-18 23:39:34"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-18 23:40:11"}
{"level":"error","message":"Failed to log to database or Excel: Cannot read properties of undefined (reading 'headers')","timestamp":"2025-03-18 23:40:14"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-18 23:40:30"}
{"level":"error","message":"Failed to log to database or Excel: Cannot read properties of undefined (reading 'headers')","timestamp":"2025-03-18 23:40:33"}
