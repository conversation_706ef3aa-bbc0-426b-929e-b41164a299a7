{"level":"info","message":"{\"method\":\"POST\",\"api_url\":\"/auth/signin\",\"table_name\":\"Organization\",\"operation\":\"SIGNIN_ATTEMPT\",\"description\":\"Sign-in attempt failed: Invalid email or password\",\"old_value\":\"{\\\"email\\\":\\\"<EMAIL>\\\"}\",\"new_value\":null,\"browser\":\"Chrome 135.0.0\",\"ip_address\":\"::1\",\"user\":\"<EMAIL>\"}","timestamp":"2025-04-30 12:06:53"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 135.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-04-30 12:07:08"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 135.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-04-30 12:09:19"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/566","browser":"Chrome 135.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-04-30 12:11:15"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-04-30 12:11:15"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/566","browser":"Chrome 135.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-04-30 12:11:36"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/566","browser":"Chrome 135.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-04-30 12:11:36"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-04-30 12:11:37"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-04-30 12:11:37"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/566","browser":"Chrome 135.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-04-30 12:12:41"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-04-30 12:12:41"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/566","browser":"Chrome 135.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-04-30 12:13:34"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-04-30 12:13:34"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/566","browser":"Chrome 135.0.0","description":"saaaaaaaaaaaaaasa","ip_address":"::1","method":"PUT","new_value":"{\"reason\":\"saaaaaaaaaaaaaasa\"}","old_value":"{\"notification\":\"some notification\",\"user_id\":604}","operation":"UPDATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-04-30 12:13:36"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/566","browser":"Chrome 135.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-04-30 13:03:43"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-04-30 13:03:43"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/566","browser":"Chrome 135.0.0","description":"saaaaaaaaaaaaaasa","ip_address":"::1","method":"PUT","new_value":"{\"first_name\":\"stITMANAGERa\",\"reason\":\"saaaaaaaaaaaaaasa\"}","old_value":"{\"first_name\":\"stITMANAGER\",\"notification\":null,\"user_id\":604}","operation":"UPDATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-04-30 13:03:45"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/566","browser":"Chrome 135.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-04-30 13:04:15"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-04-30 13:04:15"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/566","browser":"Chrome 135.0.0","description":"saaaaaaaaaaaaaasa","ip_address":"::1","method":"PUT","new_value":"{\"first_name\":\"asdad\",\"reason\":\"saaaaaaaaaaaaaasa\"}","old_value":"{\"first_name\":\"stITMANAGERa\",\"notification\":null,\"user_id\":604}","operation":"UPDATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-04-30 13:04:17"}
