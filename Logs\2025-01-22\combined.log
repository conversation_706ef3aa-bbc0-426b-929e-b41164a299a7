{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 131.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-01-22 02:09:05"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Other 0.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-01-22 02:17:03"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-01-22 02:24:39"}
{"level":"info","message":{"api_url":"/organization/logout","browser":"Chrome 131.0.0","description":"User logged out successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"LOGOUT","table_name":"N/A","user":"<EMAIL>"},"timestamp":"2025-01-22 02:35:24"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-01-22 02:35:29"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 131.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-01-22 02:36:04"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 131.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-01-22 02:37:39"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-22 02:37:39"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 131.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-01-22 02:38:45"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-22 02:38:45"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 131.0.0","description":"New organization created","ip_address":"::1","method":"POST","new_value":"{\"first_name\":\"Anas\",\"last_name\":\"khan\",\"middle_name\":\"\",\"email\":\"<EMAIL>\",\"status\":\"Screened\",\"gender\":\"male\",\"address\":\"Mohallah: Boki kheil,Garhi Ismail zai Garhi Kapura Mardan\",\"contact_number\":\"17134846947\",\"date_of_birth\":\"02/04/1998\",\"stipend\":\"0\",\"study_enrolled_ids\":\"3\",\"notification\":\"some notification\",\"note\":\"Note for admin\",\"role_id\":\"10\",\"organization_detail_id\":\"1\",\"ecrf_id\":\"A101-909\"}","old_value":"null","operation":"CREATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-01-22 02:38:47"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/329","browser":"Chrome 131.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-01-22 02:39:22"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-22 02:39:23"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/329","browser":"Chrome 131.0.0","description":"Welcome email sent to Anas khan (<EMAIL>)","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":432,\"email\":\"<EMAIL>\",\"subject\":\"Welcome to ResearchHero!\"}","old_value":"null","operation":"EMAIL_SENT","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-01-22 02:39:25"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/329","browser":"Chrome 131.0.0","description":"Registration status updated to Accepted for ID 329","ip_address":"::1","method":"PUT","new_value":"{\"id\":\"329\",\"status\":\"Accepted\",\"reason\":\"Accepted By Super Admin \",\"user_id\":432}","old_value":"[{\"account_status_id\":329,\"user_id\":432,\"account_status\":\"Pending\",\"reason\":\"Initial registration\",\"first_time\":\"1\",\"updated_at\":\"2025-01-22T08:38:47.000Z\"}]","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-01-22 02:39:26"}
{"level":"info","message":{"api_url":"/schedule/createSchedule","browser":"Chrome 131.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-01-22 02:39:26"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-22 02:39:26"}
{"level":"info","message":{"api_url":"/schedule/createSchedule","browser":"Chrome 131.0.0","description":"Schedule created successfully for user 432","ip_address":"::1","method":"POST","new_value":"{}","old_value":"null","operation":"CREATE","table_name":"Schedule","user":"<EMAIL>"},"timestamp":"2025-01-22 02:39:32"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1468","browser":"Chrome 131.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-01-22 02:40:10"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-22 02:40:10"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1468","browser":"Chrome 131.0.0","description":"Auto Create Medicine","ip_address":"::1","method":"PUT","new_value":"{\"medication_name\":\"Sunobinop Or Placebo\",\"dosage\":\"0.5mg or 1.0mg or 2.0mg\",\"dosage_times\":[\"09:00 PM\"],\"frequencyType\":\"QD\",\"frequencyTime\":\"N/A\",\"frequencyCondition\":\"At Bedtime\",\"dosageType\":\"Tablet\",\"allot_medicine\":\"1\",\"route\":\"Oral\",\"note\":\"Auto-created medicine\",\"user_id\":432,\"investigator_id\":0,\"tracker_time\":\"2025-01-22T08:40:12.178Z\"}","old_value":"null","operation":"CREATE","table_name":"Auto Create Medicine Due to Subject Screening Complete","user":"<EMAIL>"},"timestamp":"2025-01-22 02:40:12"}
{"level":"error","message":"Failed to log to database: Cannot read properties of undefined (reading 'headers')","timestamp":"2025-01-22 02:40:13"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1468","browser":"Chrome 131.0.0","description":"Schedule completed with ID: 1468","ip_address":"::1","method":"PUT","new_value":"{\"schedule_date\":\"2025-01-22\",\"schedule_time\":\"09:00\",\"status\":\"Completed\",\"note\":\"Auto-created schedule\",\"user_id\":432}","old_value":"{\"schedule_date\":\"2025-01-22T06:00:00.000Z\",\"schedule_time\":\"09:00\",\"status\":\"Scheduled\",\"note\":\"Auto-created schedule\",\"user_id\":432}","operation":"UPDATE","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-01-22 02:40:18"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 131.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-01-22 20:47:47"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-01-22 21:40:20"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Other 0.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-01-22 21:40:47"}
