{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-10 16:06:47"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-10 16:12:06"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-10 16:17:25"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-10 16:20:51"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-10 16:23:13"}
{"level":"error","message":"Failed to log to database: Data too long for column 'description' at row 1","timestamp":"2025-01-10 16:23:14"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-01-10 21:31:50"}
