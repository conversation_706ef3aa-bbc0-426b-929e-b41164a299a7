{"level":"info","message":{"api_url":"/organization/deleteOrganization/425","browser":"Chrome 132.0.0","ip_address":"::1","method":"DELETE","new_value":"null","old_value":"null","operation":"delete","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-02-05 09:38:26"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-05 09:38:26"}
{"level":"info","message":{"api_url":"/organization/deleteOrganization/425","browser":"Chrome 132.0.0","ip_address":"::1","method":"DELETE","new_value":"null","old_value":"null","operation":"delete","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-02-05 09:42:44"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-05 09:42:44"}
{"level":"info","message":{"api_url":"/organization/deleteOrganization/425","browser":"Chrome 132.0.0","ip_address":"::1","method":"DELETE","new_value":"null","old_value":"null","operation":"delete","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-02-05 09:45:26"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-05 09:45:26"}
{"level":"info","message":{"api_url":"/organization/deleteOrganization/425","browser":"Chrome 132.0.0","ip_address":"::1","method":"DELETE","new_value":"null","old_value":"null","operation":"delete","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-02-05 09:50:18"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-05 09:50:18"}
{"level":"info","message":{"api_url":"/organization/deleteOrganization/425","browser":"Chrome 132.0.0","ip_address":"::1","method":"DELETE","new_value":"null","old_value":"null","operation":"delete","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-02-05 09:55:21"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-05 09:55:21"}
{"level":"info","message":{"api_url":"/organization/deleteOrganization/425","browser":"Chrome 132.0.0","ip_address":"::1","method":"DELETE","new_value":"null","old_value":"null","operation":"delete","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-02-05 09:59:19"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-05 09:59:19"}
{"level":"info","message":{"api_url":"/organization/deleteOrganization/425","browser":"Chrome 132.0.0","ip_address":"::1","method":"DELETE","new_value":"null","old_value":"null","operation":"delete","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-02-05 10:00:00"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-05 10:00:01"}
{"level":"info","message":{"api_url":"/organization/deleteOrganization/425","browser":"Chrome 132.0.0","description":"Accepted By Super Admin ","ip_address":"::1","method":"DELETE","new_value":"null","old_value":"{\"organization_id\":394,\"first_name\":\"adfadsf\",\"middle_name\":null,\"last_name\":\"adskjfalkdsf\",\"status\":\"Screened\",\"is_randomized\":0,\"is_compliant\":1,\"gender\":\"male\",\"address\":\"adakfjaldsf\",\"timezone\":\"UTC\",\"contact_number\":\"18172983798\",\"date_of_birth\":\"03/04/1990\",\"stipend\":\"0\",\"image\":null,\"study_enrolled_id\":\"1\",\"date_enrolled\":\"12/21/2024\",\"notification\":\"some notification\",\"user_id\":425,\"organization_detail_id\":1,\"role_id\":10,\"ecrf_id\":\"AA71-982\",\"email\":\"<EMAIL>\",\"organization_name\":\"Dr Prabhu Manjeshwar\",\"organization_address\":\"7080 Southwest Freeway Houston TX 77074o\",\"note\":\"Note for admin\",\"enrolled_ids\":\"1\",\"study_names\":\"SUN2003A - 102\",\"investigator_user_ids\":\"337,346,347,401,406\",\"study_enrolled\":[{\"id\":1,\"name\":\"SUN2003A - 102\"}],\"investigators\":[{\"user_id\":337,\"first_name\":\"Aurelie\",\"last_name\":\"Foray\"},{\"user_id\":346,\"first_name\":\"Taufeeq\",\"last_name\":\"khan\"},{\"user_id\":347,\"first_name\":\"Dr Mudassar\",\"last_name\":\"Hassan\"},{\"user_id\":401,\"first_name\":\"Dr Mudassar\",\"last_name\":\"Hassan\"},{\"user_id\":406,\"first_name\":\"\",\"last_name\":\"\"}]}","operation":"DELETE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-02-05 10:00:02"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1610","browser":"Chrome 132.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-02-05 10:39:19"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-05 10:39:19"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1610","browser":"Chrome 132.0.0","description":"Schedule scheduled with ID: 1610","ip_address":"::1","method":"PUT","new_value":"{\"schedule_date\":\"2025-04-15\",\"schedule_time\":\"09:00\",\"status\":\"Scheduled\",\"note\":\"Auto-scheduled for Safety Follow-up Day 64\",\"user_id\":446}","old_value":"{\"schedule_date\":\"2025-04-14T19:00:00.000Z\",\"schedule_time\":\"09:00\",\"status\":\"Pending\",\"note\":\"Auto-scheduled for Safety Follow-up Day 64\",\"user_id\":446}","operation":"UPDATE","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-02-05 10:39:21"}
{"level":"info","message":{"api_url":"/schedule/deleteSchedule/1544","browser":"Chrome 132.0.0","ip_address":"::1","method":"DELETE","new_value":"null","old_value":"null","operation":"delete","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-02-05 10:43:39"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-05 10:43:39"}
{"level":"info","message":{"api_url":"/schedule/deleteSchedule/1544","browser":"Chrome 132.0.0","ip_address":"::ffff:127.0.0.1","method":"DELETE","new_value":"null","old_value":"null","operation":"delete","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-02-05 10:43:59"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-05 10:44:00"}
{"level":"info","message":{"api_url":"/schedule/deleteSchedule/1544","browser":"Chrome 132.0.0","ip_address":"::1","method":"DELETE","new_value":"null","old_value":"null","operation":"delete","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-02-05 10:44:54"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-05 10:44:54"}
{"level":"info","message":{"api_url":"/schedule/deleteSchedule/1544","browser":"Chrome 132.0.0","ip_address":"::1","method":"DELETE","new_value":"null","old_value":"null","operation":"delete","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-02-05 10:50:22"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-05 10:50:23"}
{"level":"info","message":{"api_url":"/schedule/deleteSchedule/1527","browser":"Chrome 132.0.0","ip_address":"::1","method":"DELETE","new_value":"null","old_value":"null","operation":"delete","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-02-05 10:53:27"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-05 10:53:34"}
{"level":"info","message":{"api_url":"/schedule/deleteSchedule/1525","browser":"Chrome 132.0.0","ip_address":"::1","method":"DELETE","new_value":"null","old_value":"null","operation":"delete","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-02-05 10:54:30"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-05 10:54:31"}
{"level":"info","message":{"api_url":"/schedule/deleteSchedule/1526","browser":"Chrome 132.0.0","ip_address":"::1","method":"DELETE","new_value":"null","old_value":"null","operation":"delete","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-02-05 10:59:53"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-05 10:59:53"}
{"level":"info","message":{"api_url":"/schedule/deleteSchedule/1526","browser":"Chrome 132.0.0","ip_address":"::1","method":"DELETE","new_value":"null","old_value":"null","operation":"delete","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-02-05 11:00:56"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-05 11:00:56"}
{"level":"info","message":{"api_url":"/schedule/deleteSchedule/1526","browser":"Chrome 132.0.0","description":"deleted","ip_address":"::1","method":"DELETE","new_value":"null","old_value":"{\"schedule_id\":1526,\"study_enrolled_id\":\"2\",\"schedule_date\":\"2025-02-12T19:00:00.000Z\",\"schedule_time\":\"09:00\",\"status\":\"Pending\",\"note\":\"Auto-scheduled for Double-blind Treatment Day 7\",\"disable_status\":\"Disable\",\"reason\":\"deleted\",\"user_id\":399,\"day_id\":23,\"rescheduled\":null,\"email\":\"<EMAIL>\",\"first_name\":\"TLFB\",\"last_name\":\"TEST_2\",\"user_status\":\"Screened\",\"gender\":\"male\",\"address\":\"address\",\"contact_number\":\"11231231231\",\"date_of_birth\":\"10/18/1993\",\"stipend\":\"0\",\"study_name\":\"SUN2003B\",\"notification\":\"some notification\",\"ecrf_id\":\"B101-004\"}","operation":"DELETE","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-02-05 11:00:57"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/209","browser":"Chrome 132.0.0","description":"Error updating medication: pool is not defined","ip_address":"::1","method":"PUT","new_value":"null","old_value":"{\"medication_id\":\"209\"}","operation":"UPDATE_ERROR","table_name":"Medication","user":"<EMAIL>"},"timestamp":"2025-02-05 11:10:36"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/207","browser":"Chrome 132.0.0","description":"Error updating medication: Column 'investigator_id' cannot be null","ip_address":"::1","method":"PUT","new_value":"null","old_value":"{\"medication_id\":\"207\"}","operation":"UPDATE_ERROR","table_name":"Medication","user":"<EMAIL>"},"timestamp":"2025-02-05 11:11:15"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/207","browser":"Chrome 132.0.0","description":"Error updating medication: Column 'investigator_id' cannot be null","ip_address":"::1","method":"PUT","new_value":"null","old_value":"{\"medication_id\":\"207\"}","operation":"UPDATE_ERROR","table_name":"Medication","user":"<EMAIL>"},"timestamp":"2025-02-05 11:13:52"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/209","browser":"Chrome 132.0.0","description":"Error updating medication: Column 'update_entity' cannot be null","ip_address":"::1","method":"PUT","new_value":"null","old_value":"{\"medication_id\":\"209\"}","operation":"UPDATE_ERROR","table_name":"Medication","user":"<EMAIL>"},"timestamp":"2025-02-05 11:15:04"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/209","browser":"Chrome 132.0.0","description":"Error updating medication: Column 'investigator_id' cannot be null","ip_address":"::ffff:127.0.0.1","method":"PUT","new_value":"null","old_value":"{\"medication_id\":\"209\"}","operation":"UPDATE_ERROR","table_name":"Medication","user":"<EMAIL>"},"timestamp":"2025-02-05 11:16:31"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/209","browser":"Chrome 132.0.0","description":"Error updating medication: Column 'update_entity' cannot be null","ip_address":"::1","method":"PUT","new_value":"null","old_value":"{\"medication_id\":\"209\"}","operation":"UPDATE_ERROR","table_name":"Medication","user":"<EMAIL>"},"timestamp":"2025-02-05 11:17:45"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/207","browser":"Chrome 132.0.0","description":"Error updating medication: Column 'investigator_id' cannot be null","ip_address":"::ffff:127.0.0.1","method":"PUT","new_value":"null","old_value":"{\"medication_id\":\"207\"}","operation":"UPDATE_ERROR","table_name":"Medication","user":"<EMAIL>"},"timestamp":"2025-02-05 11:18:21"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/209","browser":"Chrome 132.0.0","description":"Error updating medication: Column 'investigator_id' cannot be null","ip_address":"::1","method":"PUT","new_value":"null","old_value":"{\"medication_id\":\"209\"}","operation":"UPDATE_ERROR","table_name":"Medication","user":"<EMAIL>"},"timestamp":"2025-02-05 11:23:09"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/209","browser":"Chrome 132.0.0","description":"Error updating medication: Column 'investigator_id' cannot be null","ip_address":"::ffff:127.0.0.1","method":"PUT","new_value":"null","old_value":"{\"medication_id\":\"209\"}","operation":"UPDATE_ERROR","table_name":"Medication","user":"<EMAIL>"},"timestamp":"2025-02-05 11:23:51"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/209","browser":"Chrome 132.0.0","description":"Updated fields: note","ip_address":"::1","method":"PUT","new_value":"{\"note\":\"nothing herefghhhhhhhhhhhhhh hfggggggggggg fghhhhhhhhhhhhhhhhhhfgh fhggggggggg fhgggggggs sfdddddddddd\"}","old_value":"{\"note\":\"nothing here\"}","operation":"UPDATE","table_name":"Medication","user":"<EMAIL>"},"timestamp":"2025-02-05 11:24:30"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/209","browser":"Chrome 132.0.0","description":"Error updating medication: Column 'investigator_id' cannot be null","ip_address":"::1","method":"PUT","new_value":"null","old_value":"{\"medication_id\":\"209\"}","operation":"UPDATE_ERROR","table_name":"Medication","user":"<EMAIL>"},"timestamp":"2025-02-05 11:27:38"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/207","browser":"Chrome 132.0.0","description":"Error updating medication: Column 'investigator_id' cannot be null","ip_address":"::1","method":"PUT","new_value":"null","old_value":"{\"medication_id\":\"207\"}","operation":"UPDATE_ERROR","table_name":"Medication","user":"<EMAIL>"},"timestamp":"2025-02-05 11:28:25"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/207","browser":"Chrome 132.0.0","description":"Updated fields: note","ip_address":"::1","method":"PUT","new_value":"{\"note\":\"asdfasdfffdsfdsfsd\"}","old_value":"{\"note\":\"asdfasdf\"}","operation":"UPDATE","table_name":"Medication","user":"<EMAIL>"},"timestamp":"2025-02-05 11:31:52"}
{"level":"info","message":{"api_url":"/medicine/deleteMedication/209","browser":"Chrome 132.0.0","description":"deleted","ip_address":"::1","method":"DELETE","new_value":"null","old_value":"{\"medication_id\":209,\"created_at\":\"2025-01-27T10:04:57.000Z\",\"medication_name\":\"Sunobhinop\",\"medication_status\":\"Pending\",\"dosage\":\"01 or 2mg or 10mg\",\"dosage_time\":\"08:04 PM\",\"frequency_type\":\"QD\",\"frequency_time\":\"Morning\",\"frequency_condition\":\"Fasting\",\"dosageType\":\"Drops\",\"allot_medicine\":\"2\",\"route\":\"Oral\",\"note\":\"nothing herefghhhhhhhhhhhhhh hfggggggggggg fghhhhhhhhhhhhhhhhhhfgh fhggggggggg fhgggggggs sfdddddddddd\",\"date_of_birth\":\"01/01/1990\",\"gender\":\"male\",\"stipend\":\"0\",\"first_name\":\"gkajlkdjalk\",\"last_name\":\"akjsdlkfjasdfl\",\"address\":\"addres\",\"contact_number\":\"11232131231\",\"study_enrolled_id\":\"2\",\"status\":\"Randomized\",\"user_id\":427,\"email\":\"<EMAIL>\",\"study_name\":\"SUN2003B\"}","operation":"DELETE","table_name":"Medication","user":"<EMAIL>"},"timestamp":"2025-02-05 11:37:56"}
{"level":"info","message":{"api_url":"/medicine/deleteMedication/206","browser":"Chrome 132.0.0","description":"deleted","ip_address":"::1","method":"DELETE","new_value":"null","old_value":"{\"medication_id\":206,\"created_at\":\"2025-01-27T09:24:22.000Z\",\"medication_name\":\"asdfasd\",\"medication_status\":\"Pending\",\"dosage\":\"fasdfasdf\",\"dosage_time\":\"19:24\",\"frequency_type\":\"QD\",\"frequency_time\":\"Morning\",\"frequency_condition\":\"After Meal\",\"dosageType\":\"Powder\",\"allot_medicine\":\"1\",\"route\":\"Oral\",\"note\":\"asedfadsf\",\"date_of_birth\":\"03/01/1990\",\"gender\":\"male\",\"stipend\":\"0\",\"first_name\":\"some test\",\"last_name\":\"tlfb\",\"address\":\"some address\",\"contact_number\":\"11232132132\",\"study_enrolled_id\":\"2\",\"status\":\"Randomized\",\"user_id\":429,\"email\":\"<EMAIL>\",\"study_name\":\"SUN2003B\"}","operation":"DELETE","table_name":"Medication","user":"<EMAIL>"},"timestamp":"2025-02-05 11:59:54"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/207","browser":"Chrome 132.0.0","description":"Updated fields: note","ip_address":"::1","method":"PUT","new_value":"{\"note\":\"asdfasdfffdsfdsfsdsadasd\"}","old_value":"{\"note\":\"asdfasdfffdsfdsfsd\"}","operation":"UPDATE","table_name":"Medication","user":"<EMAIL>"},"timestamp":"2025-02-05 12:01:25"}
{"level":"info","message":{"api_url":"/schedule/deleteSchedule/1486","browser":"Chrome 132.0.0","ip_address":"::1","method":"DELETE","new_value":"null","old_value":"null","operation":"delete","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-02-05 12:03:50"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-05 12:03:50"}
{"level":"info","message":{"api_url":"/schedule/deleteSchedule/1486","browser":"Chrome 132.0.0","description":"deleted","ip_address":"::1","method":"DELETE","new_value":"null","old_value":"{\"schedule_id\":1486,\"study_enrolled_id\":\"1\",\"schedule_date\":\"2025-01-27T19:00:00.000Z\",\"schedule_time\":\"00:12\",\"status\":\"Scheduled\",\"note\":\"hjgj\",\"disable_status\":\"Enable\",\"reason\":\"Manual Schedule\",\"user_id\":428,\"day_id\":null,\"rescheduled\":null,\"email\":\"<EMAIL>\",\"first_name\":\"First\",\"last_name\":\"Tender\",\"user_status\":\"Randomized\",\"gender\":\"male\",\"address\":\"ajlsdjflakjsdf\",\"contact_number\":\"19812083901\",\"date_of_birth\":\"01/03/1990\",\"stipend\":\"0\",\"study_name\":\"SUN2003A - 102\",\"notification\":\"some notification\",\"ecrf_id\":\"BBB9-878\"}","operation":"DELETE","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-02-05 12:03:51"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1480","browser":"Chrome 132.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-02-05 12:05:23"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-05 12:05:23"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1609","browser":"Chrome 132.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-02-05 12:06:09"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-05 12:06:09"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1609","browser":"Chrome 132.0.0","description":"Schedule scheduled with ID: 1609","ip_address":"::1","method":"PUT","new_value":"{\"schedule_date\":\"2025-04-08\",\"schedule_time\":\"09:00\",\"status\":\"Scheduled\",\"note\":\"Auto-scheduled for Early Termination Unscheduled\",\"user_id\":446}","old_value":"{\"schedule_date\":\"2025-04-07T19:00:00.000Z\",\"schedule_time\":\"09:00\",\"status\":\"Pending\",\"note\":\"Auto-scheduled for Early Termination Unscheduled\",\"user_id\":446}","operation":"UPDATE","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-02-05 12:06:11"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/405","browser":"Chrome 132.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-02-05 13:22:38"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-05 13:22:38"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/405","browser":"Chrome 132.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-02-05 13:33:46"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-05 13:33:46"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/405","browser":"Chrome 132.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-02-05 13:37:09"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-05 13:37:09"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/403","browser":"Chrome 132.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-02-05 13:39:54"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-05 13:39:55"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/403","browser":"Chrome 132.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-02-05 13:41:51"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-05 13:41:52"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/403","browser":"Chrome 132.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-02-05 13:42:36"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-05 13:42:36"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/403","browser":"Chrome 132.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-02-05 13:45:42"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-05 13:45:42"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/405","browser":"Chrome 132.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-02-05 14:13:52"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-05 14:13:52"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/405","browser":"Chrome 132.0.0","description":"hahaha","ip_address":"::1","method":"PUT","new_value":"{\"first_name\":\"check\",\"middle_name\":\"\",\"last_name\":\"error\",\"status\":\"Screened\",\"gender\":\"male\",\"address\":\"5959 Bonhomme Rdsdssad\",\"contact_number\":\"12377292048\",\"stipend\":\"2\",\"study_enrolled_ids\":\"1\",\"notification\":\"some notification\",\"note\":\"Note for admin\",\"ecrf_id\":\"A123-443\",\"reason\":\"hahaha\"}","old_value":"{\"organization_id\":405,\"first_name\":\"check\",\"middle_name\":\"\",\"last_name\":\"error\",\"status\":\"Screened\",\"is_randomized\":0,\"is_compliant\":1,\"gender\":\"male\",\"address\":\"5959 Bonhomme Rdsds\",\"timezone\":\"UTC\",\"contact_number\":\"12377292048\",\"date_of_birth\":\"03/11/1998\",\"stipend\":\"2\",\"image\":null,\"study_enrolled_id\":\"1\",\"date_enrolled\":\"01/28/2025\",\"notification\":\"some notification\",\"user_id\":436,\"organization_detail_id\":1,\"role_id\":10,\"ecrf_id\":\"A123-443\",\"study_enrolled\":[{\"id\":1,\"name\":\"\"}]}","operation":"UPDATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-02-05 14:13:54"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/405","browser":"Chrome 132.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-02-05 14:15:10"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-05 14:15:11"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/405","browser":"Chrome 132.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-02-05 14:23:25"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-05 14:23:25"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/405","browser":"Chrome 132.0.0","description":"i update this who r you","ip_address":"::1","method":"PUT","new_value":"{\"address\":\"5959 Bonhomme Rd\",\"study_enrolled_ids\":\"1\",\"note\":\"Note for admin\",\"reason\":\"i update this who r you\"}","old_value":"{\"address\":\"5959 Bonhomme Rdsdssad\"}","operation":"UPDATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-02-05 14:23:26"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/405","browser":"Chrome 132.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-02-05 14:26:27"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-05 14:26:27"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/405","browser":"Chrome 132.0.0","description":"yes i update","ip_address":"::1","method":"PUT","new_value":"{\"address\":\"5959 Bonhomme Rd haha\",\"reason\":\"yes i update\"}","old_value":"{\"address\":\"5959 Bonhomme Rd\"}","operation":"UPDATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-02-05 14:26:29"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/207","browser":"Chrome 132.0.0","description":"Error updating medication: Column 'user_id' cannot be null","ip_address":"::1","method":"PUT","new_value":"null","old_value":"{\"medication_id\":\"207\"}","operation":"UPDATE_ERROR","table_name":"Medication","user":"<EMAIL>"},"timestamp":"2025-02-05 14:42:01"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/207","browser":"Chrome 132.0.0","description":"Error updating medication: Column 'user_id' cannot be null","ip_address":"::1","method":"PUT","new_value":"null","old_value":"{\"medication_id\":\"207\"}","operation":"UPDATE_ERROR","table_name":"Medication","user":"<EMAIL>"},"timestamp":"2025-02-05 14:43:52"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/207","browser":"Chrome 132.0.0","description":"Error updating medication: Column 'investigator_id' cannot be null","ip_address":"::1","method":"PUT","new_value":"null","old_value":"{\"medication_id\":\"207\"}","operation":"UPDATE_ERROR","table_name":"Medication","user":"<EMAIL>"},"timestamp":"2025-02-05 14:46:18"}
