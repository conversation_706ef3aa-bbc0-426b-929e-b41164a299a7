{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 133.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-02-10 13:19:36"}
{"level":"info","message":{"api_url":"/organization/logout","browser":"Chrome 133.0.0","description":"User logged out successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"LOGOUT","table_name":"N/A","user":"<EMAIL>"},"timestamp":"2025-02-10 13:20:24"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 133.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-02-10 13:21:11"}
{"level":"info","message":{"api_url":"/organization/logout","browser":"Chrome 133.0.0","description":"User logged out successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"LOGOUT","table_name":"N/A","user":"<EMAIL>"},"timestamp":"2025-02-10 14:03:40"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 133.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-02-10 14:03:50"}
{"level":"info","message":{"api_url":"/organization/logout","browser":"Chrome 133.0.0","description":"User logged out successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"LOGOUT","table_name":"N/A","user":"<EMAIL>"},"timestamp":"2025-02-10 14:04:29"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 133.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-02-10 14:04:38"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/417","browser":"Chrome 133.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-02-10 14:09:32"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-10 14:09:33"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/417","browser":"Chrome 133.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-02-10 14:10:52"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-10 14:10:52"}
