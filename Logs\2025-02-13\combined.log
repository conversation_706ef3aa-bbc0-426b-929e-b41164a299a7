{"level":"info","message":{"api_url":"/organization/logout","browser":"Chrome 133.0.0","description":"User logged out successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"LOGOUT","table_name":"N/A","user":"<EMAIL>"},"timestamp":"2025-02-13 11:52:13"}
{"level":"info","message":{"api_url":"/organization/logout","browser":"Chrome 133.0.0","description":"User logged out successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"LOGOUT","table_name":"N/A","user":"<EMAIL>"},"timestamp":"2025-02-13 11:52:14"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 133.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-02-13 12:33:18"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 133.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-02-13 12:37:54"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 133.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-02-13 12:58:30"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 133.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-02-13 13:22:09"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 133.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-02-13 13:29:05"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 133.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-02-13 14:09:27"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 133.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-02-13 14:42:34"}
{"level":"info","message":{"api_url":"/organization/logout","browser":"Chrome 133.0.0","description":"User logged out successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"LOGOUT","table_name":"N/A","user":"<EMAIL>"},"timestamp":"2025-02-13 14:42:59"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 133.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-02-13 14:43:03"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 133.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-02-13 15:01:51"}
{"level":"info","message":{"api_url":"/organization/logout","browser":"Chrome 133.0.0","description":"User logged out successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"LOGOUT","table_name":"N/A","user":"<EMAIL>"},"timestamp":"2025-02-13 15:02:10"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 133.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-02-13 15:02:31"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1515","browser":"Chrome 133.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-02-13 15:07:10"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-13 15:07:11"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1515","browser":"Chrome 133.0.0","description":"Schedule scheduled with ID: 1515","ip_address":"::1","method":"PUT","new_value":"{\"schedule_date\":\"2025-01-31\",\"note\":\"Auto-Created Schedule FOR THIS USER\"}","old_value":"{\"schedule_date\":\"2025-01-30T19:00:00.000Z\",\"note\":\"Auto-Created Schedule\"}","operation":"UPDATE","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-02-13 15:07:13"}
{"level":"info","message":{"api_url":"/organization/logout","browser":"Chrome 133.0.0","description":"User logged out successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"LOGOUT","table_name":"N/A","user":"<EMAIL>"},"timestamp":"2025-02-13 15:09:37"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 133.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-02-13 15:09:59"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 133.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-02-13 15:27:21"}
