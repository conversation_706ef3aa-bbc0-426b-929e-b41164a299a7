{"level":"info","message":{"api_url":"/organization/logout","browser":"Chrome 132.0.0","description":"User logged out successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"LOGOUT","table_name":"N/A","user":"<EMAIL>"},"timestamp":"2025-02-06 07:43:12"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 132.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-02-06 07:58:53"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/207","browser":"Chrome 132.0.0","description":"Medicine Updated Sucessfully","ip_address":"::1","method":"PUT","new_value":"{}","old_value":"{}","operation":"UPDATE","table_name":"Medication","user":"<EMAIL>"},"timestamp":"2025-02-06 08:00:39"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/207","browser":"Chrome 132.0.0","description":"Medicine Updated Sucessfully","ip_address":"::1","method":"PUT","new_value":"{}","old_value":"{}","operation":"UPDATE","table_name":"Medication","user":"<EMAIL>"},"timestamp":"2025-02-06 08:02:26"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/207","browser":"Chrome 132.0.0","description":"Medicine Updated Successfully","ip_address":"::1","method":"PUT","new_value":"{\"medication_name\":\"ads\",\"dosage_times\":[\"07:27 AM AM AM AM AM AM AM AM AM\",\"07:25 AM AM AM AM AM AM AM AM AM\"],\"frequencyType\":\"BID\",\"frequencyTime\":\"Morning\",\"frequencyCondition\":\"Fasting\",\"note\":\"asd\",\"tracker_time\":\"2025-02-06T03:07:18.706Z\",\"reason\":\"ghdfghdfgfdgdfg\"}","old_value":"{\"medication_name\":\"adsffdffgfdbvnbv\",\"note\":\"asdfasdffsdfdfgdfgvbnvb\"}","operation":"UPDATE","table_name":"Medication","user":"<EMAIL>"},"timestamp":"2025-02-06 08:07:23"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/207","browser":"Chrome 132.0.0","description":"Error updating medication: newData is not defined","ip_address":"::1","method":"PUT","new_value":"null","old_value":"{\"medication_id\":\"207\"}","operation":"UPDATE_ERROR","table_name":"Medication","user":"<EMAIL>"},"timestamp":"2025-02-06 08:09:25"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/207","browser":"Chrome 132.0.0","description":"Error updating medication: newMedication is not defined","ip_address":"::1","method":"PUT","new_value":"null","old_value":"{\"medication_id\":\"207\"}","operation":"UPDATE_ERROR","table_name":"Medication","user":"<EMAIL>"},"timestamp":"2025-02-06 08:10:16"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/207","browser":"Chrome 132.0.0","description":"mnbmbnmbnm","ip_address":"::1","method":"PUT","new_value":"{\"medication_name\":\"adsdsfbvn\",\"dosage_times\":[\"07:27 AM AM AM AM AM AM AM AM AM AM AM\",\"07:25 AM AM AM AM AM AM AM AM AM AM AM\"],\"frequencyType\":\"BID\",\"frequencyTime\":\"Morning\",\"frequencyCondition\":\"Fasting\",\"note\":\"asddsfnvbn\",\"tracker_time\":\"2025-02-06T03:11:34.664Z\",\"reason\":\"mnbmbnmbnm\"}","old_value":"{\"medication_name\":\"adsxzcxzc\",\"note\":\"asdxzcxzcxzc\"}","operation":"UPDATE","table_name":"Medication","user":"<EMAIL>"},"timestamp":"2025-02-06 08:11:39"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/207","browser":"Chrome 132.0.0","description":"Error updating medication: newMedication is not defined","ip_address":"::1","method":"PUT","new_value":"null","old_value":"{\"medication_id\":\"207\"}","operation":"UPDATE_ERROR","table_name":"Medication","user":"<EMAIL>"},"timestamp":"2025-02-06 08:13:34"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/207","browser":"Chrome 132.0.0","description":"Medicine Updated Successfully","ip_address":"::ffff:127.0.0.1","method":"PUT","new_value":"{\"medication_name\":\"ad\",\"dosage_times\":[\"07:27 AM AM AM AM AM AM AM AM AM AM AM AM AM\",\"07:25 AM AM AM AM AM AM AM AM AM AM AM AM AM\"],\"frequencyType\":\"BID\",\"frequencyTime\":\"Morning\",\"frequencyCondition\":\"Fasting\",\"note\":\"hi\",\"tracker_time\":\"2025-02-06T03:14:34.729Z\",\"reason\":\"hjhgj\"}","old_value":"{\"medication_name\":\"adsdsfbvndsfsdfs\",\"note\":\"asddsfnvbndsfdsfsdf\"}","operation":"UPDATE","table_name":"Medication","user":"<EMAIL>"},"timestamp":"2025-02-06 08:14:39"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/207","browser":"Chrome 132.0.0","description":"Medicine Updated Successfully","ip_address":"::1","method":"PUT","new_value":"{\"medication_name\":\"adfgdfg\",\"dosage_times\":[\"07:25 AM AM AM AM AM AM AM AM AM AM AM AM AM AM\",\"07:27 AM AM AM AM AM AM AM AM AM AM AM AM AM AM\"],\"frequencyType\":\"BID\",\"frequencyTime\":\"Morning\",\"frequencyCondition\":\"Fasting\",\"note\":\"hifdgf\",\"tracker_time\":\"2025-02-06T03:26:00.010Z\",\"reason\":\"ghfghgfhgfh\"}","old_value":"{\"medication_name\":\"ad\",\"note\":\"hi\"}","operation":"UPDATE","table_name":"Medication","user":"<EMAIL>"},"timestamp":"2025-02-06 08:26:05"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/207","browser":"Chrome 132.0.0","description":"Medicine Updated Successfully","ip_address":"::1","method":"PUT","new_value":"{\"medication_name\":\"adfgdfgdsfd\",\"dosage_times\":[\"07:27 AM AM AM AM AM AM AM AM AM AM AM AM AM AM AM\",\"07:25 AM AM AM AM AM AM AM AM AM AM AM AM AM AM AM\"],\"frequencyType\":\"BID\",\"frequencyTime\":\"Morning\",\"frequencyCondition\":\"Fasting\",\"note\":\"hifdgfdsfffffffff\",\"tracker_time\":\"2025-02-06T05:13:03.624Z\",\"reason\":\"vbcvbcb\"}","old_value":"{\"medication_name\":\"adfgdfg\",\"note\":\"hifdgf\"}","operation":"UPDATE","table_name":"Medication","user":"<EMAIL>"},"timestamp":"2025-02-06 10:13:09"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/207","browser":"Chrome 132.0.0","description":"Medicine Updated Successfully","ip_address":"::1","method":"PUT","new_value":"{\"medication_name\":\"haha\",\"dosage_times\":[\"07:25 AM AM AM AM AM AM AM AM AM AM AM AM AM AM AM AM\",\"07:27 AM AM AM AM AM AM AM AM AM AM AM AM AM AM AM AM\"],\"frequencyType\":\"BID\",\"frequencyTime\":\"Morning\",\"frequencyCondition\":\"Fasting\",\"note\":\"hhhhhhhhhhhhhhh\",\"tracker_time\":\"2025-02-06T05:17:40.522Z\",\"reason\":\"hhhhhhhhhhhhhhhh\"}","old_value":"{\"medication_name\":\"adfgdfgdsfd\",\"note\":\"hifdgfdsfffffffff\"}","operation":"UPDATE","table_name":"Medication","user":"<EMAIL>"},"timestamp":"2025-02-06 10:17:45"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/207","browser":"Chrome 132.0.0","description":"Error updating medication: newData is not defined","ip_address":"::1","method":"PUT","new_value":"null","old_value":"{\"medication_id\":\"207\"}","operation":"UPDATE_ERROR","table_name":"Medication","user":"<EMAIL>"},"timestamp":"2025-02-06 10:20:27"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/207","browser":"Chrome 132.0.0","description":"Medicine Updated Successfully","ip_address":"::1","method":"PUT","new_value":"{\"medication_name\":\"hahahaha\",\"dosage_times\":[\"07:25 AM AM AM AM AM AM AM AM AM AM AM AM AM AM AM AM AM AM\",\"07:27 AM AM AM AM AM AM AM AM AM AM AM AM AM AM AM AM AM AM\"],\"frequencyType\":\"BID\",\"frequencyTime\":\"Morning\",\"frequencyCondition\":\"Fasting\",\"tracker_time\":\"2025-02-06T05:21:21.020Z\",\"reason\":\"cvvxc\"}","old_value":"{\"medication_name\":\"hahaha\"}","operation":"UPDATE","table_name":"Medication","user":"<EMAIL>"},"timestamp":"2025-02-06 10:21:25"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/207","browser":"Chrome 132.0.0","description":"Error updating medication: newMedication is not defined","ip_address":"::ffff:127.0.0.1","method":"PUT","new_value":"null","old_value":"{\"medication_id\":\"207\"}","operation":"UPDATE_ERROR","table_name":"Medication","user":"<EMAIL>"},"timestamp":"2025-02-06 10:23:19"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/207","browser":"Chrome 132.0.0","description":"Medicine Updated Successfully","ip_address":"::1","method":"PUT","new_value":"{\"medication_name\":\"hahahahaha\",\"dosage_times\":[\"07:27 AM AM AM AM AM AM AM AM AM AM AM AM AM AM AM AM AM AM AM\",\"07:25 AM AM AM AM AM AM AM AM AM AM AM AM AM AM AM AM AM AM AM\"],\"frequencyType\":\"BID\",\"frequencyTime\":\"Morning\",\"frequencyCondition\":\"Fasting\",\"tracker_time\":\"2025-02-06T05:24:03.791Z\",\"reason\":\"zxzc\"}","old_value":"{\"medication_name\":\"hahahahaxz\"}","operation":"UPDATE","table_name":"Medication","user":"<EMAIL>"},"timestamp":"2025-02-06 10:24:08"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/207","browser":"Chrome 132.0.0","description":"Medicine Updated Successfully","ip_address":"::1","method":"PUT","new_value":"{\"medication_name\":\"hahahahahaha\",\"dosage_times\":[\"07:25 AM AM AM AM AM AM AM AM AM AM AM AM AM AM AM AM AM AM AM AM\",\"07:27 AM AM AM AM AM AM AM AM AM AM AM AM AM AM AM AM AM AM AM AM\"],\"frequencyType\":\"BID\",\"frequencyTime\":\"Morning\",\"frequencyCondition\":\"Fasting\",\"tracker_time\":\"2025-02-06T05:25:40.598Z\",\"reason\":\"vcb\"}","old_value":"{\"medication_name\":\"hahahahaha\"}","operation":"UPDATE","table_name":"Medication","user":"<EMAIL>"},"timestamp":"2025-02-06 10:25:45"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/207","browser":"Chrome 132.0.0","description":"Error updating medication: connect EHOSTUNREACH ***********:3306","ip_address":"::1","method":"PUT","new_value":"null","old_value":"{\"medication_id\":\"207\"}","operation":"UPDATE_ERROR","table_name":"Medication","user":"<EMAIL>"},"timestamp":"2025-02-06 10:30:08"}
{"level":"error","message":"Failed to log to database or Excel: connect EHOSTUNREACH ***********:3306","timestamp":"2025-02-06 10:30:08"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/207","browser":"Chrome 132.0.0","description":"Error updating medication: connect EHOSTUNREACH ***********:3306","ip_address":"::1","method":"PUT","new_value":"null","old_value":"{\"medication_id\":\"207\"}","operation":"UPDATE_ERROR","table_name":"Medication","user":"<EMAIL>"},"timestamp":"2025-02-06 10:30:35"}
{"level":"error","message":"Failed to log to database or Excel: connect EHOSTUNREACH ***********:3306","timestamp":"2025-02-06 10:30:35"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/207","browser":"Chrome 132.0.0","description":"Medicine Updated Successfully","ip_address":"::1","method":"PUT","new_value":"{\"medication_name\":\"hahahahahaha bvb\",\"dosage_times\":[\"07:27 AM AM AM AM AM AM AM AM AM AM AM AM AM AM AM AM AM AM AM AM AM\",\"07:25 AM AM AM AM AM AM AM AM AM AM AM AM AM AM AM AM AM AM AM AM AM\"],\"frequencyType\":\"BID\",\"frequencyTime\":\"Morning\",\"frequencyCondition\":\"Fasting\",\"note\":\"hhhhhhhhhhhhhhhvcbvc\",\"tracker_time\":\"2025-02-06T05:31:35.383Z\",\"reason\":\"bnnnnnnnnnn\"}","old_value":"{\"medication_name\":\"hahahahahaha\",\"note\":\"hhhhhhhhhhhhhhh\"}","operation":"UPDATE","table_name":"Medication","user":"<EMAIL>"},"timestamp":"2025-02-06 10:31:42"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1606","browser":"Chrome 132.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-02-06 10:50:15"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-06 10:50:15"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1606","browser":"Chrome 132.0.0","description":"Schedule scheduled with ID: 1606","ip_address":"::1","method":"PUT","new_value":"{\"schedule_date\":\"2025-03-10\",\"schedule_time\":\"21:04\",\"status\":\"Scheduled\"}","old_value":"{\"schedule_date\":\"2025-03-09T19:00:00.000Z\",\"schedule_time\":\"09:00\",\"status\":\"Pending\"}","operation":"UPDATE","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-02-06 10:50:18"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/340","browser":"Chrome 132.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-02-06 10:56:59"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-06 10:56:59"}
{"level":"info","message":{"api_url":"/schedule/createSchedule","browser":"Other 0.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-02-06 10:57:01"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-06 10:57:01"}
{"level":"info","message":{"api_url":"/schedule/createSchedule","browser":"Other 0.0.0","description":"Schedule created successfully for user 447","ip_address":"::1","method":"POST","new_value":"{}","old_value":"null","operation":"CREATE","table_name":"Schedule","user":"<EMAIL>"},"timestamp":"2025-02-06 10:57:07"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/340","browser":"Chrome 132.0.0","description":"Auto-schedule created for user 447","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":447,\"schedule\":{\"schedule_date\":\"2025-02-06\",\"schedule_time\":\"09:00\",\"study_enrolled_id\":\"1\",\"status\":\"Scheduled\",\"user_id\":447,\"note\":\"Auto-Created Schedule\"}}","old_value":"null","operation":"SCHEDULE_CREATED","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-02-06 10:57:10"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/340","browser":"Chrome 132.0.0","description":"Welcome email sent to Hora  Horario (<EMAIL>)","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":447,\"email\":\"<EMAIL>\",\"subject\":\"Welcome to ResearchHero!\"}","old_value":"null","operation":"EMAIL_SENT","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-02-06 10:57:11"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/340","browser":"Chrome 132.0.0","description":"Registration status updated to Accepted for ID 340","ip_address":"::1","method":"PUT","new_value":"{\"id\":\"340\",\"status\":\"Accepted\",\"reason\":\"Accepted By Super Admin \",\"user_id\":447}","old_value":"{}","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-02-06 10:57:11"}
{"level":"error","message":"Failed to log to database or Excel: Corrupted zip or bug: expected 16 records in central dir, got 0","timestamp":"2025-02-06 10:57:11"}
{"level":"error","message":"Failed to log to database or Excel: Corrupted zip or bug: expected 16 records in central dir, got 0","timestamp":"2025-02-06 10:57:12"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/263","browser":"Chrome 132.0.0","ip_address":"::ffff:127.0.0.1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-02-06 11:19:21"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-06 11:19:21"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/263","browser":"Chrome 132.0.0","description":"Error updating registration status: jwt is not defined","ip_address":"::ffff:127.0.0.1","method":"PUT","new_value":"null","old_value":"{\"id\":\"263\",\"status\":\"Accepted\",\"reason\":\"Accepted By Super Admin \",\"user_id\":366}","operation":"UPDATE_ERROR","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-02-06 11:19:22"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/259","browser":"Chrome 132.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-02-06 11:19:34"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-06 11:19:34"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/259","browser":"Chrome 132.0.0","description":"Error updating registration status: jwt is not defined","ip_address":"::1","method":"PUT","new_value":"null","old_value":"{\"id\":\"259\",\"status\":\"Accepted\",\"reason\":\"Accepted By Super Admin \",\"user_id\":363}","operation":"UPDATE_ERROR","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-02-06 11:19:34"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 132.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-02-06 11:20:49"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-06 11:20:49"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 132.0.0","description":"New organization created","ip_address":"::1","method":"POST","new_value":"{\"first_name\":\"mark\",\"last_name\":\"zukerburg\",\"middle_name\":\"\",\"email\":\"<EMAIL>\",\"status\":\"Screened\",\"gender\":\"male\",\"address\":\"5959 Bonhomme Rd\",\"contact_number\":\"12377292048\",\"date_of_birth\":\"01/28/1997\",\"stipend\":\"4\",\"study_enrolled_ids\":\"1\",\"notification\":\"some notification\",\"note\":\"Note for admin\",\"role_id\":\"10\",\"organization_detail_id\":\"1\",\"ecrf_id\":\"A676-767\",\"timezone\":\"Asia/Karachi\"}","old_value":"null","operation":"CREATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-02-06 11:20:51"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/341","browser":"Chrome 132.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-02-06 11:21:29"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-06 11:21:29"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/341","browser":"Chrome 132.0.0","description":"Error updating registration status: jwt is not defined","ip_address":"::1","method":"PUT","new_value":"null","old_value":"{\"id\":\"341\",\"status\":\"Accepted\",\"reason\":\"Accepted By Super Admin \",\"user_id\":448}","operation":"UPDATE_ERROR","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-02-06 11:21:30"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/341","browser":"Chrome 132.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-02-06 11:22:27"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-06 11:22:27"}
{"level":"info","message":{"api_url":"/schedule/createSchedule","browser":"Other 0.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-02-06 11:22:29"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-06 11:22:30"}
{"level":"info","message":{"api_url":"/schedule/createSchedule","browser":"Other 0.0.0","description":"Schedule created successfully for user 448","ip_address":"::1","method":"POST","new_value":"{}","old_value":"null","operation":"CREATE","table_name":"Schedule","user":"<EMAIL>"},"timestamp":"2025-02-06 11:22:36"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/341","browser":"Chrome 132.0.0","description":"Auto-schedule created for user 448","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":448,\"schedule\":{\"schedule_date\":\"2025-02-06\",\"schedule_time\":\"09:00\",\"study_enrolled_id\":\"1\",\"status\":\"Scheduled\",\"user_id\":448,\"note\":\"Auto-Created Schedule\"}}","old_value":"null","operation":"SCHEDULE_CREATED","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-02-06 11:22:39"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/341","browser":"Chrome 132.0.0","description":"Welcome email sent to mark zukerburg (<EMAIL>)","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":448,\"email\":\"<EMAIL>\",\"subject\":\"Welcome to ResearchHero!\"}","old_value":"null","operation":"EMAIL_SENT","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-02-06 11:22:40"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/341","browser":"Chrome 132.0.0","description":"Registration status updated to Accepted for ID 341","ip_address":"::1","method":"PUT","new_value":"{\"id\":\"341\",\"status\":\"Accepted\",\"reason\":\"Accepted By Super Admin \",\"user_id\":448}","old_value":"{}","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-02-06 11:22:40"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1620","browser":"Chrome 132.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-02-06 11:32:39"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-06 11:32:39"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1620","browser":"Chrome 132.0.0","description":"Auto Create Medicine","ip_address":"::1","method":"PUT","new_value":"{\"medication_name\":\"Sunobinop Or Placebo\",\"dosage\":\"0.5mg or 1.0mg or 2.0mg\",\"dosage_times\":[\"09:00 PM\"],\"frequencyType\":\"QD\",\"frequencyTime\":\"N/A\",\"frequencyCondition\":\"At Bedtime\",\"dosageType\":\"Tablet\",\"allot_medicine\":\"1\",\"route\":\"Oral\",\"note\":\"Auto-created medicine\",\"user_id\":448,\"investigator_id\":0,\"tracker_time\":\"2025-02-06T06:32:40.992Z\"}","old_value":"null","operation":"CREATE","table_name":"Auto Create Medicine Due to Subject Screening Complete","user":"<EMAIL>"},"timestamp":"2025-02-06 11:32:41"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1620","browser":"Chrome 132.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-02-06 11:32:43"}
{"level":"error","message":"Failed to log to database or Excel: Cannot read properties of undefined (reading 'headers')","timestamp":"2025-02-06 11:32:43"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-06 11:32:43"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1620","browser":"Chrome 132.0.0","description":"Schedule completed with ID: 1620","ip_address":"::1","method":"PUT","new_value":"{\"schedule_date\":\"2025-02-06\",\"status\":\"Completed\"}","old_value":"{\"schedule_date\":\"2025-02-05T19:00:00.000Z\",\"status\":\"Scheduled\"}","operation":"UPDATE","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-02-06 11:32:46"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1620","browser":"Chrome 132.0.0","description":"Schedule completed with ID: 1620","ip_address":"::1","method":"PUT","new_value":"{\"schedule_date\":\"2025-02-06\",\"status\":\"Completed\"}","old_value":"{\"schedule_date\":\"2025-02-05T19:00:00.000Z\",\"status\":\"Scheduled\"}","operation":"UPDATE","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-02-06 11:32:48"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1621","browser":"Chrome 132.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-02-06 11:33:11"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-06 11:33:11"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1621","browser":"Chrome 132.0.0","description":"Schedule completed with ID: 1621","ip_address":"::1","method":"PUT","new_value":"{\"schedule_date\":\"2025-02-13\",\"status\":\"Completed\"}","old_value":"{\"schedule_date\":\"2025-02-12T19:00:00.000Z\",\"status\":\"Pending\"}","operation":"UPDATE","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-02-06 11:33:14"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-06 14:40:54"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Other 0.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-02-06 14:42:33"}
