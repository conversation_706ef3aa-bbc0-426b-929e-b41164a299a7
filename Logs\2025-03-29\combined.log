{"level":"info","message":{"api_url":"/auth/signin","browser":"Other 0.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-29 00:25:05"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 134.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-29 01:12:13"}
{"level":"info","message":{"api_url":"/organization/createPersonnel","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-29 01:13:39"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-29 01:13:39"}
{"level":"info","message":{"api_url":"/organization/createPersonnel","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-29 01:13:42"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-29 01:13:43"}
{"level":"info","message":{"api_url":"/organization/createPersonnel","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-29 01:14:02"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-29 01:14:02"}
{"level":"info","message":{"api_url":"/organization/createPersonnel","browser":"Chrome 134.0.0","description":"Personnel created with email: <EMAIL>","ip_address":"::1","method":"POST","new_value":"{\"userId\":570}","old_value":"null","operation":"CREATE","table_name":"personnel","user":"<EMAIL>"},"timestamp":"2025-03-29 01:14:06"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Other 0.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-29 03:39:39"}
{"level":"info","message":{"api_url":"/emergency/emergencyEmail","browser":"Other 0.0.0","description":"Emergency email sent and logged successfully","ip_address":"::1","method":"POST","new_value":"{\"subject\":\"Urgent: Immediate Attention Required\",\"date_time\":\"8/1/2024 T 4:14PM\",\"description\":\"Please review and respond to this message as soon as possible due to its high priority.\",\"user_id\":433,\"reportId\":{\"fieldCount\":0,\"affectedRows\":1,\"insertId\":65,\"info\":\"\",\"serverStatus\":2,\"warningStatus\":0,\"changedRows\":0}}","old_value":"null","operation":"SUBMIT","table_name":"Callback Schedule","user":"<EMAIL>"},"timestamp":"2025-03-29 03:40:15"}
{"level":"info","message":{"api_url":"/emergency/emergencyEmail","browser":"Other 0.0.0","description":"Emergency email sent and logged successfully","ip_address":"::1","method":"POST","new_value":"{\"subject\":\"Urgent: Immediate Attention Required\",\"date_time\":\"8/1/2024 T 4:14PM\",\"description\":\"testing\",\"user_id\":433,\"reportId\":{\"fieldCount\":0,\"affectedRows\":1,\"insertId\":67,\"info\":\"\",\"serverStatus\":2,\"warningStatus\":0,\"changedRows\":0}}","old_value":"null","operation":"SUBMIT","table_name":"Callback Schedule","user":"<EMAIL>"},"timestamp":"2025-03-29 03:49:05"}
{"level":"info","message":{"api_url":"/emergency/emergencyEmail","browser":"Other 0.0.0","description":"Emergency email sent and logged successfully","ip_address":"::1","method":"POST","new_value":"{\"subject\":\"Urgent: Immediate Attention Required\",\"date_time\":\"8/1/2024 T 4:14PM\",\"description\":\"testing\",\"user_id\":433,\"reportId\":{\"fieldCount\":0,\"affectedRows\":1,\"insertId\":69,\"info\":\"\",\"serverStatus\":2,\"warningStatus\":0,\"changedRows\":0}}","old_value":"null","operation":"SUBMIT","table_name":"Callback Schedule","user":"<EMAIL>"},"timestamp":"2025-03-29 03:51:56"}
{"level":"info","message":{"api_url":"/organization/update-password","browser":"Chrome 134.0.0","description":"Password updated for user ID: 403","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"UPDATE","table_name":"Password","user":"<EMAIL>"},"timestamp":"2025-03-29 06:00:23"}
{"level":"info","message":{"api_url":"/organization/update-password","browser":"Chrome 134.0.0","description":"Password updated for user ID: 403","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"UPDATE","table_name":"Password","user":"<EMAIL>"},"timestamp":"2025-03-29 06:01:11"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-03-29 20:02:21"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Other 0.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-29 20:10:19"}
{"level":"info","message":{"api_url":"/incident_report/createIncidentReportResponse","browser":"Other 0.0.0","description":"Incident report response created:\n          study_id: 1,\n          user_id: 520,\n          question_id: 45,\n          response_text: Testing email.,\n          description: Testing email ,\n          incident_severety: Moderate,\n          start_date: 01/10/2025,\n          start_time: 4:54:00 PM,\n          medical_issue: 1,\n          end_date: 2024-12-25,\n          end_time: 15:00","ip_address":"::1","method":"POST","new_value":"{\"study_id\":1,\"user_id\":520,\"question_id\":45,\"response_text\":\"Testing email.\",\"description\":\"Testing email \",\"incident_severety\":\"Moderate\",\"start_date\":\"01/10/2025\",\"start_time\":\"4:54:00 PM\",\"medical_issue\":1,\"end_date\":\"2024-12-25\",\"end_time\":\"15:00\"}","old_value":"null","operation":"SUBMIT","table_name":"Incident Report Response","user":"<EMAIL>"},"timestamp":"2025-03-29 22:45:29"}
{"level":"error","message":"Failed to log to database or Excel: Data too long for column 'description' at row 1","timestamp":"2025-03-29 22:45:29"}
{"level":"info","message":{"api_url":"/incident_report/createIncidentReportResponse","browser":"Other 0.0.0","description":"Incident report response created:\n          study_id: 1,\n          user_id: 520,\n          question_id: 45,\n          response_text: Testing email.,\n          description: Testing email ,\n          incident_severety: Moderate,\n          start_date: 01/10/2025,\n          start_time: 4:54:00 PM,\n          medical_issue: 1,\n          end_date: 2024-12-25,\n          end_time: 15:00","ip_address":"::1","method":"POST","new_value":"{\"study_id\":1,\"user_id\":520,\"question_id\":45,\"response_text\":\"Testing email.\",\"description\":\"Testing email \",\"incident_severety\":\"Moderate\",\"start_date\":\"01/10/2025\",\"start_time\":\"4:54:00 PM\",\"medical_issue\":1,\"end_date\":\"2024-12-25\",\"end_time\":\"15:00\"}","old_value":"null","operation":"SUBMIT","table_name":"Incident Report Response","user":"<EMAIL>"},"timestamp":"2025-03-29 22:49:02"}
{"level":"error","message":"Failed to log to database or Excel: Data too long for column 'description' at row 1","timestamp":"2025-03-29 22:49:05"}
{"level":"info","message":{"api_url":"/incident_report/createIncidentReportResponse","browser":"Other 0.0.0","description":"Incident report response created:\n          study_id: 1,\n          user_id: 520,\n          question_id: 45,\n          response_text: Testing email.,\n          description: Testing email ,\n          incident_severety: Moderate,\n          start_date: 01/10/2025,\n          start_time: 4:54:00 PM,\n          medical_issue: 1,\n          end_date: 2024-12-25,\n          end_time: 15:00","ip_address":"::1","method":"POST","new_value":"{\"study_id\":1,\"user_id\":520,\"question_id\":45,\"response_text\":\"Testing email.\",\"description\":\"Testing email \",\"incident_severety\":\"Moderate\",\"start_date\":\"01/10/2025\",\"start_time\":\"4:54:00 PM\",\"medical_issue\":1,\"end_date\":\"2024-12-25\",\"end_time\":\"15:00\"}","old_value":"null","operation":"SUBMIT","table_name":"Incident Report Response","user":"<EMAIL>"},"timestamp":"2025-03-29 22:53:26"}
{"level":"error","message":"Failed to log to database or Excel: Data too long for column 'description' at row 1","timestamp":"2025-03-29 22:53:27"}
{"level":"info","message":{"api_url":"/incident_report/createIncidentReportResponse","browser":"Other 0.0.0","description":"Incident report response created:\n          study_id: 1,\n          user_id: 520,\n          question_id: 45,\n          response_text: Testing email.,\n          description: Testing email ,\n          incident_severety: Moderate,\n          start_date: 01/10/2025,\n          start_time: 4:54:00 PM,\n          medical_issue: 1,\n          end_date: 2024-12-25,\n          end_time: 15:00","ip_address":"::1","method":"POST","new_value":"{\"study_id\":1,\"user_id\":520,\"question_id\":45,\"response_text\":\"Testing email.\",\"description\":\"Testing email \",\"incident_severety\":\"Moderate\",\"start_date\":\"01/10/2025\",\"start_time\":\"4:54:00 PM\",\"medical_issue\":1,\"end_date\":\"2024-12-25\",\"end_time\":\"15:00\"}","old_value":"null","operation":"SUBMIT","table_name":"Incident Report Response","user":"<EMAIL>"},"timestamp":"2025-03-29 22:58:29"}
{"level":"error","message":"Failed to log to database or Excel: Data too long for column 'description' at row 1","timestamp":"2025-03-29 22:58:30"}
{"level":"info","message":{"api_url":"/incident_report/createIncidentReportResponse","browser":"Other 0.0.0","description":"Incident report response created:\n          study_id: 1,\n          user_id: 520,\n          question_id: 45,\n          response_text: Testing email.,\n          description: Testing email ,\n          incident_severety: Moderate,\n          start_date: 01/10/2025,\n          start_time: 4:54:00 PM,\n          medical_issue: 1,\n          end_date: 2024-12-25,\n          end_time: 15:00","ip_address":"::1","method":"POST","new_value":"{\"study_id\":1,\"user_id\":520,\"question_id\":45,\"response_text\":\"Testing email.\",\"description\":\"Testing email \",\"incident_severety\":\"Moderate\",\"start_date\":\"01/10/2025\",\"start_time\":\"4:54:00 PM\",\"medical_issue\":1,\"end_date\":\"2024-12-25\",\"end_time\":\"15:00\"}","old_value":"null","operation":"SUBMIT","table_name":"Incident Report Response","user":"<EMAIL>"},"timestamp":"2025-03-29 22:59:25"}
{"level":"error","message":"Failed to log to database or Excel: Data too long for column 'description' at row 1","timestamp":"2025-03-29 22:59:25"}
{"level":"info","message":{"api_url":"/incident_report/createIncidentReportResponse","browser":"Other 0.0.0","description":"Incident report response created:\n          study_id: 1,\n          user_id: 520,\n          question_id: 45,\n          response_text: Testing email.,\n          description: Testing email ,\n          incident_severety: Moderate,\n          start_date: 01/10/2025,\n          start_time: 4:54:00 PM,\n          medical_issue: 1,\n          end_date: 2024-12-25,\n          end_time: 15:00","ip_address":"::1","method":"POST","new_value":"{\"study_id\":1,\"user_id\":520,\"question_id\":45,\"response_text\":\"Testing email.\",\"description\":\"Testing email \",\"incident_severety\":\"Moderate\",\"start_date\":\"01/10/2025\",\"start_time\":\"4:54:00 PM\",\"medical_issue\":1,\"end_date\":\"2024-12-25\",\"end_time\":\"15:00\"}","old_value":"null","operation":"SUBMIT","table_name":"Incident Report Response","user":"<EMAIL>"},"timestamp":"2025-03-29 23:05:01"}
{"level":"error","message":"Failed to log to database or Excel: Data too long for column 'description' at row 1","timestamp":"2025-03-29 23:05:02"}
