{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-10 22:24:11"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-10 22:25:44"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-10 22:26:00"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-10 22:28:56"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-10 22:29:05"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-10 22:30:51"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-10 22:36:32"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-10 22:38:10"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-10 22:40:01"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-10 22:52:32"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-10 23:03:40"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-10 23:28:57"}
