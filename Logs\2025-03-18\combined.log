{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 134.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-18 21:03:53"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-18 21:05:29"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-18 21:05:29"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","description":"New organization created","ip_address":"::1","method":"POST","new_value":"{\"first_name\":\"abbas\",\"last_name\":\"khan\",\"middle_name\":\"\",\"email\":\"<EMAIL>\",\"status\":\"Screened\",\"gender\":\"male\",\"address\":\"mardan\",\"contact_number\":\"10000000000\",\"date_of_birth\":\"01/01/1999\",\"stipend\":\"2\",\"study_enrolled_ids\":\"3\",\"notification\":\"some notification\",\"note\":\"Note for admin\",\"role_id\":\"10\",\"organization_detail_id\":\"1\",\"ecrf_id\":\"A101-001\",\"timezone\":\"Asia/Karachi\"}","old_value":"null","operation":"CREATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-18 21:05:32"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-18 21:06:27"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-18 21:06:27"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","description":"New organization created","ip_address":"::1","method":"POST","new_value":"{\"first_name\":\"abbas\",\"last_name\":\"khan\",\"middle_name\":\"\",\"email\":\"<EMAIL>\",\"status\":\"Screened\",\"gender\":\"male\",\"address\":\"mardan\",\"contact_number\":\"10000000000\",\"date_of_birth\":\"01/01/1999\",\"stipend\":\"2\",\"study_enrolled_ids\":\"1\",\"notification\":\"some notification\",\"note\":\"Note for admin\",\"role_id\":\"10\",\"organization_detail_id\":\"1\",\"ecrf_id\":\"A101-001\",\"timezone\":\"Asia/Karachi\"}","old_value":"null","operation":"CREATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-18 21:06:30"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-18 21:08:28"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-18 21:08:29"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","description":"New organization created","ip_address":"::1","method":"POST","new_value":"{\"first_name\":\"abbas\",\"last_name\":\"khan\",\"middle_name\":\"\",\"email\":\"<EMAIL>\",\"status\":\"Screened\",\"gender\":\"male\",\"address\":\"mardan\",\"contact_number\":\"10000000000\",\"date_of_birth\":\"01/01/1999\",\"stipend\":\"2\",\"study_enrolled_ids\":\"1\",\"notification\":\"some notification\",\"note\":\"Note for admin\",\"role_id\":\"10\",\"organization_detail_id\":\"1\",\"ecrf_id\":\"A101-001\",\"timezone\":\"Asia/Karachi\"}","old_value":"null","operation":"CREATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-18 21:08:32"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-18 21:09:48"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-18 21:09:48"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","description":"New organization created","ip_address":"::1","method":"POST","new_value":"{\"first_name\":\"abbas\",\"last_name\":\"khan\",\"middle_name\":\"\",\"email\":\"<EMAIL>\",\"status\":\"Screened\",\"gender\":\"male\",\"address\":\"mardan\",\"contact_number\":\"10000000000\",\"date_of_birth\":\"01/01/1999\",\"stipend\":\"2\",\"study_enrolled_ids\":\"1\",\"notification\":\"some notification\",\"note\":\"Note for admin\",\"role_id\":\"10\",\"organization_detail_id\":\"2\",\"ecrf_id\":\"A101-001\",\"timezone\":\"Asia/Karachi\"}","old_value":"null","operation":"CREATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-18 21:09:51"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/450","browser":"Chrome 134.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-03-18 21:10:26"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-18 21:10:26"}
{"level":"info","message":{"api_url":"/schedule/createSchedule","browser":"Other 0.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-03-18 21:10:29"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-18 21:10:29"}
{"level":"info","message":{"api_url":"/schedule/createSchedule","browser":"Other 0.0.0","description":"Schedule created successfully for user 562","ip_address":"::1","method":"POST","new_value":"{}","old_value":"null","operation":"CREATE","table_name":"Schedule","user":"<EMAIL>"},"timestamp":"2025-03-18 21:10:37"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/450","browser":"Chrome 134.0.0","description":"Auto-schedule created for user 562","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":562,\"schedule\":{\"schedule_date\":\"2025-03-18\",\"schedule_time\":\"09:00\",\"study_enrolled_id\":\"1\",\"status\":\"Scheduled\",\"user_id\":562,\"note\":\"Auto-Created Schedule\"}}","old_value":"null","operation":"SCHEDULE_CREATED","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-03-18 21:10:39"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/450","browser":"Chrome 134.0.0","description":"Welcome email sent to abbas khan (<EMAIL>)","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":562,\"email\":\"<EMAIL>\",\"subject\":\"Welcome to ResearchHero!\"}","old_value":"null","operation":"EMAIL_SENT","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-03-18 21:10:40"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/450","browser":"Chrome 134.0.0","description":"Registration status updated to Accepted for ID 450","ip_address":"::1","method":"PUT","new_value":"{\"id\":\"450\",\"status\":\"Accepted\",\"reason\":\"Accepted! fcxcvxcvxvxcvxcv\",\"user_id\":562}","old_value":"{}","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-03-18 21:10:40"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Other 0.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-18 21:19:48"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-18 21:25:33"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-18 21:25:33"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","description":"New organization created","ip_address":"::1","method":"POST","new_value":"{\"first_name\":\"abbas\",\"last_name\":\"khan\",\"middle_name\":\"\",\"email\":\"<EMAIL>\",\"status\":\"Screened\",\"gender\":\"male\",\"address\":\"mardan\",\"contact_number\":\"10000000000\",\"date_of_birth\":\"01/01/1999\",\"stipend\":\"12\",\"study_enrolled_ids\":\"3\",\"notification\":\"some notification\",\"note\":\"Note for admin\",\"role_id\":\"10\",\"organization_detail_id\":\"1\",\"ecrf_id\":\"A101-001\",\"timezone\":\"Asia/Karachi\"}","old_value":"null","operation":"CREATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-18 21:25:37"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/451","browser":"Chrome 134.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-03-18 21:26:09"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-18 21:26:09"}
{"level":"info","message":{"api_url":"/schedule/createSchedule","browser":"Other 0.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-03-18 21:26:12"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-18 21:26:12"}
{"level":"info","message":{"api_url":"/schedule/createSchedule","browser":"Other 0.0.0","description":"Schedule created successfully for user 563","ip_address":"::1","method":"POST","new_value":"{}","old_value":"null","operation":"CREATE","table_name":"Schedule","user":"<EMAIL>"},"timestamp":"2025-03-18 21:26:22"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/451","browser":"Chrome 134.0.0","description":"Auto-schedule created for user 563","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":563,\"schedule\":{\"schedule_date\":\"2025-03-18\",\"schedule_time\":\"09:00\",\"study_enrolled_id\":\"3\",\"status\":\"Scheduled\",\"user_id\":563,\"note\":\"Auto-Created Schedule\"}}","old_value":"null","operation":"SCHEDULE_CREATED","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-03-18 21:26:24"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/451","browser":"Chrome 134.0.0","description":"Welcome email sent to abbas khan (<EMAIL>)","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":563,\"email\":\"<EMAIL>\",\"subject\":\"Welcome to ResearchHero!\"}","old_value":"null","operation":"EMAIL_SENT","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-03-18 21:26:25"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/451","browser":"Chrome 134.0.0","description":"Registration status updated to Accepted for ID 451","ip_address":"::1","method":"PUT","new_value":"{\"id\":\"451\",\"status\":\"Accepted\",\"reason\":\"Accepted! fcxcvxcvxvxcvxcv\",\"user_id\":563}","old_value":"{}","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-03-18 21:26:25"}
{"level":"error","message":"Failed to log to database or Excel: Corrupted zip or bug: expected 16 records in central dir, got 0","timestamp":"2025-03-18 21:26:26"}
{"level":"error","message":"Failed to log to database or Excel: Corrupted zip or bug: expected 16 records in central dir, got 0","timestamp":"2025-03-18 21:26:26"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-18 21:40:59"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-18 21:40:59"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","description":"New organization created","ip_address":"::1","method":"POST","new_value":"{\"first_name\":\"abbas\",\"last_name\":\"khan\",\"middle_name\":\"\",\"email\":\"<EMAIL>\",\"status\":\"Screened\",\"gender\":\"male\",\"address\":\"mardan\",\"contact_number\":\"10000000000\",\"date_of_birth\":\"01/01/1999\",\"stipend\":\"23\",\"study_enrolled_ids\":\"3\",\"notification\":\"some notification\",\"note\":\"Note for admin\",\"role_id\":\"10\",\"organization_detail_id\":\"1\",\"ecrf_id\":\"A101-001\",\"timezone\":\"Asia/Karachi\"}","old_value":"null","operation":"CREATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-18 21:41:03"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/452","browser":"Chrome 134.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-03-18 21:41:39"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-18 21:41:39"}
{"level":"info","message":{"api_url":"/schedule/createSchedule","browser":"Other 0.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-03-18 21:41:42"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-18 21:41:42"}
{"level":"info","message":{"api_url":"/schedule/createSchedule","browser":"Other 0.0.0","description":"Schedule created successfully for user 564","ip_address":"::1","method":"POST","new_value":"{}","old_value":"null","operation":"CREATE","table_name":"Schedule","user":"<EMAIL>"},"timestamp":"2025-03-18 21:41:51"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/452","browser":"Chrome 134.0.0","description":"Auto-schedule created for user 564","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":564,\"schedule\":{\"schedule_date\":\"2025-03-18\",\"schedule_time\":\"09:00\",\"study_enrolled_id\":\"3\",\"status\":\"Scheduled\",\"user_id\":564,\"note\":\"Auto-Created Schedule\"}}","old_value":"null","operation":"SCHEDULE_CREATED","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-03-18 21:41:53"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/452","browser":"Chrome 134.0.0","description":"Welcome email sent to abbas khan (<EMAIL>)","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":564,\"email\":\"<EMAIL>\",\"subject\":\"Welcome to ResearchHero!\"}","old_value":"null","operation":"EMAIL_SENT","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-03-18 21:41:54"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/452","browser":"Chrome 134.0.0","description":"Registration status updated to Accepted for ID 452","ip_address":"::1","method":"PUT","new_value":"{\"id\":\"452\",\"status\":\"Accepted\",\"reason\":\"Accepted! fcxcvxcvxvxcvxcv\",\"user_id\":564}","old_value":"{}","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-03-18 21:41:54"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/528","browser":"Chrome 134.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-18 21:43:32"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-18 21:43:33"}
{"level":"error","message":"Failed to log to database or Excel: Cannot read properties of undefined (reading 'headers')","timestamp":"2025-03-18 21:43:36"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/528","browser":"Chrome 134.0.0","description":"Auto Create Medicine","ip_address":"::1","method":"PUT","new_value":"{\"medication_name\":\"Sunobinop Or Placebo\",\"dosage\":\"0.5mg or 1.0mg or 2.0mg\",\"dosage_times\":[\"09:00 PM\"],\"frequencyType\":\"QD\",\"frequencyTime\":\"N/A\",\"frequencyCondition\":\"At Bedtime\",\"dosageType\":\"Tablet\",\"allot_medicine\":\"1\",\"route\":\"Oral\",\"note\":\"Auto-created medicine\",\"user_id\":\"564\",\"investigator_id\":\"403\",\"tracker_time\":\"2025-03-18T16:43:34.999Z\"}","old_value":"null","operation":"CREATE","table_name":"Auto Create Medicine Due to Subject Status Update","user":"<EMAIL>"},"timestamp":"2025-03-18 21:43:38"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/528","browser":"Chrome 134.0.0","description":"laallllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllll","ip_address":"::1","method":"PUT","new_value":"{\"email\":\"<EMAIL>\",\"middle_name\":\"\",\"status\":\"Randomized\",\"reason\":\"laallllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllll\"}","old_value":"{\"middle_name\":null,\"status\":\"Screened\"}","operation":"UPDATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-18 21:43:40"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/528","browser":"Chrome 134.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-18 21:44:19"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-18 21:44:20"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/528","browser":"Chrome 134.0.0","description":"vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv","ip_address":"::1","method":"PUT","new_value":"{\"email\":\"<EMAIL>\",\"middle_name\":\"\",\"status\":\"Screen Failed\",\"reason\":\"vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv\"}","old_value":"{\"middle_name\":null,\"status\":\"Randomized\"}","operation":"UPDATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-18 21:44:22"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/528","browser":"Chrome 134.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-18 21:44:38"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-18 21:44:39"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/528","browser":"Chrome 134.0.0","description":"cvvvvvvvvvvvvvvvvv","ip_address":"::1","method":"PUT","new_value":"{\"email\":\"<EMAIL>\",\"middle_name\":\"\",\"status\":\"Randomized\",\"reason\":\"cvvvvvvvvvvvvvvvvv\"}","old_value":"{\"middle_name\":null,\"status\":\"Screen Failed\"}","operation":"UPDATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-18 21:44:40"}
{"level":"info","message":{"api_url":"/organization/createPersonnel","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-18 23:01:44"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-18 23:01:44"}
{"level":"info","message":{"api_url":"/organization/createPersonnel","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-18 23:04:29"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-18 23:04:29"}
{"level":"info","message":{"api_url":"/organization/createPersonnel","browser":"Chrome 134.0.0","ip_address":"::ffff:127.0.0.1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-18 23:04:59"}
{"level":"info","message":{"api_url":"/organization/createPersonnel","browser":"Chrome 134.0.0","ip_address":"::ffff:127.0.0.1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-18 23:04:59"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-18 23:04:59"}
{"level":"info","message":{"api_url":"/organization/createPersonnel","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-18 23:26:05"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-18 23:26:05"}
{"level":"info","message":{"api_url":"/organization/createPersonnel","browser":"Chrome 134.0.0","description":"Personnel created with email: <EMAIL>","ip_address":"::1","method":"POST","new_value":"{\"userId\":565}","old_value":"null","operation":"CREATE","table_name":"personnel","user":"<EMAIL>"},"timestamp":"2025-03-18 23:26:08"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/453","browser":"Chrome 134.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-03-18 23:26:30"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-18 23:26:31"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/453","browser":"Chrome 134.0.0","description":"Welcome email sent to anas khan (<EMAIL>)","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":565,\"email\":\"<EMAIL>\",\"subject\":\"Welcome to ResearchHero!\"}","old_value":"null","operation":"EMAIL_SENT","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-03-18 23:26:35"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/453","browser":"Chrome 134.0.0","description":"Registration status updated to Accepted for ID 453","ip_address":"::1","method":"PUT","new_value":"{\"id\":\"453\",\"status\":\"Accepted\",\"reason\":\"Accepted! fcxcvxcvxvxcvxcv\",\"user_id\":565}","old_value":"{}","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-03-18 23:26:35"}
{"level":"error","message":"Failed to log to database or Excel: Corrupted zip or bug: expected 16 records in central dir, got 0","timestamp":"2025-03-18 23:26:36"}
{"level":"error","message":"Failed to log to database or Excel: Corrupted zip or bug: expected 16 records in central dir, got 0","timestamp":"2025-03-18 23:26:36"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-18 23:34:19"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-18 23:34:19"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","description":"New organization created","ip_address":"::1","method":"POST","new_value":"{\"first_name\":\"huzaifa\",\"last_name\":\"khan\",\"middle_name\":\"\",\"email\":\"<EMAIL>\",\"status\":\"Screened\",\"gender\":\"male\",\"address\":\"mardan\",\"contact_number\":\"10000000000\",\"date_of_birth\":\"01/01/1999\",\"stipend\":\"2\",\"study_enrolled_ids\":\"1\",\"notification\":\"some notification\",\"note\":\"Note for admin\",\"role_id\":\"10\",\"organization_detail_id\":\"1\",\"ecrf_id\":\"A101-002\",\"timezone\":\"Asia/Karachi\"}","old_value":"null","operation":"CREATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-18 23:34:23"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-18 23:34:43"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-18 23:34:43"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-18 23:36:54"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-18 23:36:54"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","description":"New organization created","ip_address":"::1","method":"POST","new_value":"{\"first_name\":\"huzaifa\",\"last_name\":\"khan\",\"middle_name\":\"\",\"email\":\"<EMAIL>\",\"status\":\"Screened\",\"gender\":\"male\",\"address\":\"mardan\",\"contact_number\":\"10000000000\",\"date_of_birth\":\"01/01/1999\",\"stipend\":\"2\",\"study_enrolled_ids\":\"3\",\"notification\":\"some notification\",\"note\":\"Note for admin\",\"role_id\":\"10\",\"organization_detail_id\":\"1\",\"ecrf_id\":\"A101-001\",\"timezone\":\"Asia/Karachi\"}","old_value":"null","operation":"CREATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-18 23:36:58"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-18 23:38:13"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-18 23:38:14"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","description":"New organization created","ip_address":"::1","method":"POST","new_value":"{\"first_name\":\"abbas\",\"last_name\":\"khan\",\"middle_name\":\"\",\"email\":\"<EMAIL>\",\"status\":\"Screened\",\"gender\":\"male\",\"address\":\"mardan\",\"contact_number\":\"10000000000\",\"date_of_birth\":\"01/01/1999\",\"stipend\":\"2\",\"study_enrolled_ids\":\"3\",\"notification\":\"some notification\",\"note\":\"Note for admin\",\"role_id\":\"10\",\"organization_detail_id\":\"1\",\"ecrf_id\":\"A101-002\",\"timezone\":\"Pacific/Honolulu\"}","old_value":"null","operation":"CREATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-18 23:38:17"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/456","browser":"Chrome 134.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-03-18 23:38:50"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-18 23:38:50"}
{"level":"info","message":{"api_url":"/schedule/createSchedule","browser":"Other 0.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-03-18 23:38:53"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-18 23:38:53"}
{"level":"info","message":{"api_url":"/schedule/createSchedule","browser":"Other 0.0.0","description":"Schedule created successfully for user 568","ip_address":"::1","method":"POST","new_value":"{}","old_value":"null","operation":"CREATE","table_name":"Schedule","user":"<EMAIL>"},"timestamp":"2025-03-18 23:39:02"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/456","browser":"Chrome 134.0.0","description":"Auto-schedule created for user 568","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":568,\"schedule\":{\"schedule_date\":\"2025-03-18\",\"schedule_time\":\"09:00\",\"study_enrolled_id\":\"3\",\"status\":\"Scheduled\",\"user_id\":568,\"note\":\"Auto-Created Schedule\"}}","old_value":"null","operation":"SCHEDULE_CREATED","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-03-18 23:39:05"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/456","browser":"Chrome 134.0.0","description":"Welcome email sent to abbas khan (<EMAIL>)","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":568,\"email\":\"<EMAIL>\",\"subject\":\"Welcome to ResearchHero!\"}","old_value":"null","operation":"EMAIL_SENT","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-03-18 23:39:05"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/456","browser":"Chrome 134.0.0","description":"Registration status updated to Accepted for ID 456","ip_address":"::1","method":"PUT","new_value":"{\"id\":\"456\",\"status\":\"Accepted\",\"reason\":\"Accepted! fcxcvxcvxvxcvxcv\",\"user_id\":568}","old_value":"{}","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-03-18 23:39:05"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/455","browser":"Chrome 134.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-03-18 23:39:18"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-18 23:39:19"}
{"level":"info","message":{"api_url":"/schedule/createSchedule","browser":"Other 0.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-03-18 23:39:21"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-18 23:39:21"}
{"level":"info","message":{"api_url":"/schedule/createSchedule","browser":"Other 0.0.0","description":"Schedule created successfully for user 567","ip_address":"::1","method":"POST","new_value":"{}","old_value":"null","operation":"CREATE","table_name":"Schedule","user":"<EMAIL>"},"timestamp":"2025-03-18 23:39:31"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/455","browser":"Chrome 134.0.0","description":"Auto-schedule created for user 567","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":567,\"schedule\":{\"schedule_date\":\"2025-03-18\",\"schedule_time\":\"09:00\",\"study_enrolled_id\":\"3\",\"status\":\"Scheduled\",\"user_id\":567,\"note\":\"Auto-Created Schedule\"}}","old_value":"null","operation":"SCHEDULE_CREATED","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-03-18 23:39:33"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/455","browser":"Chrome 134.0.0","description":"Welcome email sent to huzaifa khan (<EMAIL>)","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":567,\"email\":\"<EMAIL>\",\"subject\":\"Welcome to ResearchHero!\"}","old_value":"null","operation":"EMAIL_SENT","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-03-18 23:39:34"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/455","browser":"Chrome 134.0.0","description":"Registration status updated to Accepted for ID 455","ip_address":"::1","method":"PUT","new_value":"{\"id\":\"455\",\"status\":\"Accepted\",\"reason\":\"Accepted! fcxcvxcvxvxcvxcv\",\"user_id\":567}","old_value":"{}","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-03-18 23:39:34"}
{"level":"error","message":"Failed to log to database or Excel: Corrupted zip or bug: expected 16 records in central dir, got 0","timestamp":"2025-03-18 23:39:34"}
{"level":"error","message":"Failed to log to database or Excel: Corrupted zip or bug: expected 16 records in central dir, got 0","timestamp":"2025-03-18 23:39:34"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/532","browser":"Chrome 134.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-18 23:40:10"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-18 23:40:11"}
{"level":"error","message":"Failed to log to database or Excel: Cannot read properties of undefined (reading 'headers')","timestamp":"2025-03-18 23:40:14"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/532","browser":"Chrome 134.0.0","description":"Auto Create Medicine","ip_address":"::1","method":"PUT","new_value":"{\"medication_name\":\"Sunobinop Or Placebo\",\"dosage\":\"0.5mg or 1.0mg or 2.0mg\",\"dosage_times\":[\"09:00 PM\"],\"frequencyType\":\"QD\",\"frequencyTime\":\"N/A\",\"frequencyCondition\":\"At Bedtime\",\"dosageType\":\"Tablet\",\"allot_medicine\":\"1\",\"route\":\"Oral\",\"note\":\"Auto-created medicine\",\"user_id\":\"568\",\"investigator_id\":\"403\",\"tracker_time\":\"2025-03-18T18:40:13.463Z\"}","old_value":"null","operation":"CREATE","table_name":"Auto Create Medicine Due to Subject Status Update","user":"<EMAIL>"},"timestamp":"2025-03-18 23:40:17"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/532","browser":"Chrome 134.0.0","description":"xxxxxzxzzzzzzzzzzzzzzzzzzzzzzzz","ip_address":"::1","method":"PUT","new_value":"{\"email\":\"<EMAIL>\",\"middle_name\":\"\",\"status\":\"Randomized\",\"reason\":\"xxxxxzxzzzzzzzzzzzzzzzzzzzzzzzz\"}","old_value":"{\"middle_name\":null,\"status\":\"Screened\"}","operation":"UPDATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-18 23:40:18"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/531","browser":"Chrome 134.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-18 23:40:30"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-18 23:40:30"}
{"level":"error","message":"Failed to log to database or Excel: Cannot read properties of undefined (reading 'headers')","timestamp":"2025-03-18 23:40:33"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/531","browser":"Chrome 134.0.0","description":"Auto Create Medicine","ip_address":"::1","method":"PUT","new_value":"{\"medication_name\":\"Sunobinop Or Placebo\",\"dosage\":\"0.5mg or 1.0mg or 2.0mg\",\"dosage_times\":[\"09:00 PM\"],\"frequencyType\":\"QD\",\"frequencyTime\":\"N/A\",\"frequencyCondition\":\"At Bedtime\",\"dosageType\":\"Tablet\",\"allot_medicine\":\"1\",\"route\":\"Oral\",\"note\":\"Auto-created medicine\",\"user_id\":\"567\",\"investigator_id\":\"403\",\"tracker_time\":\"2025-03-18T18:40:32.179Z\"}","old_value":"null","operation":"CREATE","table_name":"Auto Create Medicine Due to Subject Status Update","user":"<EMAIL>"},"timestamp":"2025-03-18 23:40:35"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/531","browser":"Chrome 134.0.0","description":"xxxxzzzzzzzzzzzzzzzzzzzzz","ip_address":"::1","method":"PUT","new_value":"{\"email\":\"<EMAIL>\",\"middle_name\":\"\",\"status\":\"Randomized\",\"reason\":\"xxxxzzzzzzzzzzzzzzzzzzzzz\"}","old_value":"{\"middle_name\":null,\"status\":\"Screened\"}","operation":"UPDATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-18 23:40:36"}
