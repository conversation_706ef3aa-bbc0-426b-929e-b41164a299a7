{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 134.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-04-11 15:22:58"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/543","browser":"Chrome 134.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-04-11 15:23:41"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-04-11 15:23:42"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/543","browser":"Chrome 134.0.0","description":"chheeeeeeeeeeeeeeeeeeeee","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":\"581\",\"address\":\"U.S.A.\",\"reason\":\"chheeeeeeeeeeeeeeeeeeeee\"}","old_value":"{\"user_id\":\"581\",\"address\":\"U.S.A\"}","operation":"UPDATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-04-11 15:23:43"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/543","browser":"Chrome 134.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-04-11 15:24:20"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-04-11 15:24:20"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/543","browser":"Chrome 134.0.0","description":"hhhhhhhhhhhhhhhhhhhhhh","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":\"581\",\"address\":\"U.S.A.nn\",\"reason\":\"hhhhhhhhhhhhhhhhhhhhhh\"}","old_value":"{\"user_id\":\"581\",\"address\":\"U.S.A.\"}","operation":"UPDATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-04-11 15:24:21"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/543","browser":"Chrome 134.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-04-11 15:24:49"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-04-11 15:24:49"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/543","browser":"Chrome 134.0.0","description":"upfsyrf yr nsrs","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":\"581\",\"address\":\"U.S.A.\",\"reason\":\"upfsyrf yr nsrs\"}","old_value":"{\"user_id\":\"581\",\"address\":\"U.S.A.nn\"}","operation":"UPDATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-04-11 15:24:51"}
{"level":"info","message":"{\"method\":\"POST\",\"api_url\":\"/auth/signin\",\"table_name\":\"Organization\",\"operation\":\"SIGNIN_ATTEMPT\",\"description\":\"Sign-in attempt failed: Invalid email or password\",\"old_value\":\"{\\\"email\\\":\\\"<EMAIL>\\\"}\",\"new_value\":null,\"browser\":\"Chrome 134.0.0\",\"ip_address\":\"::1\",\"user\":\"<EMAIL>\"}","timestamp":"2025-04-11 16:09:02"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-04-11 16:09:27"}
{"level":"info","message":"{\"method\":\"POST\",\"api_url\":\"/auth/signin\",\"table_name\":\"Organization\",\"operation\":\"SIGNIN_ATTEMPT\",\"description\":\"Sign-in attempt failed: Invalid email or password\",\"old_value\":\"{\\\"email\\\":\\\"<EMAIL>\\\"}\",\"new_value\":null,\"browser\":\"Chrome 134.0.0\",\"ip_address\":\"::1\",\"user\":\"<EMAIL>\"}","timestamp":"2025-04-11 16:10:06"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 134.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-04-11 16:10:53"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Other 0.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-04-11 21:15:54"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 134.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-04-11 21:52:59"}
