const db = require("../../config/DBConnection3");
const crypto = require("crypto");

const ENCRYPTION_KEY = Buffer.from(
  "KY73owoqZwuKwBy7ndP5hMnm01TNcl0/PTNZoLnkYtk=",
  "base64"
); // Decoding Base64 key to <PERSON>uffer
const IV_LENGTH = 16; // For AES, this is always 16

function decrypt(text) {
  if (!text) return text; // Return if text is null or undefined
  let textParts = text.split(":");
  let iv = Buffer.from(textParts.shift(), "hex");
  let encryptedText = Buffer.from(textParts.join(":"), "hex");
  let decipher = crypto.createDecipheriv("aes-256-cbc", ENCRYPTION_KEY, iv);
  let decrypted = decipher.update(encryptedText, "hex", "utf8");
  decrypted += decipher.final("utf8");
  return decrypted;
}

const getScaleReportModel = async () => {
  try {
    const query = `
    SELECT org.organization_name,org.organization_address, o.user_id, o.ecrf_id, o.date_enrolled,o.stipend,  st.scale_name, sd.day_name, ss.schedule_name, s.filled_by, sig.investigatorId,sig.created_at, o.status FROM signature AS sig JOIN organization AS o ON sig.user_id = o.user_id JOIN organization_details AS org On o.organization_detail_id = org.organization_detail_id JOIN schedule_days AS sd ON sig.day_id = sd.day_id JOIN study_schedules AS ss ON sd.schedule_id = ss.schedule_id JOIN scale AS s ON sig.scale_id = s.scale_id JOIN scale_translations AS st ON s.scale_id = st.scale_id
    `;
    const [result] = await db.execute(query);

    const decryptDate = result.map((data) => {
      return {
        ...data,
        investigator_first_name: decrypt(data.investigator_first_name),
        investigator_last_name: decrypt(data.investigator_last_name),
      };
    });

    const enrollmentlog = `SELECT o.ecrf_id ,  o.status, o.date_enrolled FROM organization AS o JOIN user_role AS ur ON o.user_id = ur.user_id WHERE ur.role_id = 10;`;
    const [result2] = await db.execute(enrollmentlog);
    const enroll_decrypt = result2.map((data) => {
      return {
        ...data,
        gender: decrypt(data.gender),
      };
    });

    const scheduleLog = `SELECT o.ecrf_id,  s.schedule_date, s.schedule_time,s.status,d.day_name FROM schedule AS s 
JOIN organization AS o ON s.user_id = o.user_id
JOIN schedule_days AS d ON s.day_id = d.day_id;
`;
    const [result3] = await db.execute(scheduleLog);
    const schedule_decrypt = result3.map((data) => {
      return {
        ...data,
        gender: decrypt(data.gender),
      };
    });

    const subjectIdentificationLog = `SELECT se.study_name, o.ecrf_id FROM organization AS o
JOIN study_enrolled AS se ON o.study_enrolled_id = se.enrolled_id
JOIN user_role AS ur ON o.user_id = ur.user_id
WHERE ur.role_id = 10;
`;
    const [result4] = await db.execute(subjectIdentificationLog);
    const subjectIdentification_decrypt = result4.map((data) => {
      return {
        ...data,
        first_name: decrypt(data.first_name),
        last_name: decrypt(data.last_name),
        gender: decrypt(data.gender),
        contact_number: decrypt(data.contact_number),
      };
    });

    //     const medicineComplaince = `SELECT o.ecrf_id, m.medication_name, m.dosage, m.frequency_type,smr.intake_quantity, smr.date AS dosageDate, smr.time AS dosageTime, smr.created_at FROM patientmedications AS m
    // JOIN organization AS o ON m.user_id = o.user_id
    // JOIN submit_medicine_records AS smr ON m.medication_id = smr.medicine_id
    // `;
    const medicineComplaince = `
      SELECT 
        o.ecrf_id, 
        m.medication_name, 
        m.dosage, 
        m.frequency_type, 
        smr.intake_quantity, 
        smr.date AS dosageDate, 
        smr.time AS dosageTime, 
        smr.created_at AS record_created_at,
        rd.reason,
        mc.comments,
        mc.created_at AS comment_created_at
      FROM patientmedications AS m
      JOIN organization AS o ON m.user_id = o.user_id
      JOIN submit_medicine_records AS smr ON m.medication_id = smr.medicine_id
      LEFT JOIN reason_description AS rd ON m.medication_id = rd.track_id
      LEFT JOIN medicine_comments AS mc ON m.medication_id = mc.medicine_id
    `;
    const [result5] = await db.execute(medicineComplaince);

    const medicineTakenHistory = `SELECT o.ecrf_id,m.created_at, m.dosage,m.dosageType, m.allot_medicine, md.dosage_time, m.frequency_time,m.frequency_condition, m.route FROM patientmedications AS m JOIN organization AS o ON m.user_id = o.user_id JOIN medication_dosage_times AS md ON m.medication_id = md.medication_id
`;
    const [result6] = await db.execute(medicineTakenHistory);

    const AEIncidentLog = `
SELECT
    ats.ticket_id,
    o.ecrf_id,
    org.organization_name AS site_name,
    ats.start_date AS patient_reported_datetime,
    iqr.incident_severety AS severity,  -- Only one reference to incident severity

    -- Existing columns
    
    irq.question_text AS incident_question,
    iqr.response_text AS incident_response,
    ir.description AS incident_description,
    ir.created_at AS Ticket_Creation_Time,
    ats.status AS ticket_status,
    eq.question AS ecrf_question,
    ea.answer AS ecrf_answer,
    iaq.question_text AS aesi_question,
    iaqo.option_text AS aesi_option,
    aqr.description AS aesi_description,
    aqr.created_at AS aesi_created_at,
    ta.history_text,
    ta.action_type,
    ta.created_at AS activity_created_at,
    ua.email AS activity_user_email

    

FROM adverse_ticketing_system ats
INNER JOIN incident_reports ir
    ON ats.incident_report_id = ir.id
INNER JOIN user u
    ON ir.user_id = u.user_id
INNER JOIN organization o
    ON u.user_id = o.user_id
INNER JOIN organization_details AS org 
    ON o.organization_detail_id = org.organization_detail_id
INNER JOIN incident_question_response iqr
    ON ir.id = iqr.incident_report_id
INNER JOIN incident_report_question irq
    ON iqr.question_id = irq.question_id

LEFT JOIN ecrf_submissions es
    ON ats.ticket_id = es.ticket_id
LEFT JOIN ecrf_answers ea
    ON es.id = ea.submission_id
    AND ats.ticket_id = es.ticket_id
LEFT JOIN ecrf_questions eq
    ON ea.question_id = eq.id

LEFT JOIN aesi_question_response aqr
    ON ats.ticket_id = aqr.ticket_id
LEFT JOIN investigator_aesi_question iaq
    ON aqr.question_id = iaq.question_id
LEFT JOIN investigator_aesi_question_option iaqo
    ON aqr.option_id = iaqo.option_id

LEFT JOIN ticket_activity ta
    ON ats.ticket_id = ta.ticket_id
LEFT JOIN user ua
    ON ta.user_id = ua.user_id

ORDER BY
    ats.ticket_id,
    es.id,
    eq.id,
    aqr.created_at,
    ta.created_at;
`;

    const [result7] = await db.execute(AEIncidentLog);

    return (data = {
      scale_report: decryptDate,
      enrollment_log: enroll_decrypt,
      schedule_log: schedule_decrypt,
      subject_identification_log: subjectIdentification_decrypt,
      dosage_compliance: result5,
      dosage_taken_history: result6,
      AE_incident_log: result7,
    });
  } catch (error) {
    throw error;
  }
};
// const getScaleReportModel = async () => {
//   try {
//     const query = `
//     SELECT org.organization_name,org.organization_address, o.user_id, o.ecrf_id, o.date_enrolled,o.stipend, o.date_of_birth, st.scale_name, sd.day_name, ss.schedule_name, s.filled_by, sig.investigatorId,sig.created_at,o.first_name AS investigator_first_name, o.last_name AS investigator_last_name, o.status FROM signature AS sig JOIN organization AS o ON sig.user_id = o.user_id JOIN organization_details AS org On o.organization_detail_id = org.organization_detail_id JOIN schedule_days AS sd ON sig.day_id = sd.day_id JOIN study_schedules AS ss ON sd.schedule_id = ss.schedule_id JOIN scale AS s ON sig.scale_id = s.scale_id JOIN scale_translations AS st ON s.scale_id = st.scale_id
//     `;
//     const [result] = await db.execute(query);

//     const decryptDate = result.map((data) => {
//       return {
//         ...data,
//         investigator_first_name: decrypt(data.investigator_first_name),
//         investigator_last_name: decrypt(data.investigator_last_name),
//       };
//     });

//     const enrollmentlog = `SELECT o.ecrf_id , o.date_of_birth, o.gender, o.status, o.date_enrolled FROM organization AS o JOIN user_role AS ur ON o.user_id = ur.user_id WHERE ur.role_id = 10;`;
//     const [result2] = await db.execute(enrollmentlog);
//     const enroll_decrypt = result2.map((data) => {
//       return {
//         ...data,
//         gender: decrypt(data.gender),
//       };
//     });

//     const scheduleLog = `SELECT o.ecrf_id, o.date_of_birth, o.gender, s.schedule_date, s.schedule_time,s.status,d.day_name FROM schedule AS s
// JOIN organization AS o ON s.user_id = o.user_id
// JOIN schedule_days AS d ON s.day_id = d.day_id;
// `;
//     const [result3] = await db.execute(scheduleLog);
//     const schedule_decrypt = result3.map((data) => {
//       return {
//         ...data,
//         gender: decrypt(data.gender),
//       };
//     });

//     const subjectIdentificationLog = `SELECT se.study_name, o.ecrf_id, o.first_name, o.last_name, o.date_of_birth, o.gender,o.contact_number FROM organization AS o
// JOIN study_enrolled AS se ON o.study_enrolled_id = se.enrolled_id
// JOIN user_role AS ur ON o.user_id = ur.user_id
// WHERE ur.role_id = 10;
// `;
//     const [result4] = await db.execute(subjectIdentificationLog);
//     const subjectIdentification_decrypt = result4.map((data) => {
//       return {
//         ...data,
//         first_name: decrypt(data.first_name),
//         last_name: decrypt(data.last_name),
//         gender: decrypt(data.gender),
//         contact_number: decrypt(data.contact_number),
//       };
//     });

//     //     const medicineComplaince = `SELECT o.ecrf_id, m.medication_name, m.dosage, m.frequency_type,smr.intake_quantity, smr.date AS dosageDate, smr.time AS dosageTime, smr.created_at FROM patientmedications AS m
//     // JOIN organization AS o ON m.user_id = o.user_id
//     // JOIN submit_medicine_records AS smr ON m.medication_id = smr.medicine_id
//     // `;
//     const medicineComplaince = `
//       SELECT
//         o.ecrf_id,
//         m.medication_name,
//         m.dosage,
//         m.frequency_type,
//         smr.intake_quantity,
//         smr.date AS dosageDate,
//         smr.time AS dosageTime,
//         smr.created_at AS record_created_at,
//         rd.reason,
//         mc.comments,
//         mc.created_at AS comment_created_at
//       FROM patientmedications AS m
//       JOIN organization AS o ON m.user_id = o.user_id
//       JOIN submit_medicine_records AS smr ON m.medication_id = smr.medicine_id
//       LEFT JOIN reason_description AS rd ON m.medication_id = rd.track_id
//       LEFT JOIN medicine_comments AS mc ON m.medication_id = mc.medicine_id
//     `;
//     const [result5] = await db.execute(medicineComplaince);

//     const medicineTakenHistory = `SELECT o.ecrf_id,m.created_at, m.dosage,m.dosageType, m.allot_medicine, md.dosage_time, m.frequency_time,m.frequency_condition, m.route FROM patientmedications AS m JOIN organization AS o ON m.user_id = o.user_id JOIN medication_dosage_times AS md ON m.medication_id = md.medication_id
// `;
//     const [result6] = await db.execute(medicineTakenHistory);

//     const AEIncidentLog = `
// SELECT
//     ats.ticket_id,
//     o.ecrf_id,
//     org.organization_name AS site_name,
//     ats.start_date AS patient_reported_datetime,
//     iqr.incident_severety AS severity,  -- Only one reference to incident severity

//     -- Existing columns
//     es.id AS submission_id,
//     irq.question_text AS incident_question,
//     iqr.response_text AS incident_response,
//     ir.description AS incident_description,
//     ir.created_at AS Ticket_Creation_Time,
//     ats.status AS ticket_status,
//     eq.question AS ecrf_question,
//     ea.answer AS ecrf_answer,
//     iaq.question_text AS aesi_question,
//     iaqo.option_text AS aesi_option,
//     aqr.description AS aesi_description,
//     aqr.created_at AS aesi_created_at,
//     ta.history_text,
//     ta.action_type,
//     ta.created_at AS activity_created_at,
//     ua.email AS activity_user_email,

//     -- New column: Person who closed
//     (
//         SELECT ua2.email
//         FROM \`ticket_activity\` ta2
//         JOIN \`user\` ua2 ON ta2.user_id = ua2.user_id
//         WHERE
//             ta2.ticket_id = ats.ticket_id
//             AND ta2.action_type = 'status_change'
//             AND ta2.history_text LIKE '%Closed%'
//         ORDER BY ta2.created_at DESC
//         LIMIT 1
//     ) AS person_who_closed

// FROM \`adverse_ticketing_system\` ats
// INNER JOIN \`incident_reports\` ir
//     ON ats.incident_report_id = ir.id
// INNER JOIN \`user\` u
//     ON ir.user_id = u.user_id
// INNER JOIN \`organization\` o
//     ON u.user_id = o.user_id
// INNER JOIN \`organization_details\` AS org
//     ON o.organization_detail_id = org.organization_detail_id
// INNER JOIN \`incident_question_response\` iqr
//     ON ir.id = iqr.incident_report_id
// INNER JOIN \`incident_report_question\` irq
//     ON iqr.question_id = irq.question_id

// LEFT JOIN \`ecrf_submissions\` es
//     ON ats.ticket_id = es.ticket_id
// LEFT JOIN \`ecrf_answers\` ea
//     ON es.id = ea.submission_id
//     AND ats.ticket_id = es.ticket_id
// LEFT JOIN \`ecrf_questions\` eq
//     ON ea.question_id = eq.id

// LEFT JOIN \`aesi_question_response\` aqr
//     ON ats.ticket_id = aqr.ticket_id
// LEFT JOIN \`investigator_aesi_question\` iaq
//     ON aqr.question_id = iaq.question_id
// LEFT JOIN \`investigator_aesi_question_option\` iaqo
//     ON aqr.option_id = iaqo.option_id

// LEFT JOIN \`ticket_activity\` ta
//     ON ats.ticket_id = ta.ticket_id
// LEFT JOIN \`user\` ua
//     ON ta.user_id = ua.user_id

// ORDER BY
//     ats.ticket_id,
//     es.id,
//     eq.id,
//     aqr.created_at,
//     ta.created_at;
// `;

//     const [result7] = await db.execute(AEIncidentLog);

//     return (data = {
//       scale_report: decryptDate,
//       enrollment_log: enroll_decrypt,
//       schedule_log: schedule_decrypt,
//       subject_identification_log: subjectIdentification_decrypt,
//       dosage_complaince: result5,
//       dosage_taken_history: result6,
//       AE_incident_log: result7,
//     });
//   } catch (error) {
//     throw error;
//   }
// };

module.exports = {
  getScaleReportModel,
};
