{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-09 22:01:42"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-09 22:02:33"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-09 22:04:01"}
{"level":"error","message":"Failed to log to database: Data too long for column 'description' at row 1","timestamp":"2025-01-09 22:04:03"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-09 22:05:28"}
{"level":"error","message":"Failed to log to database: Data too long for column 'description' at row 1","timestamp":"2025-01-09 22:05:32"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-09 22:38:56"}
{"level":"error","message":"Failed to log to database: Data too long for column 'description' at row 1","timestamp":"2025-01-09 22:38:59"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-09 22:39:28"}
{"level":"error","message":"Failed to log to database: Data too long for column 'description' at row 1","timestamp":"2025-01-09 22:39:30"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-09 23:18:14"}
{"level":"error","message":"Failed to log to database: Data too long for column 'description' at row 1","timestamp":"2025-01-09 23:18:16"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-09 23:28:17"}
{"level":"error","message":"Failed to log to database: Data too long for column 'description' at row 1","timestamp":"2025-01-09 23:28:19"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-09 23:29:27"}
{"level":"error","message":"Failed to log to database: Data too long for column 'description' at row 1","timestamp":"2025-01-09 23:29:29"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-09 23:32:49"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-09 23:33:11"}
{"level":"error","message":"Failed to log to database: Data too long for column 'description' at row 1","timestamp":"2025-01-09 23:33:14"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-09 23:39:33"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-09 23:42:18"}
{"level":"error","message":"Failed to log to database: Data too long for column 'description' at row 1","timestamp":"2025-01-09 23:42:21"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-09 23:43:44"}
{"level":"error","message":"Failed to log to database: Data too long for column 'description' at row 1","timestamp":"2025-01-09 23:43:46"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-09 23:52:15"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-09 12:55:17"}
{"level":"error","message":"Failed to log to database: Data too long for column 'description' at row 1","timestamp":"2025-01-09 12:55:19"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-09 12:57:06"}
{"level":"error","message":"Failed to log to database: Data too long for column 'description' at row 1","timestamp":"2025-01-09 12:57:07"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-09 23:58:48"}
{"level":"error","message":"Failed to log to database: Data too long for column 'description' at row 1","timestamp":"2025-01-09 23:58:50"}
