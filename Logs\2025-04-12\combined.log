{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 134.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-04-12 22:34:58"}
{"level":"info","message":{"api_url":"/schedule/create_manual_schedule","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"Manual schedule","user":"<EMAIL>"},"timestamp":"2025-04-12 22:37:06"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-04-12 22:37:06"}
{"level":"info","message":{"api_url":"/schedule/create_manual_schedule","browser":"Chrome 134.0.0","description":"Schedule Created successfully for user: 578","ip_address":"::1","method":"POST","new_value":"{}","old_value":"null","operation":"CREATE","table_name":"Manual Schedule","user":"<EMAIL>"},"timestamp":"2025-04-12 22:37:07"}
{"level":"info","message":{"api_url":"/schedule/create_manual_schedule","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"Manual schedule","user":"<EMAIL>"},"timestamp":"2025-04-12 22:40:57"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-04-12 22:40:57"}
{"level":"info","message":{"api_url":"/schedule/create_manual_schedule","browser":"Chrome 134.0.0","description":"Schedule Created successfully for user: 578","ip_address":"::1","method":"POST","new_value":"{}","old_value":"null","operation":"CREATE","table_name":"Manual Schedule","user":"<EMAIL>"},"timestamp":"2025-04-12 22:40:58"}
{"level":"info","message":"{\"method\":\"POST\",\"api_url\":\"/auth/signin\",\"table_name\":\"Organization\",\"operation\":\"SIGNIN_ATTEMPT\",\"description\":\"Sign-in attempt failed: Invalid email or password\",\"old_value\":\"{\\\"email\\\":\\\"<EMAIL>\\\"}\",\"new_value\":null,\"browser\":\"Other 0.0.0\",\"ip_address\":\"::1\",\"user\":\"<EMAIL>\"}","timestamp":"2025-04-12 22:50:44"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Other 0.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-04-12 22:51:02"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 134.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-04-12 22:52:25"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 134.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-04-12 23:09:56"}
