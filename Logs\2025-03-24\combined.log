{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 134.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-24 22:05:52"}
{"level":"info","message":{"api_url":"/organization/createPersonnel","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-24 22:12:22"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-24 22:12:22"}
{"level":"info","message":{"api_url":"/organization/createPersonnel","browser":"Chrome 134.0.0","description":"Personnel created with email: <EMAIL>","ip_address":"::1","method":"POST","new_value":"{\"userId\":572}","old_value":"null","operation":"CREATE","table_name":"personnel","user":"<EMAIL>"},"timestamp":"2025-03-24 22:12:25"}
{"level":"info","message":{"api_url":"/organization/createPersonnel","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-24 22:13:29"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-24 22:13:29"}
{"level":"info","message":{"api_url":"/organization/createPersonnel","browser":"Chrome 134.0.0","description":"Personnel created with email: <EMAIL>","ip_address":"::1","method":"POST","new_value":"{\"userId\":573}","old_value":"null","operation":"CREATE","table_name":"personnel","user":"<EMAIL>"},"timestamp":"2025-03-24 22:13:32"}
{"level":"info","message":{"api_url":"/organization/createPersonnel","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-24 22:15:37"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-24 22:15:37"}
{"level":"info","message":{"api_url":"/organization/createPersonnel","browser":"Chrome 134.0.0","description":"Personnel created with email: <EMAIL>","ip_address":"::1","method":"POST","new_value":"{\"userId\":574}","old_value":"null","operation":"CREATE","table_name":"personnel","user":"<EMAIL>"},"timestamp":"2025-03-24 22:15:40"}
{"level":"info","message":{"api_url":"/organization/createPersonnel","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-24 22:17:34"}
{"level":"info","message":{"api_url":"/organization/createPersonnel","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-24 22:17:34"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-24 22:17:35"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-24 22:17:35"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Other 0.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-24 22:23:32"}
{"level":"info","message":{"api_url":"/organization/createPersonnel","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-24 22:25:36"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-24 22:25:36"}
{"level":"info","message":{"api_url":"/organization/createPersonnel","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-24 22:26:17"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-24 22:26:18"}
{"level":"info","message":{"api_url":"/organization/createPersonnel","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-24 22:27:02"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-24 22:27:02"}
{"level":"info","message":{"api_url":"/organization/createPersonnel","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-24 22:27:30"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-24 22:27:30"}
{"level":"info","message":{"api_url":"/organization/createPersonnel","browser":"Chrome 134.0.0","description":"Personnel created with email: <EMAIL>","ip_address":"::1","method":"POST","new_value":"{\"userId\":580}","old_value":"null","operation":"CREATE","table_name":"personnel","user":"<EMAIL>"},"timestamp":"2025-03-24 22:27:33"}
{"level":"info","message":{"api_url":"/organization/createPersonnel","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-24 22:29:55"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-24 22:29:55"}
{"level":"info","message":{"api_url":"/organization/createPersonnel","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-24 22:30:28"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-24 22:30:29"}
{"level":"info","message":{"api_url":"/organization/createPersonnel","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-24 22:31:05"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-24 22:31:06"}
{"level":"info","message":{"api_url":"/organization/createPersonnel","browser":"Chrome 134.0.0","description":"Personnel created with email: <EMAIL>","ip_address":"::1","method":"POST","new_value":"{\"userId\":583}","old_value":"null","operation":"CREATE","table_name":"personnel","user":"<EMAIL>"},"timestamp":"2025-03-24 22:31:08"}
{"level":"info","message":{"api_url":"/organization/createPersonnel","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-24 22:33:22"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-24 22:33:23"}
{"level":"info","message":{"api_url":"/organization/createPersonnel","browser":"Chrome 134.0.0","description":"Personnel created with email: <EMAIL>","ip_address":"::1","method":"POST","new_value":"{\"userId\":584}","old_value":"null","operation":"CREATE","table_name":"personnel","user":"<EMAIL>"},"timestamp":"2025-03-24 22:33:26"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/465","browser":"Chrome 134.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-03-24 22:33:56"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-24 22:33:57"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/465","browser":"Chrome 134.0.0","description":"Welcome email sent to mr worrier (<EMAIL>)","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":584,\"email\":\"<EMAIL>\",\"subject\":\"Welcome to ResearchHero!\"}","old_value":"null","operation":"EMAIL_SENT","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-03-24 22:34:02"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/465","browser":"Chrome 134.0.0","description":"Registration status updated to Accepted for ID 465","ip_address":"::1","method":"PUT","new_value":"{\"id\":\"465\",\"status\":\"Accepted\",\"reason\":\"Accepted By Super Admin \",\"user_id\":584}","old_value":"{}","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-03-24 22:34:02"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/464","browser":"Chrome 134.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-03-24 22:34:34"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-24 22:34:35"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/464","browser":"Chrome 134.0.0","description":"Welcome email sent to muhammadanas khan (<EMAIL>)","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":583,\"email\":\"<EMAIL>\",\"subject\":\"Welcome to ResearchHero!\"}","old_value":"null","operation":"EMAIL_SENT","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-03-24 22:34:40"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/464","browser":"Chrome 134.0.0","description":"Registration status updated to Accepted for ID 464","ip_address":"::1","method":"PUT","new_value":"{\"id\":\"464\",\"status\":\"Accepted\",\"reason\":\"Accepted By Super Admin \",\"user_id\":583}","old_value":"{}","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-03-24 22:34:40"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/463","browser":"Chrome 134.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-03-24 22:34:58"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-24 22:34:59"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/463","browser":"Chrome 134.0.0","description":"Welcome email sent to anas khan (<EMAIL>)","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":580,\"email\":\"<EMAIL>\",\"subject\":\"Welcome to ResearchHero!\"}","old_value":"null","operation":"EMAIL_SENT","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-03-24 22:35:04"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/463","browser":"Chrome 134.0.0","description":"Registration status updated to Accepted for ID 463","ip_address":"::1","method":"PUT","new_value":"{\"id\":\"463\",\"status\":\"Accepted\",\"reason\":\"Accepted By Super Admin \",\"user_id\":580}","old_value":"{}","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-03-24 22:35:04"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/462","browser":"Chrome 134.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-03-24 22:35:20"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-24 22:35:20"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/462","browser":"Chrome 134.0.0","description":"Welcome email sent to huzaifa khan (<EMAIL>)","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":574,\"email\":\"<EMAIL>\",\"subject\":\"Welcome to ResearchHero!\"}","old_value":"null","operation":"EMAIL_SENT","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-03-24 22:35:25"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/462","browser":"Chrome 134.0.0","description":"Registration status updated to Accepted for ID 462","ip_address":"::1","method":"PUT","new_value":"{\"id\":\"462\",\"status\":\"Accepted\",\"reason\":\"Accepted By Super Admin \",\"user_id\":574}","old_value":"{}","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-03-24 22:35:25"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/461","browser":"Chrome 134.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-03-24 22:35:43"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-24 22:35:43"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/461","browser":"Chrome 134.0.0","description":"Welcome email sent to abbas khan (<EMAIL>)","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":573,\"email\":\"<EMAIL>\",\"subject\":\"Welcome to ResearchHero!\"}","old_value":"null","operation":"EMAIL_SENT","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-03-24 22:35:48"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/461","browser":"Chrome 134.0.0","description":"Registration status updated to Accepted for ID 461","ip_address":"::1","method":"PUT","new_value":"{\"id\":\"461\",\"status\":\"Accepted\",\"reason\":\"Accepted By Super Admin \",\"user_id\":573}","old_value":"{}","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-03-24 22:35:48"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/460","browser":"Chrome 134.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-03-24 22:36:04"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-24 22:36:05"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/460","browser":"Chrome 134.0.0","description":"Welcome email sent to arslan mehmood (<EMAIL>)","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":572,\"email\":\"<EMAIL>\",\"subject\":\"Welcome to ResearchHero!\"}","old_value":"null","operation":"EMAIL_SENT","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-03-24 22:36:10"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/460","browser":"Chrome 134.0.0","description":"Registration status updated to Accepted for ID 460","ip_address":"::1","method":"PUT","new_value":"{\"id\":\"460\",\"status\":\"Accepted\",\"reason\":\"Accepted By Super Admin \",\"user_id\":572}","old_value":"{}","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-03-24 22:36:10"}
{"level":"error","message":"Failed to log to database or Excel: Corrupted zip or bug: expected 16 records in central dir, got 0","timestamp":"2025-03-24 22:36:11"}
{"level":"error","message":"Failed to log to database or Excel: Corrupted zip or bug: expected 16 records in central dir, got 0","timestamp":"2025-03-24 22:36:11"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-24 22:38:07"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-24 22:38:07"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","description":"New organization created","ip_address":"::1","method":"POST","new_value":"{\"first_name\":\"test a\",\"last_name\":\"a\",\"middle_name\":\"\",\"email\":\"<EMAIL>\",\"status\":\"Screened\",\"gender\":\"male\",\"address\":\"mardan\",\"contact_number\":\"10000000000\",\"date_of_birth\":\"01/01/1999\",\"stipend\":\"12\",\"study_enrolled_ids\":\"1\",\"notification\":\"some notification\",\"note\":\"Note for admin\",\"role_id\":\"10\",\"organization_detail_id\":\"1\",\"ecrf_id\":\"A101-001\",\"timezone\":\"Asia/Karachi\"}","old_value":"null","operation":"CREATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-24 22:38:13"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-24 22:38:51"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-24 22:38:52"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","description":"New organization created","ip_address":"::1","method":"POST","new_value":"{\"first_name\":\"test a\",\"last_name\":\"a\",\"middle_name\":\"\",\"email\":\"<EMAIL>\",\"status\":\"Screened\",\"gender\":\"male\",\"address\":\"mardan\",\"contact_number\":\"10000000000\",\"date_of_birth\":\"01/01/1999\",\"stipend\":\"12\",\"study_enrolled_ids\":\"1\",\"notification\":\"some notification\",\"note\":\"Note for admin\",\"role_id\":\"10\",\"organization_detail_id\":\"1\",\"ecrf_id\":\"A101-001\",\"timezone\":\"Asia/Karachi\"}","old_value":"null","operation":"CREATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-24 22:38:56"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-24 22:40:34"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-24 22:40:34"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","description":"New organization created","ip_address":"::1","method":"POST","new_value":"{\"first_name\":\"test a\",\"last_name\":\"a\",\"middle_name\":\"\",\"email\":\"<EMAIL>\",\"status\":\"Screened\",\"gender\":\"male\",\"address\":\"mardan\",\"contact_number\":\"10000000000\",\"date_of_birth\":\"01/01/1999\",\"stipend\":\"12\",\"study_enrolled_ids\":\"1\",\"notification\":\"some notification\",\"note\":\"Note for admin\",\"role_id\":\"10\",\"organization_detail_id\":\"1\",\"ecrf_id\":\"A101-001\",\"timezone\":\"Asia/Karachi\"}","old_value":"null","operation":"CREATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-24 22:40:39"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/468","browser":"Chrome 134.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-03-24 22:41:11"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-24 22:41:12"}
{"level":"info","message":{"api_url":"/schedule/createSchedule","browser":"Other 0.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-03-24 22:41:14"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-24 22:41:15"}
{"level":"info","message":{"api_url":"/schedule/createSchedule","browser":"Other 0.0.0","description":"Schedule created successfully for user 587","ip_address":"::1","method":"POST","new_value":"{}","old_value":"null","operation":"CREATE","table_name":"Schedule","user":"<EMAIL>"},"timestamp":"2025-03-24 22:41:25"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/468","browser":"Chrome 134.0.0","description":"Auto-schedule created for user 587","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":587,\"schedule\":{\"schedule_date\":\"2025-03-24\",\"schedule_time\":\"09:00\",\"study_enrolled_id\":\"1\",\"status\":\"Scheduled\",\"user_id\":587,\"note\":\"Auto-Created Schedule\"}}","old_value":"null","operation":"SCHEDULE_CREATED","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-03-24 22:41:28"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/468","browser":"Chrome 134.0.0","description":"Welcome email sent to test a a (<EMAIL>)","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":587,\"email\":\"<EMAIL>\",\"subject\":\"Welcome to ResearchHero!\"}","old_value":"null","operation":"EMAIL_SENT","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-03-24 22:41:29"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/468","browser":"Chrome 134.0.0","description":"Registration status updated to Accepted for ID 468","ip_address":"::1","method":"PUT","new_value":"{\"id\":\"468\",\"status\":\"Accepted\",\"reason\":\"Accepted By Super Admin \",\"user_id\":587}","old_value":"{}","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-03-24 22:41:29"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-24 22:43:30"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-24 22:43:30"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","description":"New organization created","ip_address":"::1","method":"POST","new_value":"{\"first_name\":\"testb\",\"last_name\":\"b\",\"middle_name\":\"\",\"email\":\"<EMAIL>\",\"status\":\"Screened\",\"gender\":\"male\",\"address\":\"mardan\",\"contact_number\":\"10000000000\",\"date_of_birth\":\"01/01/1999\",\"stipend\":\"12\",\"study_enrolled_ids\":\"2\",\"notification\":\"some notification\",\"note\":\"Note for admin\",\"role_id\":\"10\",\"organization_detail_id\":\"10\",\"ecrf_id\":\"B101-100\",\"timezone\":\"Asia/Karachi\"}","old_value":"null","operation":"CREATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-24 22:43:34"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/469","browser":"Chrome 134.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-03-24 22:44:35"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-24 22:44:36"}
{"level":"info","message":{"api_url":"/schedule/createSchedule","browser":"Other 0.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-03-24 22:44:38"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-24 22:44:38"}
{"level":"info","message":{"api_url":"/schedule/createSchedule","browser":"Other 0.0.0","description":"Schedule created successfully for user 588","ip_address":"::1","method":"POST","new_value":"{}","old_value":"null","operation":"CREATE","table_name":"Schedule","user":"<EMAIL>"},"timestamp":"2025-03-24 22:44:46"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/469","browser":"Chrome 134.0.0","description":"Auto-schedule created for user 588","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":588,\"schedule\":{\"schedule_date\":\"2025-03-24\",\"schedule_time\":\"09:00\",\"study_enrolled_id\":\"2\",\"status\":\"Scheduled\",\"user_id\":588,\"note\":\"Auto-Created Schedule\"}}","old_value":"null","operation":"SCHEDULE_CREATED","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-03-24 22:44:49"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/469","browser":"Chrome 134.0.0","description":"Welcome email sent to testb b (<EMAIL>)","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":588,\"email\":\"<EMAIL>\",\"subject\":\"Welcome to ResearchHero!\"}","old_value":"null","operation":"EMAIL_SENT","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-03-24 22:44:50"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/469","browser":"Chrome 134.0.0","description":"Registration status updated to Accepted for ID 469","ip_address":"::1","method":"PUT","new_value":"{\"id\":\"469\",\"status\":\"Accepted\",\"reason\":\"Accepted By Super Admin \",\"user_id\":588}","old_value":"{}","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-03-24 22:44:50"}
{"level":"info","message":{"api_url":"/organization/logout","browser":"Chrome 134.0.0","description":"User logged out successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"LOGOUT","table_name":"N/A","user":"<EMAIL>"},"timestamp":"2025-03-24 22:51:23"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 134.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-24 22:51:35"}
{"level":"info","message":{"api_url":"/organization/logout","browser":"Chrome 134.0.0","description":"User logged out successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"LOGOUT","table_name":"N/A","user":"<EMAIL>"},"timestamp":"2025-03-24 22:52:07"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Other 0.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-24 22:52:50"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 134.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-24 22:53:45"}
{"level":"info","message":{"api_url":"/incident_report/createIncidentReportResponse","browser":"Other 0.0.0","description":"Incident report response created:\n          study_id: 2,\n          user_id: 588,\n          question_id: 45,\n          response_text: Texto de respuesta en español.,\n          description: Este es un informe de incidente en español.,\n          incident_severety: Moderate,\n          start_date: 01/10/2025,\n          start_time: 4:54:00 PM,\n          medical_issue: 1,\n          end_date: 2024-12-25,\n          end_time: 15:00","ip_address":"::1","method":"POST","new_value":"{\"study_id\":2,\"user_id\":588,\"question_id\":45,\"response_text\":\"Texto de respuesta en español.\",\"description\":\"Este es un informe de incidente en español.\",\"incident_severety\":\"Moderate\",\"start_date\":\"01/10/2025\",\"start_time\":\"4:54:00 PM\",\"medical_issue\":1,\"end_date\":\"2024-12-25\",\"end_time\":\"15:00\"}","old_value":"null","operation":"SUBMIT","table_name":"Incident Report Response","user":"<EMAIL>"},"timestamp":"2025-03-24 22:54:49"}
{"level":"error","message":"Failed to log to database or Excel: Data too long for column 'description' at row 1","timestamp":"2025-03-24 22:54:50"}
{"level":"info","message":{"api_url":"/incident_report/createIncidentReportResponse","browser":"Other 0.0.0","description":"Incident report response created:\n          study_id: 1,\n          user_id: 587,\n          question_id: 45,\n          response_text: Texto de respuesta en español.,\n          description: Este es un informe de incidente en español.,\n          incident_severety: Moderate,\n          start_date: 01/10/2025,\n          start_time: 4:54:00 PM,\n          medical_issue: 1,\n          end_date: 2024-12-25,\n          end_time: 15:00","ip_address":"::1","method":"POST","new_value":"{\"study_id\":1,\"user_id\":587,\"question_id\":45,\"response_text\":\"Texto de respuesta en español.\",\"description\":\"Este es un informe de incidente en español.\",\"incident_severety\":\"Moderate\",\"start_date\":\"01/10/2025\",\"start_time\":\"4:54:00 PM\",\"medical_issue\":1,\"end_date\":\"2024-12-25\",\"end_time\":\"15:00\"}","old_value":"null","operation":"SUBMIT","table_name":"Incident Report Response","user":"<EMAIL>"},"timestamp":"2025-03-24 22:56:43"}
{"level":"error","message":"Failed to log to database or Excel: Data too long for column 'description' at row 1","timestamp":"2025-03-24 22:56:43"}
{"level":"info","message":{"api_url":"/incident_report/createIncidentReportResponse","browser":"Other 0.0.0","description":"Incident report response created:\n          study_id: 1,\n          user_id: 587,\n          question_id: 45,\n          response_text: Texto de respuesta en español.,\n          description: Este es un informe de incidente en español.,\n          incident_severety: Moderate,\n          start_date: 01/10/2025,\n          start_time: 4:54:00 PM,\n          medical_issue: 1,\n          end_date: 2024-12-25,\n          end_time: 15:00","ip_address":"::1","method":"POST","new_value":"{\"study_id\":1,\"user_id\":587,\"question_id\":45,\"response_text\":\"Texto de respuesta en español.\",\"description\":\"Este es un informe de incidente en español.\",\"incident_severety\":\"Moderate\",\"start_date\":\"01/10/2025\",\"start_time\":\"4:54:00 PM\",\"medical_issue\":1,\"end_date\":\"2024-12-25\",\"end_time\":\"15:00\"}","old_value":"null","operation":"SUBMIT","table_name":"Incident Report Response","user":"<EMAIL>"},"timestamp":"2025-03-24 23:22:45"}
{"level":"error","message":"Failed to log to database or Excel: Data too long for column 'description' at row 1","timestamp":"2025-03-24 23:22:45"}
{"level":"info","message":"{\"method\":\"POST\",\"api_url\":\"/incident_report/update-ticket-history\",\"table_name\":\"Ticket History\",\"operation\":\"UPDATE\",\"description\":\"Ticket #AE-2P3T02J updated by user_id: 403\",\"old_value\":null,\"new_value\":\"{\\\"user_id\\\":403,\\\"actionType\\\":\\\"Comment\\\",\\\"status\\\":\\\"Under Process\\\",\\\"history_text\\\":\\\"asd\\\",\\\"ticket_id\\\":\\\"AE-2P3T02J\\\"}\",\"browser\":\"Chrome 134.0.0\",\"ip_address\":\"::1\",\"user\":\"<EMAIL>\"}","timestamp":"2025-03-24 23:24:39"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/545","browser":"Chrome 134.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-24 23:34:12"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-24 23:34:12"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/545","browser":"Chrome 134.0.0","description":"xczzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzz","ip_address":"::1","method":"PUT","new_value":"{\"status\":\"Screen Failed\",\"address\":\"mardanz\",\"reason\":\"xczzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzz\"}","old_value":"{\"status\":\"Screened\",\"address\":\"mardan\"}","operation":"UPDATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-24 23:34:14"}
