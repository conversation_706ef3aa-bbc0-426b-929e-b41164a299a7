const fs = require("fs");
const csv = require("csv-parser");
const ExcelJS = require("exceljs");
const moment = require("moment");

// Array to hold CSV rows
const results = [];

// Read the CSV file (adjust filename/path as needed)
fs.createReadStream("ecrf_submissions.csv")
  .pipe(csv())
  .on("data", (data) => results.push(data))
  .on("end", () => {

    // Group data by composite key: ticket_id and ticket_start_date
    const groupedData = {};

    results.forEach((row) => {
      // Filter out rows whose ecrf_id starts with B or b
      //   if (row.ecrf_id && row.ecrf_id.trim()[0].toLowerCase() === "b") {
      //     return; // skip this row
      //   }

      // Use original ticket_start_date string as part of the key
      const groupKey = `${row.ticket_id}_${row.ticket_start_date}`;

      // Parse date/time using moment.
      // Format date as YYYY/MM/DD and time in 24-hour format HH:mm:ss.
      const m = moment(new Date(row.ticket_start_date));
      const formattedDate = m.format("YYYY/MM/DD");
      const formattedTime = m.format("HH:mm ");

      // Initialize the group if it doesn't exist
      if (!groupedData[groupKey]) {
        groupedData[groupKey] = {
          "Ticket ID": row.ticket_id,
          ecrf_id: row.ecrf_id,
          "Site Name": `${row.organization_name || ""} ${
            row.organization_address || ""
          }`.trim(),
          "Adverse Question": row.incident_question,
          "Adverse Question Answer": row.incident_response,
          "Describe Adverse Event (description)": row.incident_description,
          "Incident Severity": row.incident_severety,
          "Ticket Start Date": formattedDate,
          "Ticket Start Time": formattedTime,
          // Use a Set to avoid duplicate history entries
          "Ticket Comment": new Set(),
          // Use a Set for unique AE Form Submission entries
          "AE Form Submission (ecrf)": new Set(),
          // For AESI, store entries in an object keyed by question text
          "AESI Questions & Response Option": {},
          "Ticket Status": row.ticket_status,
          // Capture the filled_by value from the CSV
          filled_by: row.filled_by || "",
          // Capture the aesi_filled_by value from the CSV
          aesi_filled_by: row.aesi_filled_by || "",
        };
      }

      const group = groupedData[groupKey];

      // Process Ticket History:
      // Format as: (User Email (Date&Time:<Ticket_History_Created_At>) <Ticket_History_Text>
      if (
        row.Ticket_History_Text ||
        row.Ticket_History_Created_At ||
        row.Ticket_History_User_Email
      ) {
        const historyEntry = `(${
          row.Ticket_History_User_Email || ""
        } (Date&Time:${row.Ticket_History_Created_At || ""}) ${
          row.Ticket_History_Text || ""
        }`;
        group["Ticket Comment"].add(historyEntry);
      }

      // Process AE Form Submission (ecrf)
      if (row.ecrf_question) {
        const ecrfQuestion = row.ecrf_question.trim();
        const ecrfAnswer = row.ecrf_answer ? row.ecrf_answer : "";

        // Instead of storing as a formatted string, store as a map of questions to answers
        if (!group["AE Form Questions"]) {
          group["AE Form Questions"] = new Map();
        }

        // Store each question and its answer separately
        group["AE Form Questions"].set(ecrfQuestion, ecrfAnswer);
      }

      // Process AESI entry if aesi_question exists
      if (row.aesi_question) {
        const questionKey = row.aesi_question.trim();

        // Create a structured AESI entry
        let aesiData = {
          question: questionKey,
          answer: "",
          description: "",
          createdAt: ""
        };

        // Handle the answer, including NULL values
        if (row.aesi_option) {
          // Preserve NULL values as is
          if (row.aesi_option.trim().toUpperCase() === "NULL") {
            aesiData.answer = "NULL";
          } else if (row.aesi_option.trim() !== "") {
            aesiData.answer = row.aesi_option.trim();
          }
        }

        if (row.aesi_description && row.aesi_description.trim() !== "") {
          aesiData.description = row.aesi_description.trim();
        }

        if (row.aesi_created_at && row.aesi_created_at.trim() !== "") {
          aesiData.createdAt = row.aesi_created_at.trim();
        }

        // Save the structured AESI entry if not already present
        if (!group["AESI Questions & Response Option"][questionKey]) {
          group["AESI Questions & Response Option"][questionKey] = aesiData;
        }
      }
    });

    // Collect all unique eCRF questions across all entries
    const allEcrfQuestions = new Set();
    Object.values(groupedData).forEach(item => {
      if (item["AE Form Questions"]) {
        for (const question of item["AE Form Questions"].keys()) {
          // Skip questions that are just "NULL"
          if (question.trim().toUpperCase() !== "NULL") {
            allEcrfQuestions.add(question);
          }
        }
      }
    });

    // Convert to array for consistent ordering
    const ecrfQuestionsList = Array.from(allEcrfQuestions);

    // Collect all unique AESI questions across all entries
    const allAesiQuestions = new Set();
    Object.values(groupedData).forEach(item => {
      if (item["AESI Questions & Response Option"]) {
        for (const question in item["AESI Questions & Response Option"]) {
          // Skip questions that are just "NULL"
          if (question.trim().toUpperCase() !== "NULL") {
            allAesiQuestions.add(question);
          }
        }
      }
    });

    // Convert to array for consistent ordering
    const aesiQuestionsList = Array.from(allAesiQuestions);

    // Prepare the final output array
    let output = Object.values(groupedData).map((item) => {
      // Build Ticket Comment header using Ticket Start Date and Time
      const header = `Ticket Submission Date & Time :${item["Ticket Start Date"]} ${item["Ticket Start Time"]}`;
      // Join history entries (each already formatted) with a newline
      const historyEntries = Array.from(item["Ticket Comment"]).join("\n");
      const ticketComment = `${header}\n${historyEntries}`;

      // Create the base output object with common fields
      const outputRow = {
        "Ticket ID": item["Ticket ID"],
        ecrf_id: item["ecrf_id"],
        "Site Name": item["Site Name"],
        "Adverse Question": item["Adverse Question"],
        "Adverse Question Answer": item["Adverse Question Answer"],
        "Describe Adverse Event (description)": item["Describe Adverse Event (description)"],
        "Incident Severity": item["Incident Severity"],
        "Ticket Start Date": item["Ticket Start Date"],
        "Ticket Start Time": item["Ticket Start Time"],
        "Ticket Comment": ticketComment,
        "AE Form Filled BY": item["filled_by"] || "",
        "AESI Filled BY": item["aesi_filled_by"] || "",
        "Ticket Status": item["Ticket Status"],
      };

      // Add each eCRF question as a separate column
      if (item["AE Form Questions"]) {
        ecrfQuestionsList.forEach(question => {
          const answer = item["AE Form Questions"].get(question) || "";
          outputRow[question] = answer;
        });
      }

      // Add each AESI question as a separate column
      if (item["AESI Questions & Response Option"]) {
        aesiQuestionsList.forEach(question => {
          const aesi = item["AESI Questions & Response Option"][question];
          if (aesi) {
            // For NULL values, just show "NULL"
            if (aesi.answer && aesi.answer.trim().toUpperCase() === "") {
              outputRow[question] = "";
            } else {
              // For other values, show the answer without the creation date in the column header
              let aesiAnswer = aesi.answer || "";

              // Add description if available
              if (aesi.description && aesi.description.trim() !== "") {
                aesiAnswer = aesiAnswer ? `${aesiAnswer}\n${aesi.description}` : aesi.description;
              }

              // Add creation date to the answer if available
              if (aesi.createdAt && aesi.createdAt.trim() !== "") {
                aesiAnswer = aesiAnswer ? `${aesiAnswer}` : ``;
              }

              outputRow[question] = aesiAnswer;
            }
          } else {
            outputRow[question] = "";
          }
        });
      }

      return outputRow;
    });

    // Sort the output by Ticket Start Date and then by Ticket Start Time
    output.sort((a, b) => {
      const dateA = moment(a["Ticket Start Date"], "YYYY/MM/DD");
      const dateB = moment(b["Ticket Start Date"], "YYYY/MM/DD");
      if (dateA.isBefore(dateB)) return -1;
      if (dateA.isAfter(dateB)) return 1;
      // If dates are equal, compare times
      const timeA = moment(a["Ticket Start Time"], "HH:mm:ss");
      const timeB = moment(b["Ticket Start Time"], "HH:mm:ss");
      if (timeA.isBefore(timeB)) return -1;
      if (timeA.isAfter(timeB)) return 1;
      return 0;
    });

    // Create a new Excel workbook and worksheet using ExcelJS
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet("Transformed Data");

    // Define base columns with headers and set widths
    const baseColumns = [
      { header: "Ticket ID", key: "Ticket ID", width: 15 },
      { header: "ecrf_id", key: "ecrf_id", width: 15 },
      { header: "Site Name", key: "Site Name", width: 30 },
      { header: "Adverse Question", key: "Adverse Question", width: 30 },
      { header: "Adverse Question Answer", key: "Adverse Question Answer", width: 30 },
      { header: "Describe Adverse Event (description)", key: "Describe Adverse Event (description)", width: 35 },
      { header: "Incident Severity", key: "Incident Severity", width: 15 },
      { header: "Ticket Start Date", key: "Ticket Start Date", width: 15 },
      { header: "Ticket Start Time", key: "Ticket Start Time", width: 15 },
      { header: "Ticket Comment", key: "Ticket Comment", width: 50 },
      { header: "AE Form Filled BY", key: "AE Form Filled BY", width: 20 },

    ];

    // Add dynamic columns for each eCRF question
    const ecrfColumns = ecrfQuestionsList.map(question => ({
      header: question,
      key: question,
      width: 30
    }));

    // Add dynamic columns for each AESI question
    const aesiColumns = aesiQuestionsList.map(question => ({
      header: question,
      key: question,
      width: 35
    }));

    // Add the Ticket Status column at the end
    const finalColumns = [
      ...baseColumns,
      ...ecrfColumns,
      ...aesiColumns,
      { header: "Ticket Status", key: "Ticket Status", width: 15 }
    ];

    // Set the columns
    worksheet.columns = finalColumns;

    // Style the header row (bold, centered, with borders)
    worksheet.getRow(1).eachCell((cell) => {
      cell.font = { bold: true };
      cell.alignment = {
        vertical: "middle",
        horizontal: "center",
        wrapText: true,
      };
      cell.border = {
        top: { style: "thin" },
        left: { style: "thin" },
        bottom: { style: "thin" },
        right: { style: "thin" },
      };
    });

    // Add each transformed row to the worksheet
    output.forEach((item) => {
      worksheet.addRow(item);
    });

    // Adjust row heights and ensure cells wrap text
    worksheet.eachRow({ includeEmpty: false }, function (row) {
      row.eachCell({ includeEmpty: false }, function (cell) {
        cell.alignment = {
          wrapText: true,
          vertical: "middle",
          horizontal: "left",
        };
      });
    });

    // Write the workbook to an Excel file with a timestamp to avoid conflicts
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const outputFilename = `transformed_output_${timestamp}.xlsx`;

    workbook.xlsx
      .writeFile(outputFilename)
      .then(() => {
        console.log(`Transformation complete! File saved as '${outputFilename}'.`);
      })
      .catch((err) => {
        console.error("Error writing Excel file:", err);
      });
  });
