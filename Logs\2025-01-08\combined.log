{"level":"error","message":"Failed to decode token or email is missing","timestamp":"2025-01-08 17:20:38"}
{"level":"error","message":"Failed to decode token or email is missing","timestamp":"2025-01-08 17:20:38"}
{"level":"error","message":"Failed to decode token or email is missing","timestamp":"2025-01-08 17:20:38"}
{"level":"error","message":"Failed to decode token or email is missing","timestamp":"2025-01-08 17:20:38"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 131.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-01-08 17:21:45"}
{"level":"info","message":{"api_url":"/organization/logout","browser":"Chrome 131.0.0","description":"User logged out successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"LOGOUT","table_name":"N/A","user":"<EMAIL>"},"timestamp":"2025-01-08 17:25:22"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 131.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-01-08 17:25:26"}
{"level":"error","message":"Failed to decode token or email is missing","timestamp":"2025-01-08 17:59:57"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 131.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-01-08 18:00:00"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/377","browser":"Chrome 131.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-01-08 18:00:31"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-08 18:00:32"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/377","browser":"Chrome 131.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-01-08 18:01:50"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-08 18:01:50"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/377","browser":"Chrome 131.0.0","description":"Auto Create Medicine","ip_address":"::1","method":"PUT","new_value":"{\"medication_name\":\"Sunobinop Or Placebo\",\"dosage\":\"0.5mg or 1.0mg or 2.0mg\",\"dosage_times\":[\"09:00 PM\"],\"frequencyType\":\"QD\",\"frequencyTime\":\"N/A\",\"frequencyCondition\":\"At Bedtime\",\"dosageType\":\"Tablet\",\"allot_medicine\":\"1\",\"route\":\"Oral\",\"note\":\"Auto-created medicine\",\"user_id\":\"408\",\"investigator_id\":\"403\",\"tracker_time\":\"2025-01-08T13:01:51.702Z\"}","old_value":"null","operation":"CREATE","table_name":"Auto Create Medicine","user":"<EMAIL>"},"timestamp":"2025-01-08 18:01:55"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/377","browser":"Chrome 131.0.0","description":"No Reason Provided","ip_address":"::1","method":"PUT","new_value":"{\"first_name\":\"NYC\",\"middle_name\":\"\",\"last_name\":\"Test\",\"status\":\"Randomized\",\"gender\":\"male\",\"address\":\"NYC\",\"contact_number\":\"12222222222\",\"stipend\":\"0\",\"study_enrolled_ids\":\"1\",\"notification\":\"some notification\",\"note\":\"Note for admin\",\"ecrf_id\":\"A102-000\"}","old_value":"{\"organization_id\":377,\"first_name\":\"NYC\",\"middle_name\":\"\",\"last_name\":\"Test\",\"status\":\"Screened\",\"is_randomized\":0,\"gender\":\"male\",\"address\":\"NYC\",\"timezone\":\"UTC\",\"contact_number\":\"12222222222\",\"date_of_birth\":\"02/02/1999\",\"stipend\":\"0\",\"image\":\"patients/1734764508794.jpg\",\"study_enrolled_id\":\"1\",\"date_enrolled\":\"12/12/2024\",\"notification\":\"some notification\",\"user_id\":408,\"organization_detail_id\":2,\"role_id\":10,\"ecrf_id\":\"A102-000\",\"study_enrolled\":[{\"id\":1,\"name\":\"\"}]}","operation":"UPDATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-01-08 18:01:55"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1429","browser":"Chrome 131.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-01-08 18:09:58"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-08 18:09:58"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1429","browser":"Chrome 131.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-01-08 18:10:44"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-08 18:10:44"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1429","browser":"Chrome 131.0.0","ip_address":"::ffff:127.0.0.1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-01-08 18:11:17"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-08 18:11:17"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1429","browser":"Chrome 131.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-01-08 18:11:59"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-08 18:11:59"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1429","browser":"Chrome 131.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-01-08 18:25:17"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-08 18:25:17"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1429","browser":"Chrome 131.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-01-08 18:26:21"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-08 18:26:21"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1429","browser":"Chrome 131.0.0","description":"Auto Create Medicine","ip_address":"::1","method":"PUT","new_value":"{\"medication_name\":\"Sunobinop Or Placebo\",\"dosage\":\"0.5mg or 1.0mg or 2.0mg\",\"dosage_times\":[\"09:00 PM\"],\"frequencyType\":\"QD\",\"frequencyTime\":\"N/A\",\"frequencyCondition\":\"At Bedtime\",\"dosageType\":\"Tablet\",\"allot_medicine\":\"1\",\"route\":\"Oral\",\"note\":\"Auto-created medicine\",\"user_id\":413,\"investigator_id\":0,\"tracker_time\":\"2025-01-08T13:26:22.912Z\"}","old_value":"null","operation":"CREATE","table_name":"Auto Create Medicine Due to Subject Screening Complete","user":"<EMAIL>"},"timestamp":"2025-01-08 18:26:22"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1429","browser":"Chrome 131.0.0","description":"Schedule completed with ID: 1429","ip_address":"::1","method":"PUT","new_value":"{\"schedule_date\":\"2025-01-08\",\"schedule_time\":\"09:00\",\"status\":\"Completed\",\"note\":\"Auto-created schedule\",\"user_id\":413}","old_value":"{\"schedule_date\":\"2025-01-07T19:00:00.000Z\",\"schedule_time\":\"09:00\",\"status\":\"Scheduled\",\"note\":\"Auto-created schedule\",\"user_id\":413}","operation":"UPDATE","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-01-08 18:26:29"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1436","browser":"Chrome 131.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-01-08 18:29:29"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-08 18:29:29"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1436","browser":"Chrome 131.0.0","description":"Schedule pending with ID: 1436","ip_address":"::1","method":"PUT","new_value":"{\"schedule_date\":\"2024-12-29\",\"schedule_time\":\"09:00\",\"status\":\"Pending\",\"note\":\"Auto-scheduled for Baseline Day 0\",\"user_id\":428}","old_value":"{\"schedule_date\":\"2024-12-27T19:00:00.000Z\",\"schedule_time\":\"09:00\",\"status\":\"Pending\",\"note\":\"Auto-scheduled for Baseline Day 0\",\"user_id\":428}","operation":"UPDATE","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-01-08 18:29:32"}
{"level":"info","message":{"api_url":"/medicine/createMedicine","browser":"Chrome 131.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"medication","user":"<EMAIL>"},"timestamp":"2025-01-08 18:35:18"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-08 18:35:18"}
{"level":"info","message":{"api_url":"/medicine/createMedicine","browser":"Chrome 131.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"medication","user":"<EMAIL>"},"timestamp":"2025-01-08 18:39:34"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-08 18:39:35"}
{"level":"info","message":{"api_url":"/medicine/createMedicine","browser":"Chrome 131.0.0","description":"Manually medication created","ip_address":"::1","method":"POST","new_value":"{\"medication_name\":\"placebo\",\"dosage\":\"1.0\",\"dosage_times\":[\"18:39\"],\"frequencyType\":\"QD\",\"frequencyTime\":\"Morning\",\"frequencyCondition\":\"Before Meal\",\"dosageType\":\"Suppository\",\"allot_medicine\":\"1\",\"route\":\"Sublingual and Buccal\",\"note\":\"note\",\"investigator_id\":403,\"user_id\":\"427\",\"tracker_time\":\"2025-01-08T13:39:34.065Z\"}","old_value":"null","operation":"CREATE","table_name":"medication","user":"<EMAIL>"},"timestamp":"2025-01-08 18:39:36"}
{"level":"info","message":{"api_url":"/medicine/createMedicine","browser":"Chrome 131.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"medication","user":"<EMAIL>"},"timestamp":"2025-01-08 18:41:03"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-08 18:41:03"}
{"level":"info","message":{"api_url":"/medicine/createMedicine","browser":"Chrome 131.0.0","description":"Manually medication created","ip_address":"::1","method":"POST","new_value":"{\"medication_name\":\"xc\",\"dosage\":\"34\",\"dosage_times\":[\"18:40\"],\"frequencyType\":\"QD\",\"frequencyTime\":\"Morning\",\"frequencyCondition\":\"Before Meal\",\"dosageType\":\"Granules\",\"allot_medicine\":\"1\",\"route\":\"Sublingual and Buccal\",\"note\":\"note\",\"investigator_id\":403,\"user_id\":\"427\",\"tracker_time\":\"2025-01-08T13:41:02.611Z\"}","old_value":"null","operation":"CREATE","table_name":"Manually medication Created","user":"<EMAIL>"},"timestamp":"2025-01-08 18:41:04"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/326","browser":"Chrome 131.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-01-08 18:42:09"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-08 18:42:09"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/326","browser":"Chrome 131.0.0","description":"Welcome email sent to some test tlfb (<EMAIL>)","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":429,\"email\":\"<EMAIL>\",\"subject\":\"Welcome to ResearchHero!\"}","old_value":"null","operation":"EMAIL_SENT","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-01-08 18:42:11"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/326","browser":"Chrome 131.0.0","description":"Registration status updated to Accepted for ID 326","ip_address":"::1","method":"PUT","new_value":"{\"id\":\"326\",\"status\":\"Accepted\",\"reason\":\"Accepted By Super Admin \",\"user_id\":429}","old_value":"[{\"account_status_id\":326,\"user_id\":429,\"account_status\":\"Pending\",\"reason\":\"Initial registration\",\"first_time\":\"1\",\"updated_at\":\"2024-12-22T00:58:52.000Z\"}]","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-01-08 18:42:12"}
{"level":"info","message":{"api_url":"/schedule/createSchedule","browser":"Chrome 131.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-01-08 18:42:12"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-08 18:42:13"}
{"level":"info","message":{"api_url":"/schedule/createSchedule","browser":"Chrome 131.0.0","description":"Schedule created successfully for user 429","ip_address":"::1","method":"POST","new_value":"{}","old_value":"null","operation":"CREATE","table_name":"Schedule","user":"<EMAIL>"},"timestamp":"2025-01-08 18:42:19"}
{"level":"info","message":{"api_url":"/app_survey/submitscalequestionresponse","browser":"Chrome 131.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"submit survey response","user":"<EMAIL>"},"timestamp":"2025-01-08 22:33:04"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-08 22:33:04"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 131.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-01-09 15:08:57"}
