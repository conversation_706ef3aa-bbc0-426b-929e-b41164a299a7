{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 134.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-04-10 12:15:05"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 134.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-04-10 12:38:58"}
{"level":"info","message":{"api_url":"/organization/logout","browser":"Chrome 134.0.0","description":"User logged out successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"LOGOUT","table_name":"N/A","user":"<EMAIL>"},"timestamp":"2025-04-10 12:39:29"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 134.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-04-10 12:39:39"}
{"level":"info","message":{"api_url":"/organization/logout","browser":"Chrome 134.0.0","description":"User logged out successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"LOGOUT","table_name":"N/A","user":"<EMAIL>"},"timestamp":"2025-04-10 12:41:04"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 134.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-04-10 12:41:14"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/531","browser":"Chrome 134.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-04-10 12:41:33"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-04-10 12:41:33"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/531","browser":"Chrome 134.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-04-10 12:43:03"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-04-10 12:43:03"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 134.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-04-10 16:09:25"}
{"level":"info","message":{"api_url":"/organization/deleteOrganization/581","browser":"Chrome 134.0.0","ip_address":"::1","method":"DELETE","new_value":"null","old_value":"null","operation":"delete","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-04-10 16:09:45"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-04-10 16:09:45"}
{"level":"info","message":{"api_url":"/organization/deleteOrganization/581","browser":"Chrome 134.0.0","ip_address":"::1","method":"DELETE","new_value":"null","old_value":"null","operation":"delete","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-04-10 16:09:56"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-04-10 16:09:56"}
{"level":"info","message":{"api_url":"/organization/deleteOrganization/581","browser":"Chrome 134.0.0","ip_address":"::1","method":"DELETE","new_value":"null","old_value":"null","operation":"delete","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-04-10 16:10:11"}
{"level":"info","message":{"api_url":"/organization/deleteOrganization/581","browser":"Chrome 134.0.0","ip_address":"::1","method":"DELETE","new_value":"null","old_value":"null","operation":"delete","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-04-10 16:10:11"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-04-10 16:10:12"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-04-10 16:10:12"}
{"level":"info","message":{"api_url":"/organization/deleteOrganization/581","browser":"Chrome 134.0.0","ip_address":"::1","method":"DELETE","new_value":"null","old_value":"null","operation":"delete","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-04-10 16:10:55"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-04-10 16:10:55"}
{"level":"info","message":{"api_url":"/organization/deleteOrganization/581","browser":"Chrome 134.0.0","ip_address":"::1","method":"DELETE","new_value":"null","old_value":"null","operation":"delete","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-04-10 16:12:44"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-04-10 16:12:44"}
{"level":"info","message":{"api_url":"/organization/deleteOrganization/576","browser":"Chrome 134.0.0","ip_address":"::1","method":"DELETE","new_value":"null","old_value":"null","operation":"delete","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-04-10 16:13:00"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-04-10 16:13:00"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 134.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-04-10 18:49:58"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 134.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-04-10 19:07:08"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 134.0.0","description":"User signed in successfully","ip_address":"::ffff:127.0.0.1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-04-10 19:17:32"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Other 0.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-04-10 20:48:03"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 134.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-04-10 23:07:30"}
