{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 133.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-11 00:39:01"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-11 00:39:01"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 133.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-11 00:39:11"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-11 00:39:11"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 133.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-11 00:39:28"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-11 00:39:28"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 133.0.0","description":"New organization created","ip_address":"::1","method":"POST","new_value":"{\"first_name\":\"haha\",\"last_name\":\"haha\",\"middle_name\":\"\",\"email\":\"<EMAIL>\",\"status\":\"Screened\",\"gender\":\"male\",\"address\":\"hahah\",\"contact_number\":\"10000000000\",\"date_of_birth\":\"01/01/1999\",\"stipend\":\"3\",\"study_enrolled_ids\":\"1\",\"notification\":\"some notification\",\"note\":\"Note for admin\",\"role_id\":\"10\",\"organization_detail_id\":\"1\",\"ecrf_id\":\"A454-5E4\",\"timezone\":\"Pacific/Midway\"}","old_value":"null","operation":"CREATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-11 00:39:30"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 133.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-11 00:40:53"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-11 00:40:53"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 133.0.0","description":"New organization created","ip_address":"::1","method":"POST","new_value":"{\"first_name\":\"cc\",\"last_name\":\"cc\",\"middle_name\":\"\",\"email\":\"<EMAIL>\",\"status\":\"Screened\",\"gender\":\"male\",\"address\":\"ere\",\"contact_number\":\"10000000000\",\"date_of_birth\":\"01/01/1999\",\"stipend\":\"43\",\"study_enrolled_ids\":\"1\",\"notification\":\"some notification\",\"note\":\"Note for admin\",\"role_id\":\"10\",\"organization_detail_id\":\"1\",\"ecrf_id\":\"A345-453\",\"timezone\":\"Pacific/Honolulu\"}","old_value":"null","operation":"CREATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-11 00:40:55"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 133.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-11 00:42:37"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-11 00:42:37"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 133.0.0","description":"New organization created","ip_address":"::1","method":"POST","new_value":"{\"first_name\":\"khan\",\"last_name\":\"khan\",\"middle_name\":\"\",\"email\":\"<EMAIL>\",\"status\":\"Screened\",\"gender\":\"male\",\"address\":\"re\",\"contact_number\":\"10000000000\",\"date_of_birth\":\"01/01/1999\",\"stipend\":\"3\",\"study_enrolled_ids\":\"1\",\"notification\":\"some notification\",\"note\":\"Note for admin\",\"role_id\":\"10\",\"organization_detail_id\":\"1\",\"ecrf_id\":\"A453-543\",\"timezone\":\"Pacific/Midway\"}","old_value":"null","operation":"CREATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-11 00:42:39"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 133.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-11 00:44:01"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-11 00:44:01"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 133.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-11 00:44:08"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-11 00:44:08"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 133.0.0","description":"New organization created","ip_address":"::1","method":"POST","new_value":"{\"first_name\":\"fd\",\"last_name\":\"df\",\"middle_name\":\"\",\"email\":\"<EMAIL>\",\"status\":\"Screened\",\"gender\":\"male\",\"address\":\"fd\",\"contact_number\":\"10000000000\",\"date_of_birth\":\"01/01/1999\",\"stipend\":\"4\",\"study_enrolled_ids\":\"1\",\"notification\":\"some notification\",\"note\":\"Note for admin\",\"role_id\":\"10\",\"organization_detail_id\":\"1\",\"ecrf_id\":\"A343-566\",\"timezone\":\"Pacific/Midway\"}","old_value":"null","operation":"CREATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-11 00:44:10"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 133.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-11 00:47:50"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-11 00:47:50"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 133.0.0","description":"New organization created","ip_address":"::1","method":"POST","new_value":"{\"first_name\":\"vcxc\",\"last_name\":\"v\",\"middle_name\":\"\",\"email\":\"<EMAIL>\",\"status\":\"Screened\",\"gender\":\"male\",\"address\":\"ksldfk\",\"contact_number\":\"10000000000\",\"date_of_birth\":\"01/01/1999\",\"stipend\":\"2\",\"study_enrolled_ids\":\"1\",\"notification\":\"some notification\",\"note\":\"Note for admin\",\"role_id\":\"10\",\"organization_detail_id\":\"1\",\"ecrf_id\":\"A010-1SO\",\"timezone\":\"Pacific/Midway\"}","old_value":"null","operation":"CREATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-11 00:47:52"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 133.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-11 00:49:57"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-11 00:49:58"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 133.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-11 00:50:04"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-11 00:50:04"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 133.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-11 00:50:26"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-11 00:50:26"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 133.0.0","description":"New organization created","ip_address":"::1","method":"POST","new_value":"{\"first_name\":\"vcxc\",\"last_name\":\"v\",\"middle_name\":\"\",\"email\":\"<EMAIL>\",\"status\":\"Screened\",\"gender\":\"male\",\"address\":\"ksldfk\",\"contact_number\":\"10000000000\",\"date_of_birth\":\"01/01/1999\",\"stipend\":\"2\",\"study_enrolled_ids\":\"1\",\"notification\":\"some notification\",\"note\":\"Note for admin\",\"role_id\":\"10\",\"organization_detail_id\":\"1\",\"ecrf_id\":\"A010-787\",\"timezone\":\"Pacific/Midway\"}","old_value":"null","operation":"CREATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-11 00:50:28"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 133.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-11 00:57:07"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-11 00:57:07"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 133.0.0","description":"New organization created","ip_address":"::1","method":"POST","new_value":"{\"first_name\":\"vcxc\",\"last_name\":\"v\",\"middle_name\":\"\",\"email\":\"<EMAIL>\",\"status\":\"Screened\",\"gender\":\"male\",\"address\":\"ksldfk\",\"contact_number\":\"10000000000\",\"date_of_birth\":\"01/01/1999\",\"stipend\":\"2\",\"study_enrolled_ids\":\"1\",\"notification\":\"some notification\",\"note\":\"Note for admin\",\"role_id\":\"10\",\"organization_detail_id\":\"1\",\"ecrf_id\":\"A010-888\",\"timezone\":\"Pacific/Midway\"}","old_value":"null","operation":"CREATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-11 00:57:09"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 134.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-11 20:45:50"}
{"level":"info","message":{"api_url":"/organization/createPersonnel","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-11 21:19:45"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-11 21:19:45"}
{"level":"info","message":{"api_url":"/organization/createPersonnel","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-11 21:19:59"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-11 21:19:59"}
{"level":"info","message":{"api_url":"/organization/createPersonnel","browser":"Chrome 134.0.0","description":"Personnel created with email: <EMAIL>","ip_address":"::1","method":"POST","new_value":"{\"userId\":494}","old_value":"null","operation":"CREATE","table_name":"personnel","user":"<EMAIL>"},"timestamp":"2025-03-11 21:20:05"}
{"level":"info","message":{"api_url":"/organization/logout","browser":"Chrome 134.0.0","description":"User logged out successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"LOGOUT","table_name":"N/A","user":"<EMAIL>"},"timestamp":"2025-03-11 21:20:32"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 134.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-11 21:20:36"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/387","browser":"Chrome 134.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-03-11 21:21:29"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-11 21:21:29"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/387","browser":"Chrome 134.0.0","description":"Welcome email sent to Testing  Phase (<EMAIL>)","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":494,\"email\":\"<EMAIL>\",\"subject\":\"Welcome to ResearchHero!\"}","old_value":"null","operation":"EMAIL_SENT","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-03-11 21:21:33"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/387","browser":"Chrome 134.0.0","description":"Registration status updated to Accepted for ID 387","ip_address":"::1","method":"PUT","new_value":"{\"id\":\"387\",\"status\":\"Accepted\",\"reason\":\"Accepted! fcxcvxcvxvxcvxcv\",\"user_id\":494}","old_value":"{}","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-03-11 21:21:33"}
{"level":"info","message":{"api_url":"/organization/logout","browser":"Chrome 134.0.0","description":"User logged out successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"LOGOUT","table_name":"N/A","user":"<EMAIL>"},"timestamp":"2025-03-11 22:03:20"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 134.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-11 22:03:34"}
{"level":"info","message":{"api_url":"/organization/logout","browser":"Chrome 134.0.0","description":"User logged out successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"LOGOUT","table_name":"N/A","user":"<EMAIL>"},"timestamp":"2025-03-11 22:04:09"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 134.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-11 22:22:18"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 134.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-11 22:22:32"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-11 22:23:36"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-11 22:23:36"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-11 22:25:39"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-11 22:25:40"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","ip_address":"::ffff:127.0.0.1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-11 22:30:15"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-11 22:30:15"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-11 22:32:04"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-11 22:32:04"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","ip_address":"::ffff:127.0.0.1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-11 22:34:45"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-11 22:34:47"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-11 22:35:49"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-11 22:35:49"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-11 22:36:45"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-11 22:36:45"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-11 22:38:50"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-11 22:38:51"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","ip_address":"::ffff:127.0.0.1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-11 22:40:30"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-11 22:40:30"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-11 22:42:25"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-11 22:42:25"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-11 22:43:27"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-11 22:43:28"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-11 22:44:23"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-11 22:44:23"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-11 22:45:26"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-11 22:45:26"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-11 22:45:57"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-11 22:45:57"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-11 22:46:02"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-11 22:46:02"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-11 22:46:12"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-11 22:46:13"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-11 22:46:51"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-11 22:46:51"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-11 22:47:37"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-11 22:47:38"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-11 22:53:17"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-11 22:53:18"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","description":"New organization created","ip_address":"::1","method":"POST","new_value":"{\"first_name\":\"hali\",\"last_name\":\"hali\",\"middle_name\":\"\",\"email\":\"<EMAIL>\",\"status\":\"Screened\",\"gender\":\"male\",\"address\":\"hali\",\"contact_number\":\"10000000000\",\"date_of_birth\":\"01/01/1999\",\"stipend\":\"5\",\"study_enrolled_ids\":\"1\",\"notification\":\"some notification\",\"note\":\"Note for admin\",\"role_id\":\"10\",\"organization_detail_id\":\"1\",\"ecrf_id\":\"A777-70J\",\"timezone\":\"Asia/Karachi\"}","old_value":"null","operation":"CREATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-11 22:53:45"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-11 22:59:30"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-11 22:59:31"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","description":"New organization created","ip_address":"::1","method":"POST","new_value":"{\"first_name\":\"study \",\"last_name\":\"b\",\"middle_name\":\"\",\"email\":\"<EMAIL>\",\"status\":\"Screened\",\"gender\":\"male\",\"address\":\"study b\",\"contact_number\":\"10000000000\",\"date_of_birth\":\"01/01/1999\",\"stipend\":\"12\",\"study_enrolled_ids\":\"1\",\"notification\":\"some notification\",\"note\":\"Note for admin\",\"role_id\":\"10\",\"organization_detail_id\":\"10\",\"ecrf_id\":\"A188-888\",\"timezone\":\"Pacific/Honolulu\"}","old_value":"null","operation":"CREATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-11 22:59:34"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-12 01:06:47"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-12 01:06:47"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","description":"New organization created","ip_address":"::1","method":"POST","new_value":"{\"first_name\":\"anas\",\"last_name\":\"KHAN\",\"middle_name\":\"\",\"email\":\"<EMAIL>\",\"status\":\"Screened\",\"gender\":\"male\",\"address\":\"mardan\",\"contact_number\":\"10000000000\",\"date_of_birth\":\"01/01/1999\",\"stipend\":\"4\",\"study_enrolled_ids\":\"1\",\"notification\":\"some notification\",\"note\":\"Note for admin\",\"role_id\":\"10\",\"organization_detail_id\":\"1\",\"ecrf_id\":\"A100-198\",\"timezone\":\"Pacific/Honolulu\"}","old_value":"null","operation":"CREATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-12 01:07:09"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-12 01:08:41"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-12 01:08:41"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-12 01:08:47"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-12 01:08:47"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","description":"New organization created","ip_address":"::1","method":"POST","new_value":"{\"first_name\":\"anas\",\"last_name\":\"khan\",\"middle_name\":\"\",\"email\":\"<EMAIL>\",\"status\":\"Screened\",\"gender\":\"male\",\"address\":\"mardan\",\"contact_number\":\"10000000000\",\"date_of_birth\":\"01/01/1999\",\"stipend\":\"3\",\"study_enrolled_ids\":\"1\",\"notification\":\"some notification\",\"note\":\"Note for admin\",\"role_id\":\"10\",\"organization_detail_id\":\"1\",\"ecrf_id\":\"A311-434\",\"timezone\":\"Pacific/Midway\"}","old_value":"null","operation":"CREATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-12 01:09:09"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-12 01:14:28"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-12 01:14:28"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","description":"New organization created","ip_address":"::1","method":"POST","new_value":"{\"first_name\":\"anas\",\"last_name\":\"khan\",\"middle_name\":\"\",\"email\":\"<EMAIL>\",\"status\":\"Screened\",\"gender\":\"male\",\"address\":\"mardan\",\"contact_number\":\"10000000000\",\"date_of_birth\":\"01/01/1999\",\"stipend\":\"4\",\"study_enrolled_ids\":\"1\",\"notification\":\"some notification\",\"note\":\"Note for admin\",\"role_id\":\"10\",\"organization_detail_id\":\"1\",\"ecrf_id\":\"A565-645\",\"timezone\":\"Pacific/Midway\"}","old_value":"null","operation":"CREATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-12 01:14:51"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-12 01:23:55"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-12 01:23:55"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-12 01:23:59"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-12 01:24:00"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","description":"New organization created","ip_address":"::1","method":"POST","new_value":"{\"first_name\":\"anas\",\"last_name\":\"khan\",\"middle_name\":\"\",\"email\":\"<EMAIL>\",\"status\":\"Screened\",\"gender\":\"male\",\"address\":\"mardan\",\"contact_number\":\"10000000000\",\"date_of_birth\":\"01/01/1999\",\"stipend\":\"4\",\"study_enrolled_ids\":\"1\",\"notification\":\"some notification\",\"note\":\"Note for admin\",\"role_id\":\"10\",\"organization_detail_id\":\"1\",\"ecrf_id\":\"A569-656\",\"timezone\":\"Pacific/Honolulu\"}","old_value":"null","operation":"CREATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-12 01:24:23"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-12 01:25:58"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-12 01:25:58"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-12 01:26:02"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-12 01:26:02"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","description":"New organization created","ip_address":"::1","method":"POST","new_value":"{\"first_name\":\"anas\",\"last_name\":\"khan\",\"middle_name\":\"\",\"email\":\"<EMAIL>\",\"status\":\"Screened\",\"gender\":\"male\",\"address\":\"mardan\",\"contact_number\":\"10000000000\",\"date_of_birth\":\"01/01/1999\",\"stipend\":\"34\",\"study_enrolled_ids\":\"1\",\"notification\":\"some notification\",\"note\":\"Note for admin\",\"role_id\":\"10\",\"organization_detail_id\":\"1\",\"ecrf_id\":\"A340-434\",\"timezone\":\"Pacific/Honolulu\"}","old_value":"null","operation":"CREATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-12 01:26:31"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-12 01:28:43"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-12 01:28:43"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","description":"New organization created","ip_address":"::1","method":"POST","new_value":"{\"first_name\":\"anas\",\"last_name\":\"khan\",\"middle_name\":\"\",\"email\":\"<EMAIL>\",\"status\":\"Screened\",\"gender\":\"male\",\"address\":\"mardan\",\"contact_number\":\"10000000000\",\"date_of_birth\":\"01/01/1999\",\"stipend\":\"12\",\"study_enrolled_ids\":\"1\",\"notification\":\"some notification\",\"note\":\"Note for admin\",\"role_id\":\"10\",\"organization_detail_id\":\"1\",\"ecrf_id\":\"A222-222\",\"timezone\":\"Pacific/Midway\"}","old_value":"null","operation":"CREATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-12 01:29:06"}
