const mysql = require("mysql2/promise");
const ExcelJS = require("exceljs");

async function exportMedicineRecordsToExcel(
  studyEnrolledId,
  startDate,
  endDate,
  outputPath
) {
  // 1) Connect to your MySQL database
  const connection = await mysql.createConnection({
    host: "localhost",
    user: "root",
    password: "",
    database: "research_hero",
  });

  try {
    // 2) Run the parameterized query
    const query = `
      SELECT 
        LEFT(o.ecrf_id, 8) AS ecrf_id,
        m.medication_name, 
        m.dosage, 
        m.frequency_type, 
        smr.intake_quantity, 
        smr.date, 
        smr.time,
        smr.status, 
        DATE_FORMAT(smr.created_at, '%Y-%m-%d %H:%i:%s') AS created_at
    
       
      FROM patientmedications AS m
      JOIN organization AS o ON m.user_id = o.user_id
      JOIN submit_medicine_records AS smr ON m.medication_id = smr.medicine_id
     
      WHERE o.study_enrolled_id = ?
      AND smr.date BETWEEN ? AND ?
      ORDER BY ecrf_id ASC, smr.date, smr.time
    `;

    const [rows] = await connection.execute(query, [
      studyEnrolledId,
      startDate,
      endDate,
    ]);

    // 3) Build an Excel workbook and worksheet
    const wb = new ExcelJS.Workbook();
    const ws = wb.addWorksheet("Medicine Records");

    // Define columns (header + key mapping)
    ws.columns = [
      { header: "eCRF ID", key: "ecrf_id", width: 12 },
      { header: "Medication Name", key: "medication_name", width: 25 },
      { header: "Dosage", key: "dosage", width: 15 },
      { header: "Frequency", key: "frequency_type", width: 15 },
      { header: "Intake Quantity", key: "intake_quantity", width: 18 },
      { header: "Dosage Date", key: "date", width: 12 },
      { header: "Dosage Time", key: "time", width: 10 },
      { header: "Creation Date Log  (UTC Time)", key: "created_at", width: 20 },
      
    ];

    // 4) Add rows
    rows.forEach((row) => ws.addRow(row));

    // 5) Save to file
    await wb.xlsx.writeFile(outputPath);
    console.log(`✅ Data exported to ${outputPath}`);
  } catch (err) {
    console.error("❌ Error exporting to Excel:", err);
  } finally {
    await connection.end();
  }
}

exportMedicineRecordsToExcel(
  2, // study_enrolled_id
  "2024-10-14", // start date
  "2025-05-16", // end date
  "./med_records.xlsx" // output file
);


