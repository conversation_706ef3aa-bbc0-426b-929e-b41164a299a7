{"level":"error","message":"Authorization token is missing","timestamp":"2025-04-03 16:40:56"}
{"level":"info","message":"{\"method\":\"POST\",\"api_url\":\"/auth/signin\",\"table_name\":\"Organization\",\"operation\":\"SIGNIN_ATTEMPT\",\"description\":\"Sign-in attempt failed: Invalid email or password\",\"old_value\":\"{\\\"email\\\":\\\"<EMAIL>\\\"}\",\"new_value\":null,\"browser\":\"Chrome 134.0.0\",\"ip_address\":\"::1\",\"user\":\"<EMAIL>\"}","timestamp":"2025-04-03 16:47:10"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 134.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-04-03 16:47:35"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/532","browser":"Chrome 134.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-04-03 16:49:07"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-04-03 16:49:07"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/532","browser":"Chrome 134.0.0","description":"bvnnnnnnnnnnnnnnnnn","ip_address":"::1","method":"PUT","new_value":"{\"contact_number\":\"1712983444\",\"notification\":\"No notification provided\",\"reason\":\"bvnnnnnnnnnnnnnnnnn\"}","old_value":"{\"contact_number\":\"17129837198\",\"notification\":null}","operation":"UPDATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-04-03 16:49:09"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/531","browser":"Chrome 134.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-04-03 17:03:02"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-04-03 17:03:02"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/531","browser":"Chrome 134.0.0","description":"zxxxxxxxxxxxxxxxxxxxxxxxxxxxxzzzzzzzzzzz","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":\"567\",\"last_name\":\"leeea\",\"reason\":\"zxxxxxxxxxxxxxxxxxxxxxxxxxxxxzzzzzzzzzzz\"}","old_value":"{\"user_id\":\"567\",\"last_name\":\"leee\"}","operation":"UPDATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-04-03 17:03:04"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/224","browser":"Chrome 134.0.0","description":"Medicine Updated Successfully","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":568,\"medication_name\":\"Sunobinop Or Placeboo\",\"reason\":\"hahaaaaaaaa\"}","old_value":"{\"user_id\":568,\"medication_name\":\"Sunobinop Or Placebo\"}","operation":"UPDATE","table_name":"Medication","user":"<EMAIL>"},"timestamp":"2025-04-03 17:10:05"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1814","browser":"Chrome 134.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-04-03 17:14:03"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-04-03 17:14:04"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1814","browser":"Chrome 134.0.0","description":"Schedule pending with ID: 1814","ip_address":"::1","method":"PUT","new_value":"{\"schedule_date\":\"2025-04-10\"}","old_value":"{\"schedule_date\":\"2025-04-09T19:00:00.000Z\",\"user_id\":566}","operation":"UPDATE","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-04-03 17:14:06"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1824","browser":"Chrome 134.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-04-03 17:17:13"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-04-03 17:17:14"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1824","browser":"Chrome 134.0.0","description":"Schedule pending with ID: 1824","ip_address":"::1","method":"PUT","new_value":"{\"schedule_date\":\"2025-04-19\",\"user_id\":567}","old_value":"{\"schedule_date\":\"2025-04-16T19:00:00.000Z\",\"user_id\":567}","operation":"UPDATE","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-04-03 17:17:16"}
{"level":"info","message":{"api_url":"/incident_report/updateAETicketStatus/AE-015O3V6","browser":"Chrome 134.0.0","description":"Ticket status updated for ticket ID: AE-015O3V6","ip_address":"::1","method":"PUT","new_value":"{\"ticket_id\":\"AE-015O3V6\",\"status\":\"Under Process\"}","old_value":"{\"ticket_id\":\"AE-015O3V6\",\"status\":\"Open\"}","operation":"UPDATE","table_name":"Ticket Status","user":"<EMAIL>"},"timestamp":"2025-04-03 17:23:40"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/453","browser":"Chrome 134.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-04-03 17:30:44"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-04-03 17:30:44"}
{"level":"info","message":{"api_url":"/schedule/createSchedule","browser":"Other 0.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-04-03 17:30:46"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-04-03 17:30:47"}
{"level":"info","message":{"api_url":"/schedule/createSchedule","browser":"Other 0.0.0","description":"Schedule created successfully for user 565","ip_address":"::1","method":"POST","new_value":"{}","old_value":"null","operation":"CREATE","table_name":"Schedule","user":"<EMAIL>"},"timestamp":"2025-04-03 17:30:55"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/453","browser":"Chrome 134.0.0","description":"Auto-schedule created for user 565","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":565,\"schedule\":{\"schedule_date\":\"2025-04-03\",\"schedule_time\":\"09:00\",\"study_enrolled_id\":\"1\",\"status\":\"Scheduled\",\"user_id\":565,\"note\":\"Auto-Created Schedule\"}}","old_value":"null","operation":"SCHEDULE_CREATED","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-04-03 17:30:58"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/453","browser":"Chrome 134.0.0","description":"Welcome email sent to Subject Test (<EMAIL>)","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":565,\"email\":\"<EMAIL>\",\"subject\":\"Welcome to ResearchHero!\"}","old_value":"null","operation":"EMAIL_SENT","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-04-03 17:30:58"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/453","browser":"Chrome 134.0.0","description":"Registration status updated to Accepted for ID 453","ip_address":"::1","method":"PUT","new_value":"{\"id\":\"453\",\"status\":\"Accepted\",\"reason\":\"Accepted By Super Admin \",\"user_id\":565}","old_value":"{\"user_id\":565}","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-04-03 17:30:58"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/449","browser":"Chrome 134.0.0","ip_address":"::ffff:127.0.0.1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-04-03 17:36:55"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-04-03 17:36:56"}
{"level":"info","message":{"api_url":"/schedule/createSchedule","browser":"Other 0.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-04-03 17:36:58"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-04-03 17:36:59"}
{"level":"info","message":{"api_url":"/schedule/createSchedule","browser":"Other 0.0.0","description":"Schedule created successfully for user 561","ip_address":"::1","method":"POST","new_value":"{}","old_value":"null","operation":"CREATE","table_name":"Schedule","user":"<EMAIL>"},"timestamp":"2025-04-03 17:37:08"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/449","browser":"Chrome 134.0.0","description":"Auto-schedule created for user 561","ip_address":"::ffff:127.0.0.1","method":"PUT","new_value":"{\"user_id\":561,\"schedule\":{\"schedule_date\":\"2025-04-03\",\"schedule_time\":\"09:00\",\"study_enrolled_id\":\"1\",\"status\":\"Scheduled\",\"user_id\":561,\"note\":\"Auto-Created Schedule\"}}","old_value":"null","operation":"SCHEDULE_CREATED","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-04-03 17:37:11"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/449","browser":"Chrome 134.0.0","description":"Welcome email sent to M Ali (<EMAIL>)","ip_address":"::ffff:127.0.0.1","method":"PUT","new_value":"{\"user_id\":561,\"email\":\"<EMAIL>\",\"subject\":\"Welcome to ResearchHero!\"}","old_value":"null","operation":"EMAIL_SENT","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-04-03 17:37:11"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/449","browser":"Chrome 134.0.0","description":"Registration status updated to Accepted for ID 449","ip_address":"::ffff:127.0.0.1","method":"PUT","new_value":"{\"status\":\"Accepted\",\"reason\":\"Accepted By Super Admin \",\"user_id\":561}","old_value":"{}","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-04-03 17:37:11"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/533","browser":"Chrome 134.0.0","ip_address":"::ffff:127.0.0.1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-04-03 17:52:06"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-04-03 17:52:07"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/533","browser":"Chrome 134.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-04-03 17:52:33"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-04-03 17:52:33"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/533","browser":"Chrome 134.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-04-03 17:52:50"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-04-03 17:52:50"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/533","browser":"Chrome 134.0.0","description":"vvvvvvvvvvvvvv","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":\"569\",\"status\":\"Lost to Follow up\",\"reason\":\"vvvvvvvvvvvvvv\"}","old_value":"{\"user_id\":\"569\",\"status\":\"Randomized\"}","operation":"UPDATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-04-03 17:52:52"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1886","browser":"Chrome 134.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-04-03 19:35:14"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-04-03 19:35:15"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1886","browser":"Chrome 134.0.0","description":"Schedule cancelled with ID: 1886","ip_address":"::1","method":"PUT","new_value":"{\"schedule_date\":\"2025-04-16\",\"status\":\"Cancelled\",\"user_id\":575,\"schedule_id\":\"1886\"}","old_value":"{\"schedule_date\":\"2025-04-16T19:00:00.000Z\",\"status\":\"Pending\",\"user_id\":575,\"schedule_id\":\"1886\"}","operation":"UPDATE","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-04-03 19:35:18"}
{"level":"info","message":{"api_url":"/organization/logout","browser":"Chrome 134.0.0","description":"User logged out successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"LOGOUT","table_name":"N/A","user":"<EMAIL>"},"timestamp":"2025-04-03 19:35:53"}
{"level":"info","message":{"api_url":"/organization/logout","browser":"Chrome 134.0.0","description":"User logged out successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"LOGOUT","table_name":"N/A","user":"<EMAIL>"},"timestamp":"2025-04-03 19:35:54"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 134.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-04-03 19:35:58"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1884","browser":"Chrome 134.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-04-03 20:43:35"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-04-03 20:43:35"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1884","browser":"Chrome 134.0.0","description":"Schedule completed with ID: 1884","ip_address":"::1","method":"PUT","new_value":"{\"schedule_date\":\"2025-04-02\",\"status\":\"Completed\",\"user_id\":575,\"schedule_id\":\"1884\"}","old_value":"{\"schedule_date\":\"2025-04-02T19:00:00.000Z\",\"status\":\"Scheduled\",\"user_id\":575,\"schedule_id\":\"1884\"}","operation":"UPDATE","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-04-03 20:43:38"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 134.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-04-03 22:06:41"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1885","browser":"Chrome 134.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-04-03 22:55:35"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-04-03 22:55:35"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1885","browser":"Chrome 134.0.0","description":"Schedule completed with ID: 1885","ip_address":"::1","method":"PUT","new_value":"{\"schedule_date\":\"2025-04-09\",\"status\":\"Completed\",\"user_id\":575,\"schedule_id\":\"1885\"}","old_value":"{\"schedule_date\":\"2025-04-09T19:00:00.000Z\",\"status\":\"Pending\",\"user_id\":575,\"schedule_id\":\"1885\"}","operation":"UPDATE","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-04-03 22:55:39"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1886","browser":"Chrome 134.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-04-03 22:56:09"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-04-03 22:56:10"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1886","browser":"Chrome 134.0.0","description":"Schedule completed with ID: 1886","ip_address":"::1","method":"PUT","new_value":"{\"schedule_date\":\"2025-04-15\",\"status\":\"Completed\",\"user_id\":575,\"schedule_id\":\"1886\"}","old_value":"{\"schedule_date\":\"2025-04-15T19:00:00.000Z\",\"status\":\"Cancelled\",\"user_id\":575,\"schedule_id\":\"1886\"}","operation":"UPDATE","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-04-03 22:56:11"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1887","browser":"Chrome 134.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-04-03 23:02:51"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-04-03 23:02:52"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1887","browser":"Chrome 134.0.0","description":"Schedule completed with ID: 1887","ip_address":"::1","method":"PUT","new_value":"{\"schedule_date\":\"2025-04-22\",\"status\":\"Completed\",\"user_id\":575,\"schedule_id\":\"1887\"}","old_value":"{\"schedule_date\":\"2025-04-22T19:00:00.000Z\",\"status\":\"Pending\",\"user_id\":575,\"schedule_id\":\"1887\"}","operation":"UPDATE","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-04-03 23:02:53"}
