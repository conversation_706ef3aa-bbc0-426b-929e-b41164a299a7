{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-13 18:36:47"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-13 18:38:53"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-13 18:47:03"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-13 19:02:46"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-13 19:12:34"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-13 20:20:12"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-13 20:24:13"}
{"level":"error","message":"Failed to log to database: Data too long for column 'description' at row 1","timestamp":"2025-01-13 20:24:15"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-13 20:25:51"}
{"level":"error","message":"Failed to log to database: Data too long for column 'description' at row 1","timestamp":"2025-01-13 20:25:53"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-13 20:28:31"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-13 20:29:15"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-13 20:30:12"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-13 20:43:22"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-13 20:44:09"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-13 20:45:03"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-13 20:45:30"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-13 20:45:59"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-13 20:46:43"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-13 20:47:09"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-13 20:48:13"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-13 20:51:59"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-13 20:53:16"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-13 20:57:24"}
