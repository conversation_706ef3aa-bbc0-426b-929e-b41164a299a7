{"level":"info","message":{"api_url":"/registration_approval/updateStatus/272","browser":"Chrome 132.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-02-03 13:27:41"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-03 13:27:41"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/272","browser":"Chrome 132.0.0","description":"Welcome email sent to yoboi haaland (<EMAIL>)","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":375,\"email\":\"<EMAIL>\",\"subject\":\"Welcome to ResearchHero!\"}","old_value":"null","operation":"EMAIL_SENT","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-02-03 13:27:44"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/272","browser":"Chrome 132.0.0","description":"Registration status updated to Accepted for ID 272","ip_address":"::1","method":"PUT","new_value":"{\"id\":\"272\",\"status\":\"Accepted\",\"reason\":\"Accepted By Super Admin \",\"user_id\":375}","old_value":"[{\"account_status_id\":272,\"user_id\":375,\"account_status\":\"Pending\",\"reason\":\"Initial registration\",\"first_time\":\"1\",\"updated_at\":\"2024-11-25T19:35:03.000Z\"}]","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-02-03 13:27:45"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/269","browser":"Chrome 132.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-02-03 13:29:54"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-03 13:29:54"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/269","browser":"Chrome 132.0.0","description":"Welcome email sent to SAM Test (<EMAIL>)","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":372,\"email\":\"<EMAIL>\",\"subject\":\"Welcome to ResearchHero!\"}","old_value":"null","operation":"EMAIL_SENT","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-02-03 13:29:57"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/269","browser":"Chrome 132.0.0","description":"Registration status updated to Accepted for ID 269","ip_address":"::1","method":"PUT","new_value":"{\"id\":\"269\",\"status\":\"Accepted\",\"reason\":\"Accepted By Super Admin \",\"user_id\":372}","old_value":"[{\"account_status_id\":269,\"user_id\":372,\"account_status\":\"Pending\",\"reason\":\"Initial registration\",\"first_time\":\"1\",\"updated_at\":\"2024-11-25T17:32:40.000Z\"}]","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-02-03 13:29:58"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/265","browser":"Chrome 132.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-02-03 13:31:14"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-03 13:31:15"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/265","browser":"Chrome 132.0.0","description":"Welcome email sent to studyb patient (<EMAIL>)","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":368,\"email\":\"<EMAIL>\",\"subject\":\"Welcome to ResearchHero!\"}","old_value":"null","operation":"EMAIL_SENT","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-02-03 13:31:18"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/265","browser":"Chrome 132.0.0","description":"Registration status updated to Accepted for ID 265","ip_address":"::1","method":"PUT","new_value":"{\"id\":\"265\",\"status\":\"Accepted\",\"reason\":\"Accepted By Super Admin \",\"user_id\":368}","old_value":"[{\"account_status_id\":265,\"user_id\":368,\"account_status\":\"Pending\",\"reason\":\"Initial registration\",\"first_time\":\"1\",\"updated_at\":\"2024-11-19T10:39:33.000Z\"}]","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-02-03 13:31:19"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/258","browser":"Chrome 132.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-02-03 13:38:11"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-03 13:38:11"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/258","browser":"Chrome 132.0.0","description":"Welcome email sent to Pablo Gonzales (<EMAIL>)","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":362,\"email\":\"<EMAIL>\",\"subject\":\"Welcome to ResearchHero!\"}","old_value":"null","operation":"EMAIL_SENT","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-02-03 13:38:14"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/258","browser":"Chrome 132.0.0","description":"Registration status updated to Accepted for ID 258","ip_address":"::1","method":"PUT","new_value":"{\"id\":\"258\",\"status\":\"Accepted\",\"reason\":\"Accepted By Super Admin \",\"user_id\":362}","old_value":"[{\"account_status_id\":258,\"user_id\":362,\"account_status\":\"Pending\",\"reason\":\"Initial registration\",\"first_time\":\"1\",\"updated_at\":\"2024-11-16T06:56:28.000Z\"}]","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-02-03 13:38:16"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/260","browser":"Chrome 132.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-02-03 13:41:39"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-03 13:41:40"}
{"level":"info","message":{"api_url":"/schedule/createSchedule","browser":"Other 0.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-02-03 13:41:42"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-03 13:41:42"}
{"level":"info","message":{"api_url":"/schedule/createSchedule","browser":"Other 0.0.0","description":"Schedule created successfully for user 364","ip_address":"::1","method":"POST","new_value":"{}","old_value":"null","operation":"CREATE","table_name":"Schedule","user":"<EMAIL>"},"timestamp":"2025-02-03 13:41:47"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/260","browser":"Chrome 132.0.0","description":"Auto-schedule created for user 364","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":364,\"schedule\":{\"schedule_date\":\"2025-02-03\",\"schedule_time\":\"09:00\",\"study_enrolled_id\":\"2\",\"status\":\"Scheduled\",\"user_id\":364,\"note\":\"Auto-Created Schedule\"}}","old_value":"null","operation":"SCHEDULE_CREATED","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-02-03 13:41:50"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/260","browser":"Chrome 132.0.0","description":"Welcome email sent to Seshadri Hariharan (<EMAIL>)","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":364,\"email\":\"<EMAIL>\",\"subject\":\"Welcome to ResearchHero!\"}","old_value":"null","operation":"EMAIL_SENT","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-02-03 13:41:50"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/260","browser":"Chrome 132.0.0","description":"Registration status updated to Accepted for ID 260","ip_address":"::1","method":"PUT","new_value":"{\"id\":\"260\",\"status\":\"Accepted\",\"reason\":\"Accepted By Super Admin \",\"user_id\":364}","old_value":"[{\"account_status_id\":260,\"user_id\":364,\"account_status\":\"Pending\",\"reason\":\"Validated by the Administrators\",\"first_time\":\"1\",\"updated_at\":\"2024-11-16T06:56:28.000Z\"}]","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-02-03 13:41:50"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/264","browser":"Chrome 132.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-02-03 13:43:59"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-03 13:44:00"}
{"level":"info","message":{"api_url":"/schedule/createSchedule","browser":"Other 0.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-02-03 13:44:01"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-03 13:44:01"}
{"level":"info","message":{"api_url":"/schedule/createSchedule","browser":"Other 0.0.0","description":"Schedule created successfully for user 367","ip_address":"::1","method":"POST","new_value":"{}","old_value":"null","operation":"CREATE","table_name":"Schedule","user":"<EMAIL>"},"timestamp":"2025-02-03 13:44:07"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/264","browser":"Chrome 132.0.0","description":"Auto-schedule created for user 367","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":367,\"schedule\":{\"schedule_date\":\"2025-02-03\",\"schedule_time\":\"09:00\",\"study_enrolled_id\":\"2\",\"status\":\"Scheduled\",\"user_id\":367,\"note\":\"Auto-Created Schedule\"}}","old_value":"null","operation":"SCHEDULE_CREATED","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-02-03 13:44:09"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/264","browser":"Chrome 132.0.0","description":"Welcome email sent to studyb1 patient (<EMAIL>)","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":367,\"email\":\"<EMAIL>\",\"subject\":\"Welcome to ResearchHero!\"}","old_value":"null","operation":"EMAIL_SENT","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-02-03 13:44:10"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/264","browser":"Chrome 132.0.0","description":"Registration status updated to Accepted for ID 264","ip_address":"::1","method":"PUT","new_value":"{\"id\":\"264\",\"status\":\"Accepted\",\"reason\":\"Accepted By Super Admin \",\"user_id\":367}","old_value":"[{\"account_status_id\":264,\"user_id\":367,\"account_status\":\"Pending\",\"reason\":\"Initial registration\",\"first_time\":\"1\",\"updated_at\":\"2024-11-19T10:10:11.000Z\"}]","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-02-03 13:44:10"}
{"level":"info","message":{"api_url":"/organization/deleteOrganization/344","browser":"Chrome 132.0.0","ip_address":"::1","method":"DELETE","new_value":"null","old_value":"null","operation":"delete","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-02-03 13:45:02"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-03 13:45:02"}
{"level":"info","message":{"api_url":"/organization/deleteOrganization/344","browser":"Chrome 132.0.0","ip_address":"::1","method":"DELETE","new_value":"null","old_value":"null","operation":"delete","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-02-03 13:45:08"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-03 13:45:08"}
{"level":"info","message":{"api_url":"/organization/deleteOrganization/344","browser":"Chrome 132.0.0","ip_address":"::1","method":"DELETE","new_value":"null","old_value":"null","operation":"delete","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-02-03 13:47:30"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-03 13:47:31"}
{"level":"info","message":{"api_url":"/organization/deleteOrganization/344","browser":"Chrome 132.0.0","ip_address":"::1","method":"DELETE","new_value":"null","old_value":"null","operation":"delete","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-02-03 14:01:53"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-03 14:01:53"}
{"level":"info","message":{"api_url":"/organization/deleteOrganization/344","browser":"Chrome 132.0.0","description":"Accepted By Super Admin ","ip_address":"::1","method":"DELETE","new_value":"null","old_value":"{\"organization_id\":316,\"first_name\":\"testpatients\",\"middle_name\":\"\",\"last_name\":\"studyb\",\"status\":\"Randomized\",\"is_randomized\":0,\"is_compliant\":1,\"gender\":\"male\",\"address\":\"Some address\",\"timezone\":\"UTC\",\"contact_number\":\"917772372377\",\"date_of_birth\":\"11/08/1999\",\"stipend\":\"1\",\"image\":null,\"study_enrolled_id\":\"2\",\"date_enrolled\":\"11/14/2024\",\"notification\":\"some notification\",\"user_id\":344,\"organization_detail_id\":1,\"role_id\":10,\"ecrf_id\":\"B101-000\",\"email\":\"<EMAIL>\",\"organization_name\":\"Dr Prabhu Manjeshwar\",\"organization_address\":\"7080 Southwest Freeway Houston TX 77074o\",\"note\":\"Note for admin\",\"enrolled_ids\":\"2\",\"study_names\":\"SUN2003B\",\"investigator_user_ids\":\"343,382,394,396\",\"study_enrolled\":[{\"id\":2,\"name\":\"SUN2003B\"}],\"investigators\":[{\"user_id\":343,\"first_name\":\"Dr Angela\",\"last_name\":\"Eakin\"},{\"user_id\":382,\"first_name\":\"dev\",\"last_name\":\"PI\"},{\"user_id\":394,\"first_name\":\"Dr. Angela\",\"last_name\":\"Eakin\"},{\"user_id\":396,\"first_name\":\"Dr. Angela\",\"last_name\":\"Eakin\"}]}","operation":"DELETE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-02-03 14:01:55"}
{"level":"info","message":{"api_url":"/organization/deleteOrganization/364","browser":"Chrome 132.0.0","ip_address":"::1","method":"DELETE","new_value":"null","old_value":"null","operation":"delete","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-02-03 14:02:53"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-03 14:02:53"}
{"level":"info","message":{"api_url":"/organization/deleteOrganization/364","browser":"Chrome 132.0.0","description":"Accepted By Super Admin ","ip_address":"::1","method":"DELETE","new_value":"null","old_value":"{\"organization_id\":333,\"first_name\":\"Seshadri\",\"middle_name\":\"\",\"last_name\":\"Hariharan\",\"status\":\"Randomized\",\"is_randomized\":0,\"is_compliant\":1,\"gender\":\"male\",\"address\":\"Satellite Town , Rawalpindi\",\"timezone\":\"Europe/Prague\",\"contact_number\":\"11231231232\",\"date_of_birth\":\"02/03/2001\",\"stipend\":\"0\",\"image\":null,\"study_enrolled_id\":\"2\",\"date_enrolled\":\"11/14/2024\",\"notification\":\"some notification\",\"user_id\":364,\"organization_detail_id\":2,\"role_id\":10,\"ecrf_id\":\"AAAA-007\",\"email\":\"<EMAIL>\",\"organization_name\":\"Dr Mudassar Hassan\",\"organization_address\":\"76 Belmont Avenue, Brooklyn, NY, USA-11212\",\"note\":\"Note for admin\",\"enrolled_ids\":\"2\",\"study_names\":\"SUN2003B\",\"investigator_user_ids\":\"343,382,394,396\",\"study_enrolled\":[{\"id\":2,\"name\":\"SUN2003B\"}],\"investigators\":[{\"user_id\":343,\"first_name\":\"Dr Angela\",\"last_name\":\"Eakin\"},{\"user_id\":382,\"first_name\":\"dev\",\"last_name\":\"PI\"},{\"user_id\":394,\"first_name\":\"Dr. Angela\",\"last_name\":\"Eakin\"},{\"user_id\":396,\"first_name\":\"Dr. Angela\",\"last_name\":\"Eakin\"}]}","operation":"DELETE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-02-03 14:02:56"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 132.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-02-03 14:18:08"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-03 14:18:09"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 132.0.0","description":"New organization created","ip_address":"::1","method":"POST","new_value":"{\"first_name\":\"left\",\"last_name\":\"right\",\"middle_name\":\"\",\"email\":\"<EMAIL>\",\"status\":\"Screened\",\"gender\":\"male\",\"address\":\"5959 Bonhomme Rd\",\"contact_number\":\"12377292048\",\"date_of_birth\":\"06/10/1986\",\"stipend\":\"2\",\"study_enrolled_ids\":\"1\",\"notification\":\"some notification\",\"note\":\"Note for admin\",\"role_id\":\"10\",\"organization_detail_id\":\"1\",\"ecrf_id\":\"A343-434\",\"timezone\":\"Asia/Karachi\"}","old_value":"null","operation":"CREATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-02-03 14:18:11"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/337","browser":"Chrome 132.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-02-03 14:18:32"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-03 14:18:32"}
{"level":"info","message":{"api_url":"/schedule/createSchedule","browser":"Other 0.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-02-03 14:18:34"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-03 14:18:34"}
{"level":"info","message":{"api_url":"/schedule/createSchedule","browser":"Other 0.0.0","description":"Schedule created successfully for user 444","ip_address":"::1","method":"POST","new_value":"{}","old_value":"null","operation":"CREATE","table_name":"Schedule","user":"<EMAIL>"},"timestamp":"2025-02-03 14:18:42"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/337","browser":"Chrome 132.0.0","description":"Auto-schedule created for user 444","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":444,\"schedule\":{\"schedule_date\":\"2025-02-03\",\"schedule_time\":\"09:00\",\"study_enrolled_id\":\"1\",\"status\":\"Scheduled\",\"user_id\":444,\"note\":\"Auto-Created Schedule\"}}","old_value":"null","operation":"SCHEDULE_CREATED","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-02-03 14:18:44"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/337","browser":"Chrome 132.0.0","description":"Welcome email sent to left right (<EMAIL>)","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":444,\"email\":\"<EMAIL>\",\"subject\":\"Welcome to ResearchHero!\"}","old_value":"null","operation":"EMAIL_SENT","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-02-03 14:18:45"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/337","browser":"Chrome 132.0.0","description":"Registration status updated to Accepted for ID 337","ip_address":"::1","method":"PUT","new_value":"{\"id\":\"337\",\"status\":\"Accepted\",\"reason\":\"Accepted By Super Admin \",\"user_id\":444}","old_value":"[{\"account_status_id\":337,\"user_id\":444,\"account_status\":\"Pending\",\"reason\":\"Initial registration\",\"first_time\":\"1\",\"updated_at\":\"2025-02-03T09:18:11.000Z\"}]","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-02-03 14:18:45"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 132.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-02-03 14:39:26"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-03 14:39:26"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 132.0.0","description":"New organization created","ip_address":"::1","method":"POST","new_value":"{\"first_name\":\"off\",\"last_name\":\"set\",\"middle_name\":\"\",\"email\":\"<EMAIL>\",\"status\":\"Screened\",\"gender\":\"male\",\"address\":\"5959 Bonhomme Rd\",\"contact_number\":\"12377292048\",\"date_of_birth\":\"06/04/1996\",\"stipend\":\"3\",\"study_enrolled_ids\":\"1\",\"notification\":\"some notification\",\"note\":\"Note for admin\",\"role_id\":\"10\",\"organization_detail_id\":\"1\",\"ecrf_id\":\"A455-655\",\"timezone\":\"Asia/Karachi\"}","old_value":"null","operation":"CREATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-02-03 14:39:29"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/338","browser":"Chrome 132.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-02-03 14:40:08"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-03 14:40:08"}
{"level":"info","message":{"api_url":"/schedule/createSchedule","browser":"Other 0.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-02-03 14:40:10"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-03 14:40:10"}
{"level":"info","message":{"api_url":"/schedule/createSchedule","browser":"Other 0.0.0","description":"Schedule created successfully for user 445","ip_address":"::1","method":"POST","new_value":"{}","old_value":"null","operation":"CREATE","table_name":"Schedule","user":"<EMAIL>"},"timestamp":"2025-02-03 14:40:17"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/338","browser":"Chrome 132.0.0","description":"Auto-schedule created for user 445","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":445,\"schedule\":{\"schedule_date\":\"2025-02-03\",\"schedule_time\":\"09:00\",\"study_enrolled_id\":\"1\",\"status\":\"Scheduled\",\"user_id\":445,\"note\":\"Auto-Created Schedule\"}}","old_value":"null","operation":"SCHEDULE_CREATED","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-02-03 14:40:20"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/338","browser":"Chrome 132.0.0","description":"Welcome email sent to off set (<EMAIL>)","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":445,\"email\":\"<EMAIL>\",\"subject\":\"Welcome to ResearchHero!\"}","old_value":"null","operation":"EMAIL_SENT","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-02-03 14:40:20"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/338","browser":"Chrome 132.0.0","description":"Registration status updated to Accepted for ID 338","ip_address":"::1","method":"PUT","new_value":"{\"id\":\"338\",\"status\":\"Accepted\",\"reason\":\"Accepted By Super Admin \",\"user_id\":445}","old_value":"[{\"account_status_id\":338,\"user_id\":445,\"account_status\":\"Pending\",\"reason\":\"Initial registration\",\"first_time\":\"1\",\"updated_at\":\"2025-02-03T09:39:29.000Z\"}]","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-02-03 14:40:21"}
{"level":"error","message":"Failed to log to database or Excel: Corrupted zip or bug: expected 16 records in central dir, got 0","timestamp":"2025-02-03 14:40:21"}
{"level":"error","message":"Failed to log to database or Excel: Corrupted zip or bug: expected 16 records in central dir, got 0","timestamp":"2025-02-03 14:40:21"}
{"level":"info","message":{"api_url":"/organization/logout","browser":"Chrome 132.0.0","description":"User logged out successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"LOGOUT","table_name":"N/A","user":"<EMAIL>"},"timestamp":"2025-02-03 14:52:43"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-03 14:52:49"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-03 15:24:00"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-03 15:24:31"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-03 15:38:50"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-03 15:39:00"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-03 15:39:39"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-03 15:41:17"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-03 15:46:47"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-03 15:48:11"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 132.0.0","description":"User signed in successfully","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-02-03 16:03:01"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 132.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-02-03 16:03:02"}
{"level":"error","message":"Failed to log to database or Excel: Column 'ip_address' cannot be null","timestamp":"2025-02-03 16:03:02"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 132.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-02-03 16:03:02"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 132.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-02-03 16:11:04"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-03 16:11:05"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 132.0.0","description":"New organization created","ip_address":"::1","method":"POST","new_value":"{\"first_name\":\"top\",\"last_name\":\"right\",\"middle_name\":\"\",\"email\":\"<EMAIL>\",\"status\":\"Screened\",\"gender\":\"male\",\"address\":\"5959 Bonhomme Rd\",\"contact_number\":\"12377292048\",\"date_of_birth\":\"02/03/1998\",\"stipend\":\"3\",\"study_enrolled_ids\":\"1\",\"notification\":\"some notification\",\"note\":\"Note for admin\",\"role_id\":\"10\",\"organization_detail_id\":\"1\",\"ecrf_id\":\"A565-556\",\"timezone\":\"Asia/Karachi\"}","old_value":"null","operation":"CREATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-02-03 16:11:07"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/339","browser":"Chrome 132.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-02-03 16:13:18"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-03 16:13:18"}
{"level":"info","message":{"api_url":"/schedule/createSchedule","browser":"Other 0.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-02-03 16:13:20"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-03 16:13:20"}
{"level":"info","message":{"api_url":"/schedule/createSchedule","browser":"Other 0.0.0","description":"Schedule created successfully for user 446","ip_address":"::1","method":"POST","new_value":"{}","old_value":"null","operation":"CREATE","table_name":"Schedule","user":"<EMAIL>"},"timestamp":"2025-02-03 16:13:26"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/339","browser":"Chrome 132.0.0","description":"Auto-schedule created for user 446","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":446,\"schedule\":{\"schedule_date\":\"2025-02-03\",\"schedule_time\":\"09:00\",\"study_enrolled_id\":\"1\",\"status\":\"Scheduled\",\"user_id\":446,\"note\":\"Auto-Created Schedule\"}}","old_value":"null","operation":"SCHEDULE_CREATED","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-02-03 16:13:28"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/339","browser":"Chrome 132.0.0","description":"Welcome email sent to top right (<EMAIL>)","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":446,\"email\":\"<EMAIL>\",\"subject\":\"Welcome to ResearchHero!\"}","old_value":"null","operation":"EMAIL_SENT","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-02-03 16:13:28"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/339","browser":"Chrome 132.0.0","description":"Registration status updated to Accepted for ID 339","ip_address":"::1","method":"PUT","new_value":"{\"id\":\"339\",\"status\":\"Accepted\",\"reason\":\"Accepted By Super Admin \",\"user_id\":446}","old_value":"[{\"account_status_id\":339,\"user_id\":446,\"account_status\":\"Pending\",\"reason\":\"Initial registration\",\"first_time\":\"1\",\"updated_at\":\"2025-02-03T11:11:07.000Z\"}]","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-02-03 16:13:28"}
{"level":"info","message":{"api_url":"/organization/deleteOrganization/445","browser":"Chrome 132.0.0","ip_address":"::1","method":"DELETE","new_value":"null","old_value":"null","operation":"delete","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-02-03 16:14:05"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-03 16:14:05"}
{"level":"info","message":{"api_url":"/organization/deleteOrganization/445","browser":"Chrome 132.0.0","description":"Accepted By Super Admin ","ip_address":"::1","method":"DELETE","new_value":"null","old_value":"{\"organization_id\":414,\"first_name\":\"off\",\"middle_name\":null,\"last_name\":\"set\",\"status\":\"Screened\",\"is_randomized\":0,\"is_compliant\":1,\"gender\":\"male\",\"address\":\"5959 Bonhomme Rd\",\"timezone\":\"Asia/Karachi\",\"contact_number\":\"12377292048\",\"date_of_birth\":\"06/04/1996\",\"stipend\":\"3\",\"image\":null,\"study_enrolled_id\":\"1\",\"date_enrolled\":\"02/03/2025\",\"notification\":\"some notification\",\"user_id\":445,\"organization_detail_id\":1,\"role_id\":10,\"ecrf_id\":\"A455-655\",\"email\":\"<EMAIL>\",\"organization_name\":\"Dr Prabhu Manjeshwar\",\"organization_address\":\"7080 Southwest Freeway Houston TX 77074o\",\"note\":\"Note for admin\",\"enrolled_ids\":\"1\",\"study_names\":\"SUN2003A - 102\",\"investigator_user_ids\":\"337,346,347,401,406\",\"study_enrolled\":[{\"id\":1,\"name\":\"SUN2003A - 102\"}],\"investigators\":[{\"user_id\":337,\"first_name\":\"Aurelie\",\"last_name\":\"Foray\"},{\"user_id\":346,\"first_name\":\"Taufeeq\",\"last_name\":\"khan\"},{\"user_id\":347,\"first_name\":\"Dr Mudassar\",\"last_name\":\"Hassan\"},{\"user_id\":401,\"first_name\":\"Dr Mudassar\",\"last_name\":\"Hassan\"},{\"user_id\":406,\"first_name\":\"\",\"last_name\":\"\"}]}","operation":"DELETE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-02-03 16:14:06"}
{"level":"info","message":{"api_url":"/organization/deleteOrganization/444","browser":"Chrome 132.0.0","ip_address":"::1","method":"DELETE","new_value":"null","old_value":"null","operation":"delete","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-02-03 16:14:26"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-03 16:14:27"}
{"level":"info","message":{"api_url":"/organization/deleteOrganization/444","browser":"Chrome 132.0.0","description":"Accepted By Super Admin ","ip_address":"::1","method":"DELETE","new_value":"null","old_value":"{\"organization_id\":413,\"first_name\":\"left\",\"middle_name\":null,\"last_name\":\"right\",\"status\":\"Screened\",\"is_randomized\":0,\"is_compliant\":1,\"gender\":\"male\",\"address\":\"5959 Bonhomme Rd\",\"timezone\":\"Asia/Karachi\",\"contact_number\":\"12377292048\",\"date_of_birth\":\"06/10/1986\",\"stipend\":\"2\",\"image\":null,\"study_enrolled_id\":\"1\",\"date_enrolled\":\"02/03/2025\",\"notification\":\"some notification\",\"user_id\":444,\"organization_detail_id\":1,\"role_id\":10,\"ecrf_id\":\"A343-434\",\"email\":\"<EMAIL>\",\"organization_name\":\"Dr Prabhu Manjeshwar\",\"organization_address\":\"7080 Southwest Freeway Houston TX 77074o\",\"note\":\"Note for admin\",\"enrolled_ids\":\"1\",\"study_names\":\"SUN2003A - 102\",\"investigator_user_ids\":\"337,346,347,401,406\",\"study_enrolled\":[{\"id\":1,\"name\":\"SUN2003A - 102\"}],\"investigators\":[{\"user_id\":337,\"first_name\":\"Aurelie\",\"last_name\":\"Foray\"},{\"user_id\":346,\"first_name\":\"Taufeeq\",\"last_name\":\"khan\"},{\"user_id\":347,\"first_name\":\"Dr Mudassar\",\"last_name\":\"Hassan\"},{\"user_id\":401,\"first_name\":\"Dr Mudassar\",\"last_name\":\"Hassan\"},{\"user_id\":406,\"first_name\":\"\",\"last_name\":\"\"}]}","operation":"DELETE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-02-03 16:14:28"}
{"level":"info","message":{"api_url":"/organization/logout","browser":"Chrome 132.0.0","description":"User logged out successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"LOGOUT","table_name":"N/A","user":"<EMAIL>"},"timestamp":"2025-02-03 16:28:28"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-03 16:28:37"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-03 17:21:00"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-03 17:21:16"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-03 18:02:44"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-03 18:06:58"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-03 18:55:42"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-03 18:56:11"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-04 08:41:40"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-04 08:41:56"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-04 08:41:57"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-04 08:41:57"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-04 08:41:58"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-04 08:41:58"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-04 08:41:58"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-04 08:41:58"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-04 08:41:59"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-04 08:41:59"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-04 08:41:59"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-04 08:41:59"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-04 08:42:00"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-04 08:57:42"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-04 08:57:43"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-04 08:57:43"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-04 08:57:44"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-04 08:57:44"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-04 08:57:53"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-04 08:58:04"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-04 09:25:13"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-04 09:25:15"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-04 09:25:24"}
