{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 135.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-04-28 05:45:18"}
{"level":"info","message":{"api_url":"/organization/logout","browser":"Chrome 135.0.0","description":"User logged out successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"LOGOUT","table_name":"N/A","user":"<EMAIL>"},"timestamp":"2025-04-28 06:02:39"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 135.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-04-28 06:02:59"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 135.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-04-28 06:13:33"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-04-28 06:13:34"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 135.0.0","description":"New organization created","ip_address":"::1","method":"POST","new_value":"{\"first_name\":\"anas\",\"last_name\":\"test\",\"middle_name\":\"\",\"email\":\"<EMAIL>\",\"status\":\"Screened\",\"gender\":\"male\",\"address\":\"sad\",\"contact_number\":\"12377292048\",\"date_of_birth\":\"04/06/1983\",\"stipend\":\"12\",\"study_enrolled_ids\":\"1\",\"notification\":\"some notification\",\"note\":\"Note for admin\",\"role_id\":\"10\",\"organization_detail_id\":\"1\",\"ecrf_id\":\"ASD2-323\",\"timezone\":\"Pacific/Easter\"}","old_value":"null","operation":"CREATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-04-28 06:13:36"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/425","browser":"Chrome 135.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-04-28 06:14:15"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-04-28 06:14:15"}
{"level":"info","message":{"api_url":"/schedule/createSchedule","browser":"Other 0.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-04-28 06:14:17"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-04-28 06:14:18"}
{"level":"info","message":{"api_url":"/schedule/createSchedule","browser":"Other 0.0.0","description":"Schedule created successfully for user 512","ip_address":"::1","method":"POST","new_value":"{}","old_value":"null","operation":"CREATE","table_name":"Schedule","user":"<EMAIL>"},"timestamp":"2025-04-28 06:14:24"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/425","browser":"Chrome 135.0.0","description":"Auto-schedule created for user 512","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":512,\"schedule\":{\"schedule_date\":\"2025-04-28\",\"schedule_time\":\"09:00\",\"study_enrolled_id\":\"1\",\"status\":\"Scheduled\",\"user_id\":512,\"note\":\"Auto-Created Schedule\"}}","old_value":"null","operation":"SCHEDULE_CREATED","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-04-28 06:14:26"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/425","browser":"Chrome 135.0.0","description":"Welcome email sent to anas test (<EMAIL>)","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":512,\"email\":\"<EMAIL>\",\"subject\":\"Welcome to ResearchHero!\"}","old_value":"null","operation":"EMAIL_SENT","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-04-28 06:14:27"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/425","browser":"Chrome 135.0.0","description":"Registration status updated to Accepted for ID 425","ip_address":"::1","method":"PUT","new_value":"{\"status\":\"Accepted\",\"reason\":\"Accepted By Super Admin \",\"user_id\":512}","old_value":"{}","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-04-28 06:14:27"}
