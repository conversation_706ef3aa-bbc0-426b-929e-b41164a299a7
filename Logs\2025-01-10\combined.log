{"level":"info","message":{"api_url":"/incident_report/createIncidentReportResponse","browser":"Other 0.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"incident report response","user":"<EMAIL>"},"timestamp":"2025-01-10 16:06:46"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-10 16:06:47"}
{"level":"info","message":{"api_url":"/incident_report/createIncidentReportResponse","browser":"Other 0.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"incident report response","user":"<EMAIL>"},"timestamp":"2025-01-10 16:12:06"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-10 16:12:06"}
{"level":"info","message":{"api_url":"/incident_report/createIncidentReportResponse","browser":"Other 0.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"incident report response","user":"<EMAIL>"},"timestamp":"2025-01-10 16:17:24"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-10 16:17:25"}
{"level":"info","message":{"api_url":"/incident_report/createIncidentReportResponse","browser":"Other 0.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"incident report response","user":"<EMAIL>"},"timestamp":"2025-01-10 16:20:51"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-10 16:20:51"}
{"level":"info","message":{"api_url":"/incident_report/createIncidentReportResponse","browser":"Other 0.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"incident report response","user":"<EMAIL>"},"timestamp":"2025-01-10 16:23:13"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-10 16:23:13"}
{"level":"info","message":{"api_url":"/incident_report/createIncidentReportResponse","browser":"Other 0.0.0","description":"Incident report response created:\n          study_id: 1,\n          user_id: 409,\n          question_id: 45,\n          response_text: Texto de respuesta en español.,\n          description: Este es un informe de incidente en español.,\n          incident_severety: Moderate,\n          start_date: 01/10/2025,\n          start_time: 4:54:00 PM,\n          medical_issue: 1,\n          end_date: 2024-12-25,\n          end_time: 15:00","ip_address":"::1","method":"POST","new_value":"{\"study_id\":1,\"user_id\":409,\"question_id\":45,\"response_text\":\"Texto de respuesta en español.\",\"description\":\"Este es un informe de incidente en español.\",\"incident_severety\":\"Moderate\",\"start_date\":\"01/10/2025\",\"start_time\":\"4:54:00 PM\",\"medical_issue\":1,\"end_date\":\"2024-12-25\",\"end_time\":\"15:00\"}","old_value":"null","operation":"SUBMIT","table_name":"Incident Report Response","user":"<EMAIL>"},"timestamp":"2025-01-10 16:23:14"}
{"level":"error","message":"Failed to log to database: Data too long for column 'description' at row 1","timestamp":"2025-01-10 16:23:14"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Other 0.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-01-10 18:01:25"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-01-10 21:31:50"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 131.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-01-10 21:32:38"}
{"level":"info","message":{"api_url":"/ecrf/submissions","browser":"Chrome 131.0.0","description":"Submitted ECRF answers","ip_address":"::1","method":"POST","new_value":"{\"userId\":403,\"ticketId\":\"AE-4Z8MMTO\",\"answers\":[{\"questionId\":1,\"answer\":\"No\"},{\"questionId\":2,\"answer\":\"bv\"},{\"questionId\":3,\"answer\":\"No\"},{\"questionId\":4,\"answer\":\"bv\"},{\"questionId\":5,\"answer\":\"bb\"},{\"questionId\":6,\"answer\":\"bv\"},{\"questionId\":7,\"answer\":\"b\"}]}","old_value":"null","operation":"SUBMIT","table_name":"eCRF Answers","user":"<EMAIL>"},"timestamp":"2025-01-10 21:33:49"}
