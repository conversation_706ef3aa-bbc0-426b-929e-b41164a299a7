{"level":"info","message":{"api_url":"/auth/signin","browser":"Other 0.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-13 01:47:22"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 134.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-13 02:17:40"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-13 02:19:12"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-13 02:19:12"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-13 02:19:22"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-13 02:19:23"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","description":"New organization created","ip_address":"::1","method":"POST","new_value":"{\"first_name\":\"as\",\"last_name\":\"as\",\"middle_name\":\"\",\"email\":\"<EMAIL>\",\"status\":\"Screened\",\"gender\":\"male\",\"address\":\"sd\",\"contact_number\":\"10000000000\",\"date_of_birth\":\"02/18/1998\",\"stipend\":\"5\",\"study_enrolled_ids\":\"1\",\"notification\":\"some notification\",\"note\":\"Note for admin\",\"role_id\":\"10\",\"organization_detail_id\":\"1\",\"ecrf_id\":\"A676-76G\",\"timezone\":\"Asia/Karachi\"}","old_value":"null","operation":"CREATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-13 02:19:51"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 134.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-13 20:50:07"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-13 20:51:09"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-13 20:51:09"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","description":"New organization created","ip_address":"::1","method":"POST","new_value":"{\"first_name\":\"cv\",\"last_name\":\"xcv\",\"middle_name\":\"\",\"email\":\"<EMAIL>\",\"status\":\"Screened\",\"gender\":\"male\",\"address\":\"df\",\"contact_number\":\"15444444444\",\"date_of_birth\":\"03/04/1999\",\"stipend\":\"4\",\"study_enrolled_ids\":\"1\",\"notification\":\"some notification\",\"note\":\"Note for admin\",\"role_id\":\"10\",\"organization_detail_id\":\"1\",\"ecrf_id\":\"FG45-454\",\"timezone\":\"Asia/Karachi\"}","old_value":"null","operation":"CREATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-13 20:51:32"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-13 20:52:54"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-13 20:52:54"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","description":"New organization created","ip_address":"::1","method":"POST","new_value":"{\"first_name\":\"cv\",\"last_name\":\"xcv\",\"middle_name\":\"\",\"email\":\"<EMAIL>\",\"status\":\"Screened\",\"gender\":\"male\",\"address\":\"df\",\"contact_number\":\"15444444444\",\"date_of_birth\":\"03/04/1999\",\"stipend\":\"4\",\"study_enrolled_ids\":\"1\",\"notification\":\"some notification\",\"note\":\"Note for admin\",\"role_id\":\"10\",\"organization_detail_id\":\"1\",\"ecrf_id\":\"FG45-45F\",\"timezone\":\"Asia/Karachi\"}","old_value":"null","operation":"CREATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-13 20:53:21"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-13 20:56:20"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-13 20:56:21"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","description":"New organization created","ip_address":"::1","method":"POST","new_value":"{\"first_name\":\"cv\",\"last_name\":\"xcv\",\"middle_name\":\"\",\"email\":\"<EMAIL>\",\"status\":\"Screened\",\"gender\":\"male\",\"address\":\"df\",\"contact_number\":\"15444444444\",\"date_of_birth\":\"03/04/1999\",\"stipend\":\"4\",\"study_enrolled_ids\":\"1\",\"notification\":\"some notification\",\"note\":\"Note for admin\",\"role_id\":\"10\",\"organization_detail_id\":\"1\",\"ecrf_id\":\"FG45-4RT\",\"timezone\":\"Asia/Karachi\"}","old_value":"null","operation":"CREATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-13 20:56:48"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-13 20:58:17"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-13 20:58:17"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","description":"New organization created","ip_address":"::1","method":"POST","new_value":"{\"first_name\":\"cv\",\"last_name\":\"xcv\",\"middle_name\":\"\",\"email\":\"<EMAIL>\",\"status\":\"Screened\",\"gender\":\"male\",\"address\":\"df\",\"contact_number\":\"15444444444\",\"date_of_birth\":\"03/04/1999\",\"stipend\":\"4\",\"study_enrolled_ids\":\"1\",\"notification\":\"some notification\",\"note\":\"Note for admin\",\"role_id\":\"10\",\"organization_detail_id\":\"1\",\"ecrf_id\":\"FG45-4R6\",\"timezone\":\"Asia/Karachi\"}","old_value":"null","operation":"CREATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-13 20:58:45"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-13 21:07:36"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-13 21:07:37"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","description":"New organization created","ip_address":"::1","method":"POST","new_value":"{\"first_name\":\"fg\",\"last_name\":\"ld\",\"middle_name\":\"\",\"email\":\"<EMAIL>\",\"status\":\"Screened\",\"gender\":\"male\",\"address\":\"as\",\"contact_number\":\"10000000000\",\"date_of_birth\":\"01/01/1999\",\"stipend\":\"34\",\"study_enrolled_ids\":\"1\",\"notification\":\"some notification\",\"note\":\"Note for admin\",\"role_id\":\"10\",\"organization_detail_id\":\"1\",\"ecrf_id\":\"AVBB-VBV\",\"timezone\":\"Pacific/Honolulu\"}","old_value":"null","operation":"CREATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-13 21:08:14"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-13 21:10:55"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-13 21:10:56"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-13 21:11:01"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-13 21:11:01"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","description":"New organization created","ip_address":"::1","method":"POST","new_value":"{\"first_name\":\"sdsa\",\"last_name\":\"asd\",\"middle_name\":\"\",\"email\":\"<EMAIL>\",\"status\":\"Screened\",\"gender\":\"male\",\"address\":\"0dgh\",\"contact_number\":\"10000000000\",\"date_of_birth\":\"01/01/1999\",\"stipend\":\"34\",\"study_enrolled_ids\":\"1\",\"notification\":\"some notification\",\"note\":\"Note for admin\",\"role_id\":\"10\",\"organization_detail_id\":\"1\",\"ecrf_id\":\"A565-DFF\",\"timezone\":\"Pacific/Honolulu\"}","old_value":"null","operation":"CREATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-13 21:11:30"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-13 21:13:06"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-13 21:13:06"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","description":"New organization created","ip_address":"::1","method":"POST","new_value":"{\"first_name\":\"sdsa\",\"last_name\":\"asd\",\"middle_name\":\"\",\"email\":\"<EMAIL>\",\"status\":\"Screened\",\"gender\":\"male\",\"address\":\"0dgh\",\"contact_number\":\"10000000000\",\"date_of_birth\":\"01/01/1999\",\"stipend\":\"34\",\"study_enrolled_ids\":\"1\",\"notification\":\"some notification\",\"note\":\"Note for admin\",\"role_id\":\"10\",\"organization_detail_id\":\"1\",\"ecrf_id\":\"A565-TRY\",\"timezone\":\"Pacific/Honolulu\"}","old_value":"null","operation":"CREATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-13 21:13:39"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Other 0.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-13 21:16:14"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-13 21:21:41"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-13 21:21:42"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","description":"New organization created","ip_address":"::1","method":"POST","new_value":"{\"first_name\":\"vvcb\",\"last_name\":\"cbv\",\"middle_name\":\"bvc\",\"email\":\"<EMAIL>\",\"status\":\"Screened\",\"gender\":\"male\",\"address\":\"cxv\",\"contact_number\":\"10000000000\",\"date_of_birth\":\"01/01/1999\",\"stipend\":\"4\",\"study_enrolled_ids\":\"2\",\"notification\":\"some notification\",\"note\":\"Note for admin\",\"role_id\":\"10\",\"organization_detail_id\":\"1\",\"ecrf_id\":\"AHTY-TYT\",\"timezone\":\"Pacific/Midway\"}","old_value":"null","operation":"CREATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-13 21:21:51"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-13 21:23:21"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-13 21:23:21"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","description":"New organization created","ip_address":"::1","method":"POST","new_value":"{\"first_name\":\"vvcb\",\"last_name\":\"cbv\",\"middle_name\":\"bvc\",\"email\":\"<EMAIL>\",\"status\":\"Screened\",\"gender\":\"male\",\"address\":\"cxv\",\"contact_number\":\"10000000000\",\"date_of_birth\":\"01/01/1999\",\"stipend\":\"4\",\"study_enrolled_ids\":\"2\",\"notification\":\"some notification\",\"note\":\"Note for admin\",\"role_id\":\"10\",\"organization_detail_id\":\"1\",\"ecrf_id\":\"AHTY-XZC\",\"timezone\":\"Pacific/Midway\"}","old_value":"null","operation":"CREATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-13 21:23:31"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-13 21:28:30"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-13 21:28:30"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","description":"New organization created","ip_address":"::1","method":"POST","new_value":"{\"first_name\":\"vb\",\"last_name\":\"cvb\",\"middle_name\":\"\",\"email\":\"<EMAIL>\",\"status\":\"Screened\",\"gender\":\"male\",\"address\":\"cxv\",\"contact_number\":\"10000000000\",\"date_of_birth\":\"01/01/1999\",\"stipend\":\"4\",\"study_enrolled_ids\":\"2\",\"notification\":\"some notification\",\"note\":\"Note for admin\",\"role_id\":\"10\",\"organization_detail_id\":\"10\",\"ecrf_id\":\"AFDS-FFD\",\"timezone\":\"Asia/Karachi\"}","old_value":"null","operation":"CREATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-13 21:28:36"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-13 21:31:15"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-13 21:31:16"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","description":"New organization created","ip_address":"::1","method":"POST","new_value":"{\"first_name\":\"xzc\",\"last_name\":\"xzc\",\"middle_name\":\"\",\"email\":\"<EMAIL>\",\"status\":\"Screened\",\"gender\":\"male\",\"address\":\"zdxc\",\"contact_number\":\"18988888888\",\"date_of_birth\":\"01/01/1999\",\"stipend\":\"4\",\"study_enrolled_ids\":\"2\",\"notification\":\"some notification\",\"note\":\"Note for admin\",\"role_id\":\"10\",\"organization_detail_id\":\"10\",\"ecrf_id\":\"AFGD-D54\",\"timezone\":\"Pacific/Midway\"}","old_value":"null","operation":"CREATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-13 21:31:22"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-13 22:24:22"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-13 22:24:22"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","description":"New organization created","ip_address":"::1","method":"POST","new_value":"{\"first_name\":\"ASasdasd\",\"last_name\":\"asd\",\"middle_name\":\"\",\"email\":\"<EMAIL>\",\"status\":\"Screened\",\"gender\":\"male\",\"address\":\"vn\",\"contact_number\":\"10000000000\",\"date_of_birth\":\"01/01/1999\",\"stipend\":\"3\",\"study_enrolled_ids\":\"1\",\"notification\":\"some notification\",\"note\":\"Note for admin\",\"role_id\":\"10\",\"organization_detail_id\":\"1\",\"ecrf_id\":\"ACXV-XCV\",\"timezone\":\"Pacific/Honolulu\"}","old_value":"null","operation":"CREATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-13 22:24:57"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/428","browser":"Chrome 134.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-03-13 22:49:01"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-13 22:49:02"}
{"level":"info","message":{"api_url":"/schedule/createSchedule","browser":"Other 0.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-03-13 22:49:04"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-13 22:49:05"}
{"level":"info","message":{"api_url":"/schedule/createSchedule","browser":"Other 0.0.0","description":"Schedule created successfully for user 536","ip_address":"::1","method":"POST","new_value":"{}","old_value":"null","operation":"CREATE","table_name":"Schedule","user":"<EMAIL>"},"timestamp":"2025-03-13 22:49:14"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/428","browser":"Chrome 134.0.0","description":"Auto-schedule created for user 536","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":536,\"schedule\":{\"schedule_date\":\"2025-03-13\",\"schedule_time\":\"09:00\",\"study_enrolled_id\":\"1\",\"status\":\"Scheduled\",\"user_id\":536,\"note\":\"Auto-Created Schedule\"}}","old_value":"null","operation":"SCHEDULE_CREATED","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-03-13 22:49:17"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/428","browser":"Chrome 134.0.0","description":"Welcome email sent to ASasdasd asd (<EMAIL>)","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":536,\"email\":\"<EMAIL>\",\"subject\":\"Welcome to ResearchHero!\"}","old_value":"null","operation":"EMAIL_SENT","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-03-13 22:49:17"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/428","browser":"Chrome 134.0.0","description":"Registration status updated to Accepted for ID 428","ip_address":"::1","method":"PUT","new_value":"{\"id\":\"428\",\"status\":\"Accepted\",\"reason\":\"xcvxcxcvxcv\",\"user_id\":536}","old_value":"{}","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-03-13 22:49:18"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-13 23:58:56"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-13 23:58:57"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 134.0.0","description":"New organization created","ip_address":"::1","method":"POST","new_value":"{\"first_name\":\" zxc\",\"last_name\":\"zcx\",\"middle_name\":\"\",\"email\":\"<EMAIL>\",\"status\":\"Screened\",\"gender\":\"male\",\"address\":\"dxc\",\"contact_number\":\"13433333333\",\"date_of_birth\":\"01/01/1999\",\"stipend\":\"32\",\"study_enrolled_ids\":\"1\",\"notification\":\"some notification\",\"note\":\"Note for admin\",\"role_id\":\"10\",\"organization_detail_id\":\"1\",\"ecrf_id\":\"A454-SAD\",\"timezone\":\"Asia/Karachi\"}","old_value":"null","operation":"CREATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-13 23:59:23"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/429","browser":"Chrome 134.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-03-14 00:00:27"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-14 00:00:27"}
{"level":"info","message":{"api_url":"/schedule/createSchedule","browser":"Other 0.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-03-14 00:00:29"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-14 00:00:29"}
{"level":"info","message":{"api_url":"/schedule/createSchedule","browser":"Other 0.0.0","description":"Schedule created successfully for user 537","ip_address":"::1","method":"POST","new_value":"{}","old_value":"null","operation":"CREATE","table_name":"Schedule","user":"<EMAIL>"},"timestamp":"2025-03-14 00:00:36"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/429","browser":"Chrome 134.0.0","description":"Auto-schedule created for user 537","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":537,\"schedule\":{\"schedule_date\":\"2025-03-13\",\"schedule_time\":\"09:00\",\"study_enrolled_id\":\"1\",\"status\":\"Scheduled\",\"user_id\":537,\"note\":\"Auto-Created Schedule\"}}","old_value":"null","operation":"SCHEDULE_CREATED","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-03-14 00:00:38"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/429","browser":"Chrome 134.0.0","description":"Welcome email sent to  zxc zcx (<EMAIL>)","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":537,\"email\":\"<EMAIL>\",\"subject\":\"Welcome to ResearchHero!\"}","old_value":"null","operation":"EMAIL_SENT","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-03-14 00:00:39"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/429","browser":"Chrome 134.0.0","description":"Registration status updated to Accepted for ID 429","ip_address":"::1","method":"PUT","new_value":"{\"id\":\"429\",\"status\":\"Accepted\",\"reason\":\"Accepted!x\",\"user_id\":537}","old_value":"{}","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-03-14 00:00:39"}
