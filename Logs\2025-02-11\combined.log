{"level":"info","message":{"api_url":"/registration_approval/updateStatus/343","browser":"Chrome 133.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-02-11 13:26:49"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-11 13:26:50"}
{"level":"info","message":{"api_url":"/schedule/createSchedule","browser":"Other 0.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-02-11 13:26:52"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-11 13:26:52"}
{"level":"info","message":{"api_url":"/schedule/createSchedule","browser":"Other 0.0.0","description":"Schedule created successfully for user 450","ip_address":"::1","method":"POST","new_value":"{}","old_value":"null","operation":"CREATE","table_name":"Schedule","user":"<EMAIL>"},"timestamp":"2025-02-11 13:27:00"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/343","browser":"Chrome 133.0.0","description":"Auto-schedule created for user 450","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":450,\"schedule\":{\"schedule_date\":\"2025-02-11\",\"schedule_time\":\"09:00\",\"study_enrolled_id\":\"1\",\"status\":\"Scheduled\",\"user_id\":450,\"note\":\"Auto-Created Schedule\"}}","old_value":"null","operation":"SCHEDULE_CREATED","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-02-11 13:27:03"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/343","browser":"Chrome 133.0.0","description":"Welcome email sent to Dougaaaa Hofmiester (<EMAIL>)","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":450,\"email\":\"<EMAIL>\",\"subject\":\"Welcome to ResearchHero!\"}","old_value":"null","operation":"EMAIL_SENT","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-02-11 13:27:04"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/343","browser":"Chrome 133.0.0","description":"Registration status updated to Accepted for ID 343","ip_address":"::1","method":"PUT","new_value":"{\"id\":\"343\",\"status\":\"Accepted\",\"reason\":\"Accepted By Super Admin \",\"user_id\":450}","old_value":"{}","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-02-11 13:27:04"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/343","browser":"Chrome 133.0.0","description":"Error updating registration status: Cannot read properties of undefined (reading 'data')","ip_address":"::1","method":"PUT","new_value":"null","old_value":"{\"id\":\"343\",\"status\":\"Accepted\",\"reason\":\"Accepted By Super Admin \",\"user_id\":450}","operation":"UPDATE_ERROR","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-02-11 13:27:04"}
{"level":"error","message":"Failed to log to database or Excel: Corrupted zip or bug: expected 16 records in central dir, got 0","timestamp":"2025-02-11 13:27:05"}
{"level":"error","message":"Failed to log to database or Excel: Corrupted zip or bug: expected 16 records in central dir, got 0","timestamp":"2025-02-11 13:27:05"}
{"level":"error","message":"Failed to log to database or Excel: Corrupted zip or bug: expected 16 records in central dir, got 0","timestamp":"2025-02-11 13:27:05"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/343","browser":"Chrome 133.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-02-11 13:27:35"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-11 13:27:35"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/343","browser":"Chrome 133.0.0","description":"Welcome email sent to Dougaaaa Hofmiester (<EMAIL>)","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":450,\"email\":\"<EMAIL>\",\"subject\":\"Welcome to ResearchHero!\"}","old_value":"null","operation":"EMAIL_SENT","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-02-11 13:27:40"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/343","browser":"Chrome 133.0.0","description":"Registration status updated to Accepted for ID 343","ip_address":"::1","method":"PUT","new_value":"{\"id\":\"343\",\"status\":\"Accepted\",\"reason\":\"Accepted By Super Admin \",\"user_id\":450}","old_value":"{}","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-02-11 13:27:40"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/343","browser":"Chrome 133.0.0","description":"Error updating registration status: Cannot read properties of undefined (reading 'data')","ip_address":"::1","method":"PUT","new_value":"null","old_value":"{\"id\":\"343\",\"status\":\"Accepted\",\"reason\":\"Accepted By Super Admin \",\"user_id\":450}","operation":"UPDATE_ERROR","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-02-11 13:27:40"}
{"level":"error","message":"Failed to log to database or Excel: Corrupted zip or bug: expected 16 records in central dir, got 0","timestamp":"2025-02-11 13:27:41"}
{"level":"error","message":"Failed to log to database or Excel: Corrupted zip or bug: expected 16 records in central dir, got 0","timestamp":"2025-02-11 13:27:41"}
{"level":"error","message":"Failed to log to database or Excel: Corrupted zip or bug: expected 16 records in central dir, got 0","timestamp":"2025-02-11 13:27:41"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/263","browser":"Chrome 133.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-02-11 13:29:22"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-11 13:29:23"}
{"level":"info","message":{"api_url":"/schedule/createSchedule","browser":"Other 0.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-02-11 13:29:25"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-11 13:29:25"}
{"level":"info","message":{"api_url":"/schedule/createSchedule","browser":"Other 0.0.0","description":"Schedule created successfully for user 366","ip_address":"::1","method":"POST","new_value":"{}","old_value":"null","operation":"CREATE","table_name":"Schedule","user":"<EMAIL>"},"timestamp":"2025-02-11 13:29:31"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/263","browser":"Chrome 133.0.0","description":"Auto-schedule created for user 366","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":366,\"schedule\":{\"schedule_date\":\"2025-02-11\",\"schedule_time\":\"09:00\",\"study_enrolled_id\":\"2\",\"status\":\"Scheduled\",\"user_id\":366,\"note\":\"Auto-Created Schedule\"}}","old_value":"null","operation":"SCHEDULE_CREATED","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-02-11 13:29:33"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/263","browser":"Chrome 133.0.0","description":"Welcome email sent to StudyB Patient (<EMAIL>)","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":366,\"email\":\"<EMAIL>\",\"subject\":\"Welcome to ResearchHero!\"}","old_value":"null","operation":"EMAIL_SENT","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-02-11 13:29:34"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/263","browser":"Chrome 133.0.0","description":"Registration status updated to Accepted for ID 263","ip_address":"::1","method":"PUT","new_value":"{\"id\":\"263\",\"status\":\"Accepted\",\"reason\":\"Accepted By Super Admin \",\"user_id\":366}","old_value":"{}","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-02-11 13:29:34"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/263","browser":"Chrome 133.0.0","description":"Error updating registration status: schedule is not defined","ip_address":"::1","method":"PUT","new_value":"null","old_value":"{\"id\":\"263\",\"status\":\"Accepted\",\"reason\":\"Accepted By Super Admin \",\"user_id\":366}","operation":"UPDATE_ERROR","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-02-11 13:29:34"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/261","browser":"Chrome 133.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-02-11 13:34:58"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-11 13:34:58"}
{"level":"info","message":{"api_url":"/schedule/createSchedule","browser":"Other 0.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-02-11 13:35:00"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-11 13:35:01"}
{"level":"info","message":{"api_url":"/schedule/createSchedule","browser":"Other 0.0.0","description":"Schedule created successfully for user 365","ip_address":"::1","method":"POST","new_value":"{}","old_value":"null","operation":"CREATE","table_name":"Schedule","user":"<EMAIL>"},"timestamp":"2025-02-11 13:35:06"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/261","browser":"Chrome 133.0.0","description":"Auto-schedule created for user 365","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":365,\"schedule\":{\"schedule_date\":\"2025-02-11\",\"schedule_time\":\"09:00\",\"study_enrolled_id\":\"2\",\"status\":\"Scheduled\",\"user_id\":365,\"note\":\"Auto-Created Schedule\"}}","old_value":"null","operation":"SCHEDULE_CREATED","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-02-11 13:35:08"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/261","browser":"Chrome 133.0.0","description":"Welcome email sent to testsubject studyb (<EMAIL>)","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":365,\"email\":\"<EMAIL>\",\"subject\":\"Welcome to ResearchHero!\"}","old_value":"null","operation":"EMAIL_SENT","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-02-11 13:35:09"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/261","browser":"Chrome 133.0.0","description":"Registration status updated to Accepted for ID 261","ip_address":"::1","method":"PUT","new_value":"{\"id\":\"261\",\"status\":\"Accepted\",\"reason\":\"Accepted By Super Admin \",\"user_id\":365}","old_value":"{}","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-02-11 13:35:09"}
{"level":"error","message":"Failed to log to database or Excel: Corrupted zip or bug: expected 16 records in central dir, got 0","timestamp":"2025-02-11 13:35:10"}
{"level":"error","message":"Failed to log to database or Excel: Corrupted zip or bug: expected 16 records in central dir, got 0","timestamp":"2025-02-11 13:35:10"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/259","browser":"Chrome 133.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-02-11 13:37:47"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-11 13:37:47"}
{"level":"info","message":{"api_url":"/schedule/createSchedule","browser":"Other 0.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-02-11 13:37:49"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-11 13:37:50"}
{"level":"info","message":{"api_url":"/schedule/createSchedule","browser":"Other 0.0.0","description":"Schedule created successfully for user 363","ip_address":"::1","method":"POST","new_value":"{}","old_value":"null","operation":"CREATE","table_name":"Schedule","user":"<EMAIL>"},"timestamp":"2025-02-11 13:37:58"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/259","browser":"Chrome 133.0.0","description":"Auto-schedule created for user 363","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":363,\"schedule\":{\"schedule_date\":\"2025-02-11\",\"schedule_time\":\"09:00\",\"study_enrolled_id\":\"1\",\"status\":\"Scheduled\",\"user_id\":363,\"note\":\"Auto-Created Schedule\"}}","old_value":"null","operation":"SCHEDULE_CREATED","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-02-11 13:38:00"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/259","browser":"Chrome 133.0.0","description":"Welcome email sent to kevin anderson (<EMAIL>)","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":363,\"email\":\"<EMAIL>\",\"subject\":\"Welcome to ResearchHero!\"}","old_value":"null","operation":"EMAIL_SENT","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-02-11 13:38:01"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/259","browser":"Chrome 133.0.0","description":"Registration status updated to Accepted for ID 259","ip_address":"::1","method":"PUT","new_value":"{\"id\":\"259\",\"status\":\"Accepted\",\"reason\":\"Accepted By Super Admin \",\"user_id\":363}","old_value":"{}","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-02-11 13:38:01"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/343","browser":"Chrome 133.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-02-11 13:38:44"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-11 13:38:45"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/343","browser":"Chrome 133.0.0","description":"Registration status updated to Blocked for ID 343","ip_address":"::1","method":"PUT","new_value":"{\"id\":\"343\",\"status\":\"Blocked\",\"reason\":\"Accepted By Super Admin \",\"user_id\":450}","old_value":"{}","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-02-11 13:38:46"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/343","browser":"Chrome 133.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-02-11 13:39:19"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-11 13:39:20"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/343","browser":"Chrome 133.0.0","description":"Welcome email sent to Dougaaaa Hofmiester (<EMAIL>)","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":450,\"email\":\"<EMAIL>\",\"subject\":\"Welcome to ResearchHero!\"}","old_value":"null","operation":"EMAIL_SENT","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-02-11 13:39:24"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/343","browser":"Chrome 133.0.0","description":"Registration status updated to Accepted for ID 343","ip_address":"::1","method":"PUT","new_value":"{\"id\":\"343\",\"status\":\"Accepted\",\"reason\":\"Accepted By Super Admin \",\"user_id\":450}","old_value":"{}","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-02-11 13:39:24"}
