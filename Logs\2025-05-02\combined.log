{"level":"info","message":{"api_url":"/auth/signin","browser":"Other 0.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-05-02 16:08:15"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 135.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-05-02 21:51:28"}
{"level":"info","message":{"api_url":"/organization/createPersonnel","browser":"Chrome 135.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-05-02 21:58:33"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-05-02 21:58:33"}
{"level":"info","message":{"api_url":"/organization/createPersonnel","browser":"Chrome 135.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-05-02 21:58:46"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-05-02 21:58:46"}
{"level":"info","message":{"api_url":"/organization/createPersonnel","browser":"Chrome 135.0.0","description":"Personnel created with email: <EMAIL>","ip_address":"::1","method":"POST","new_value":"{\"userId\":613}","old_value":"null","operation":"CREATE","table_name":"personnel","user":"<EMAIL>"},"timestamp":"2025-05-02 21:59:04"}
{"level":"info","message":{"api_url":"/organization/createPersonnel","browser":"Chrome 135.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-05-02 22:01:37"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-05-02 22:01:37"}
{"level":"info","message":{"api_url":"/organization/createPersonnel","browser":"Chrome 135.0.0","description":"Personnel created with email: <EMAIL>","ip_address":"::1","method":"POST","new_value":"{\"userId\":615}","old_value":"null","operation":"CREATE","table_name":"personnel","user":"<EMAIL>"},"timestamp":"2025-05-02 22:02:07"}
{"level":"info","message":{"api_url":"/organization/createPersonnel","browser":"Chrome 135.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-05-02 22:04:18"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-05-02 22:04:19"}
{"level":"info","message":{"api_url":"/organization/createPersonnel","browser":"Chrome 135.0.0","description":"Personnel created with email: <EMAIL>","ip_address":"::1","method":"POST","new_value":"{\"userId\":616}","old_value":"null","operation":"CREATE","table_name":"personnel","user":"<EMAIL>"},"timestamp":"2025-05-02 22:04:54"}
