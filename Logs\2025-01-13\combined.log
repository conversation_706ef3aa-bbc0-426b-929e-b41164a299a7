{"level":"info","message":{"api_url":"/medicine/updateMedication/200","browser":"Chrome 131.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update medication","user":"<EMAIL>"},"timestamp":"2025-01-13 18:36:47"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-13 18:36:47"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/200","browser":"Chrome 131.0.0","description":"Medication log added - dosage_times, frequencyType, frequencyTime, frequencyCondition, dosageType, tracker_time, status updated","ip_address":"::1","method":"PUT","new_value":"{\"dosage_times\":[\"09:00 PM AM AM\"],\"frequencyType\":\"QD\",\"frequencyTime\":\"Afternoon\",\"frequencyCondition\":\"At Bedtime\",\"dosageType\":\"Tablet\",\"tracker_time\":\"2025-01-13T13:36:46.318Z\"}","old_value":"{\"dosageType\":\"Capsule\",\"status\":\"Randomized\"}","operation":"UPDATE","table_name":"Medication","user":"<EMAIL>"},"timestamp":"2025-01-13 18:36:48"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/200","browser":"Chrome 131.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update medication","user":"<EMAIL>"},"timestamp":"2025-01-13 18:38:53"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-13 18:38:53"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/200","browser":"Chrome 131.0.0","description":"Medication log added - dosage_times, frequencyType, frequencyTime, frequencyCondition, tracker_time, status updated","ip_address":"::1","method":"PUT","new_value":"{\"dosage_times\":[\"06:38 PM\"],\"frequencyType\":\"QD\",\"frequencyTime\":\"Afternoon\",\"frequencyCondition\":\"At Bedtime\",\"tracker_time\":\"2025-01-13T13:38:52.911Z\"}","old_value":"{\"status\":\"Randomized\"}","operation":"UPDATE","table_name":"Medication","user":"<EMAIL>"},"timestamp":"2025-01-13 18:38:55"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/200","browser":"Chrome 131.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update medication","user":"<EMAIL>"},"timestamp":"2025-01-13 18:47:03"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-13 18:47:03"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/200","browser":"Chrome 131.0.0","description":"Medication updated: frequencyType, frequencyTime, frequencyCondition, dosage_times, tracker_time","ip_address":"::1","method":"PUT","new_value":"{\"frequencyType\":\"QD\",\"frequencyTime\":\"Afternoon\",\"frequencyCondition\":\"At Bedtime\",\"dosage_times\":[\"06:46 PM\"],\"tracker_time\":\"2025-01-13T13:47:02.746Z\"}","old_value":"{}","operation":"UPDATE","table_name":"Medication","user":"<EMAIL>"},"timestamp":"2025-01-13 18:47:05"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/200","browser":"Chrome 131.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update medication","user":"<EMAIL>"},"timestamp":"2025-01-13 19:02:46"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-13 19:02:46"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/200","browser":"Chrome 131.0.0","description":"Medication updated: frequencyType, frequencyTime, frequencyCondition, dosageType, dosage_times, tracker_time","ip_address":"::1","method":"PUT","new_value":"{\"frequencyType\":\"QD\",\"frequencyTime\":\"Afternoon\",\"frequencyCondition\":\"At Bedtime\",\"dosageType\":\"Ointment\",\"dosage_times\":[\"07:02 AM\"],\"tracker_time\":\"2025-01-13T14:02:45.395Z\"}","old_value":"{\"dosageType\":\"Tablet\"}","operation":"UPDATE","table_name":"Medication","user":"<EMAIL>"},"timestamp":"2025-01-13 19:02:47"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/200","browser":"Chrome 131.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update medication","user":"<EMAIL>"},"timestamp":"2025-01-13 19:12:34"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-13 19:12:34"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/200","browser":"Chrome 131.0.0","description":"Error updating medication: Data truncated for column 'status' at row 1","ip_address":"::1","method":"PUT","new_value":"null","old_value":"{\"medication_id\":\"200\"}","operation":"UPDATE_ERROR","table_name":"Medication","user":"<EMAIL>"},"timestamp":"2025-01-13 19:12:35"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/200","browser":"Chrome 131.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update medication","user":"<EMAIL>"},"timestamp":"2025-01-13 20:20:11"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-13 20:20:12"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/200","browser":"Chrome 131.0.0","description":"Medication update: medication_name, dosage_times, frequencyType, frequencyTime, frequencyCondition, dosageType, tracker_time, status modified","ip_address":"::1","method":"PUT","new_value":"{\"medication_name\":\"Sunobinop Or Placeb\",\"dosage_times\":[\"07:02 AM AM\"],\"frequencyType\":\"QD\",\"frequencyTime\":\"Afternoon\",\"frequencyCondition\":\"At Bedtime\",\"dosageType\":\"Tablet\",\"tracker_time\":\"2025-01-13T15:20:11.143Z\"}","old_value":"{\"medication_name\":\"Sunobinop Or Placebo\",\"dosageType\":\"Ointment\",\"status\":\"Randomized\"}","operation":"UPDATE","table_name":"Medication","user":"<EMAIL>"},"timestamp":"2025-01-13 20:20:13"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/200","browser":"Chrome 131.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update medication","user":"<EMAIL>"},"timestamp":"2025-01-13 20:24:13"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-13 20:24:13"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/200","browser":"Chrome 131.0.0","description":"Medication changes: medication_id: 200 → undefined, created_at: Mon Jan 13 2025 12:21:37 GMT+0500 (Pakistan Standard Time) → undefined, medication_name: Sunobinop Or Placeb → Sunobinop Or Placebo, medication_status: Pending → undefined, dosage_time: 07:02 AM AM → undefined, frequency_type: QD → undefined, frequency_time: Afternoon → undefined, frequency_condition: At Bedtime → undefined, dosageType: Tablet → Capsule, date_of_birth: 06/10/1997 → undefined, gender: male → undefined, stipend: 1 → undefined, first_name: Arslan → undefined, last_name: Malik → undefined, address: 5959 Bonhomme Rd → undefined, contact_number: 12377292048 → undefined, study_enrolled_id: 1 → undefined, status: Randomized → undefined, user_id: 430 → undefined, email: <EMAIL> → undefined, study_name: SUN2003A - 102 → undefined, dosage_times: undefined → [\"07:02 AM AM AM\"], frequencyType: undefined → QD, frequencyTime: undefined → Afternoon, frequencyCondition: undefined → At Bedtime, tracker_time: undefined → 2025-01-13T15:24:12.493Z","ip_address":"::1","method":"PUT","new_value":"{\"medication_name\":\"Sunobinop Or Placebo\",\"dosageType\":\"Capsule\",\"dosage_times\":[\"07:02 AM AM AM\"],\"frequencyType\":\"QD\",\"frequencyTime\":\"Afternoon\",\"frequencyCondition\":\"At Bedtime\",\"tracker_time\":\"2025-01-13T15:24:12.493Z\"}","old_value":"{\"medication_id\":200,\"created_at\":\"2025-01-13T07:21:37.000Z\",\"medication_name\":\"Sunobinop Or Placeb\",\"medication_status\":\"Pending\",\"dosage_time\":\"07:02 AM AM\",\"frequency_type\":\"QD\",\"frequency_time\":\"Afternoon\",\"frequency_condition\":\"At Bedtime\",\"dosageType\":\"Tablet\",\"date_of_birth\":\"06/10/1997\",\"gender\":\"male\",\"stipend\":\"1\",\"first_name\":\"Arslan\",\"last_name\":\"Malik\",\"address\":\"5959 Bonhomme Rd\",\"contact_number\":\"12377292048\",\"study_enrolled_id\":\"1\",\"status\":\"Randomized\",\"user_id\":430,\"email\":\"<EMAIL>\",\"study_name\":\"SUN2003A - 102\"}","operation":"UPDATE","table_name":"Medication","user":"<EMAIL>"},"timestamp":"2025-01-13 20:24:14"}
{"level":"error","message":"Failed to log to database: Data too long for column 'description' at row 1","timestamp":"2025-01-13 20:24:15"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/200","browser":"Chrome 131.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update medication","user":"<EMAIL>"},"timestamp":"2025-01-13 20:25:51"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-13 20:25:51"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/200","browser":"Chrome 131.0.0","description":"Medication changes: medication_id: 200 → undefined, created_at: Mon Jan 13 2025 12:21:37 GMT+0500 (Pakistan Standard Time) → undefined, medication_name: Sunobinop Or Placebo → Sunobinop Or Placeb, medication_status: Pending → undefined, dosage_time: 07:02 AM AM AM → undefined, frequency_type: QD → undefined, frequency_time: Afternoon → undefined, frequency_condition: At Bedtime → undefined, dosageType: Capsule → Tablet, date_of_birth: 06/10/1997 → undefined, gender: male → undefined, stipend: 1 → undefined, first_name: Arslan → undefined, last_name: Malik → undefined, address: 5959 Bonhomme Rd → undefined, contact_number: 12377292048 → undefined, study_enrolled_id: 1 → undefined, status: Randomized → undefined, user_id: 430 → undefined, email: <EMAIL> → undefined, study_name: SUN2003A - 102 → undefined, dosage_times: undefined → [\"07:02 AM AM AM AM\"], frequencyType: undefined → QD, frequencyTime: undefined → Afternoon, frequencyCondition: undefined → At Bedtime, tracker_time: undefined → 2025-01-13T15:25:50.540Z","ip_address":"::1","method":"PUT","new_value":"{\"medication_name\":\"Sunobinop Or Placeb\",\"dosageType\":\"Tablet\",\"dosage_times\":[\"07:02 AM AM AM AM\"],\"frequencyType\":\"QD\",\"frequencyTime\":\"Afternoon\",\"frequencyCondition\":\"At Bedtime\",\"tracker_time\":\"2025-01-13T15:25:50.540Z\"}","old_value":"{\"medication_id\":200,\"created_at\":\"2025-01-13T07:21:37.000Z\",\"medication_name\":\"Sunobinop Or Placebo\",\"medication_status\":\"Pending\",\"dosage_time\":\"07:02 AM AM AM\",\"frequency_type\":\"QD\",\"frequency_time\":\"Afternoon\",\"frequency_condition\":\"At Bedtime\",\"dosageType\":\"Capsule\",\"date_of_birth\":\"06/10/1997\",\"gender\":\"male\",\"stipend\":\"1\",\"first_name\":\"Arslan\",\"last_name\":\"Malik\",\"address\":\"5959 Bonhomme Rd\",\"contact_number\":\"12377292048\",\"study_enrolled_id\":\"1\",\"status\":\"Randomized\",\"user_id\":430,\"email\":\"<EMAIL>\",\"study_name\":\"SUN2003A - 102\"}","operation":"UPDATE","table_name":"Medication","user":"<EMAIL>"},"timestamp":"2025-01-13 20:25:53"}
{"level":"error","message":"Failed to log to database: Data too long for column 'description' at row 1","timestamp":"2025-01-13 20:25:53"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/200","browser":"Chrome 131.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update medication","user":"<EMAIL>"},"timestamp":"2025-01-13 20:28:31"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-13 20:28:31"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/200","browser":"Chrome 131.0.0","description":"Updated fields: medication_name, dosageType","ip_address":"::1","method":"PUT","new_value":"{\"medication_name\":\"Sunobinop Or Placebo\",\"dosageType\":\"Capsule\"}","old_value":"{\"medication_name\":\"Sunobinop Or Placeb\",\"dosageType\":\"Tablet\"}","operation":"UPDATE","table_name":"Medication","user":"<EMAIL>"},"timestamp":"2025-01-13 20:28:33"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/200","browser":"Chrome 131.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update medication","user":"<EMAIL>"},"timestamp":"2025-01-13 20:29:15"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-13 20:29:15"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/200","browser":"Chrome 131.0.0","description":"Updated fields: dosageType, allot_medicine","ip_address":"::1","method":"PUT","new_value":"{\"dosageType\":\"Tablet\",\"allot_medicine\":\"2\"}","old_value":"{\"dosageType\":\"Capsule\",\"allot_medicine\":\"1\"}","operation":"UPDATE","table_name":"Medication","user":"<EMAIL>"},"timestamp":"2025-01-13 20:29:17"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/200","browser":"Chrome 131.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update medication","user":"<EMAIL>"},"timestamp":"2025-01-13 20:30:12"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-13 20:30:12"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/200","browser":"Chrome 131.0.0","description":"Updated fields: medication_name, dosageType, route","ip_address":"::1","method":"PUT","new_value":"{\"medication_name\":\"Sunobinop Or Placeb\",\"dosageType\":\"Injection\",\"route\":\"Parenteral\"}","old_value":"{\"medication_name\":\"Sunobinop Or Placebo\",\"dosageType\":\"Tablet\",\"route\":\"Oral\"}","operation":"UPDATE","table_name":"Medication","user":"<EMAIL>"},"timestamp":"2025-01-13 20:30:13"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/200","browser":"Chrome 131.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update medication","user":"<EMAIL>"},"timestamp":"2025-01-13 20:43:22"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-13 20:43:22"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/200","browser":"Chrome 131.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update medication","user":"<EMAIL>"},"timestamp":"2025-01-13 20:44:09"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-13 20:44:09"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/200","browser":"Chrome 131.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update medication","user":"<EMAIL>"},"timestamp":"2025-01-13 20:45:03"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-13 20:45:03"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/200","browser":"Chrome 131.0.0","description":"Updated fields: medication_name, dosageType","ip_address":"::1","method":"PUT","new_value":"{\"medication_name\":\"Sunobinop Or Placebo\",\"dosageType\":\"Tablet\"}","old_value":"{\"medication_name\":\"Sunobinop Or Placeb\",\"dosageType\":\"Injection\"}","operation":"UPDATE","table_name":"Medication","user":"<EMAIL>"},"timestamp":"2025-01-13 20:45:05"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/200","browser":"Chrome 131.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update medication","user":"<EMAIL>"},"timestamp":"2025-01-13 20:45:29"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-13 20:45:30"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/200","browser":"Chrome 131.0.0","description":"Updated fields: dosage","ip_address":"::1","method":"PUT","new_value":"{\"dosage\":\"0.5mg or 1.0mg or 2.0mgs\"}","old_value":"{\"dosage\":\"0.5mg or 1.0mg or 2.0mg\"}","operation":"UPDATE","table_name":"Medication","user":"<EMAIL>"},"timestamp":"2025-01-13 20:45:31"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/200","browser":"Chrome 131.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update medication","user":"<EMAIL>"},"timestamp":"2025-01-13 20:45:59"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-13 20:45:59"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/200","browser":"Chrome 131.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update medication","user":"<EMAIL>"},"timestamp":"2025-01-13 20:46:42"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-13 20:46:43"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/200","browser":"Chrome 131.0.0","description":"Updated fields: route","ip_address":"::1","method":"PUT","new_value":"{\"route\":\"Oral\"}","old_value":"{\"route\":\"Parenteral\"}","operation":"UPDATE","table_name":"Medication","user":"<EMAIL>"},"timestamp":"2025-01-13 20:46:44"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/200","browser":"Chrome 131.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update medication","user":"<EMAIL>"},"timestamp":"2025-01-13 20:47:08"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-13 20:47:09"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/200","browser":"Chrome 131.0.0","description":"Updated fields: note","ip_address":"::1","method":"PUT","new_value":"{\"note\":\"Auto-created medicin\"}","old_value":"{\"note\":\"Auto-created medicine\"}","operation":"UPDATE","table_name":"Medication","user":"<EMAIL>"},"timestamp":"2025-01-13 20:47:10"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/200","browser":"Chrome 131.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update medication","user":"<EMAIL>"},"timestamp":"2025-01-13 20:48:13"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-13 20:48:13"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/200","browser":"Chrome 131.0.0","description":"Updated fields: allot_medicine","ip_address":"::1","method":"PUT","new_value":"{\"allot_medicine\":\"1\"}","old_value":"{\"allot_medicine\":\"2\"}","operation":"UPDATE","table_name":"Medication","user":"<EMAIL>"},"timestamp":"2025-01-13 20:48:15"}
{"level":"info","message":{"api_url":"/organization/updateOrganizationDetails","browser":"Chrome 131.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"organization details","user":"<EMAIL>"},"timestamp":"2025-01-13 20:51:59"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-13 20:51:59"}
{"level":"info","message":{"api_url":"/organization/updateOrganizationDetails","browser":"Chrome 131.0.0","description":"Site details updated","ip_address":"::1","method":"PUT","new_value":"{\"organization_detail_id\":1,\"organization_address\":\"7080 Southwest Freeway Houston TX 770740\"}","old_value":"[{\"organization_detail_id\":1,\"organization_name\":\"Dr Prabhu Manjeshwar\",\"organization_address\":\"7080 Southwest Freeway Houston TX 77074\"}]","operation":"UPDATE","table_name":"Site","user":"<EMAIL>"},"timestamp":"2025-01-13 20:52:00"}
{"level":"info","message":{"api_url":"/organization/logout","browser":"Chrome 131.0.0","description":"User logged out successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"LOGOUT","table_name":"N/A","user":"<EMAIL>"},"timestamp":"2025-01-13 20:52:54"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 131.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-01-13 20:53:00"}
{"level":"info","message":{"api_url":"/organization/updateOrganizationDetails","browser":"Chrome 131.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"organization details","user":"<EMAIL>"},"timestamp":"2025-01-13 20:53:15"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-13 20:53:16"}
{"level":"info","message":{"api_url":"/organization/updateOrganizationDetails","browser":"Chrome 131.0.0","description":"Site details updated","ip_address":"::1","method":"PUT","new_value":"{\"organization_detail_id\":1,\"organization_address\":\"7080 Southwest Freeway Houston TX 77074\"}","old_value":"[{\"organization_detail_id\":1,\"organization_name\":\"Dr Prabhu Manjeshwar\",\"organization_address\":\"7080 Southwest Freeway Houston TX 770740\"}]","operation":"UPDATE","table_name":"Site","user":"<EMAIL>"},"timestamp":"2025-01-13 20:53:16"}
{"level":"info","message":{"api_url":"/organization/updateOrganizationDetails","browser":"Chrome 131.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"organization details","user":"<EMAIL>"},"timestamp":"2025-01-13 20:57:24"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-13 20:57:24"}
