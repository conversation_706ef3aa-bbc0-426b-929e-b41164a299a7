{"level":"error","message":"Authorization token is missing","timestamp":"2025-01-29 21:22:14"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 132.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-01-29 21:22:39"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 132.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"h<PERSON><PERSON><EMAIL>"},"timestamp":"2025-01-29 21:53:22"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 132.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-01-30 00:13:32"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 132.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-01-30 00:16:37"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-30 00:16:37"}
{"level":"info","message":{"api_url":"/organization/createOrganization","browser":"Chrome 132.0.0","description":"New organization created","ip_address":"::1","method":"POST","new_value":"{\"first_name\":\"margin\",\"last_name\":\"bottom\",\"middle_name\":\"\",\"email\":\"<EMAIL>\",\"status\":\"Screened\",\"gender\":\"male\",\"address\":\"5959 Bonhomme Rd\",\"contact_number\":\"12377292048\",\"date_of_birth\":\"01/27/1992\",\"stipend\":\"4\",\"study_enrolled_ids\":\"1\",\"notification\":\"some notification\",\"note\":\"Note for admin\",\"role_id\":\"10\",\"organization_detail_id\":\"1\",\"ecrf_id\":\"A101-789\"}","old_value":"null","operation":"CREATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-01-30 00:16:40"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 132.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-01-30 00:16:56"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/335","browser":"Chrome 132.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-01-30 00:17:24"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-30 00:17:24"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/335","browser":"Chrome 132.0.0","description":"Welcome email sent to da5c289f4a359c63e1b6510959eb11f5:9f88e82bee7e7eb425440db1393b8e28 535ca503f350c09ede9cabaab30f7876:4c45a687b1db118cc530560ecb28ac42 (<EMAIL>)","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":438,\"email\":\"<EMAIL>\",\"subject\":\"Welcome to ResearchHero!\"}","old_value":"null","operation":"EMAIL_SENT","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-01-30 00:17:27"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/335","browser":"Chrome 132.0.0","description":"Registration status updated to Accepted for ID 335","ip_address":"::1","method":"PUT","new_value":"{\"id\":\"335\",\"status\":\"Accepted\",\"reason\":\"Accepted By Super Admin \",\"user_id\":438}","old_value":"[{\"account_status_id\":335,\"user_id\":438,\"account_status\":\"Pending\",\"reason\":\"Initial registration\",\"first_time\":\"1\",\"updated_at\":\"2025-01-30T06:16:40.000Z\"}]","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-01-30 00:17:28"}
{"level":"info","message":{"api_url":"/schedule/createSchedule","browser":"Chrome 132.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-01-30 00:17:28"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-30 00:17:28"}
{"level":"info","message":{"api_url":"/schedule/createSchedule","browser":"Chrome 132.0.0","description":"Schedule created successfully for user 438","ip_address":"::1","method":"POST","new_value":"{}","old_value":"null","operation":"CREATE","table_name":"Schedule","user":"<EMAIL>"},"timestamp":"2025-01-30 00:17:37"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 132.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-01-30 00:17:43"}
{"level":"info","message":{"api_url":"/app_survey/submitscalequestionresponse","browser":"Chrome 132.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"submit survey response","user":"<EMAIL>"},"timestamp":"2025-01-30 00:19:27"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-30 00:19:27"}
