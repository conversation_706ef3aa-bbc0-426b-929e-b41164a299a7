import type { FromSchema } from 'json-schema-to-ts';
import * as schemas from './schemas';
export type DeleteApiV1ApiApplicationsIdMetadataParam = FromSchema<typeof schemas.DeleteApiV1ApiApplicationsId.metadata>;
export type DeleteApiV1ApiApplicationsIdResponse404 = FromSchema<typeof schemas.DeleteApiV1ApiApplicationsId.response['404']>;
export type DeleteApiV1DocumentTemplatesIdMetadataParam = FromSchema<typeof schemas.DeleteApiV1DocumentTemplatesId.metadata>;
export type DeleteApiV1DocumentTemplatesIdResponse404 = FromSchema<typeof schemas.DeleteApiV1DocumentTemplatesId.response['404']>;
export type DeleteApiV1DocumentsIdMetadataParam = FromSchema<typeof schemas.DeleteApiV1DocumentsId.metadata>;
export type DeleteApiV1DocumentsIdResponse404 = FromSchema<typeof schemas.DeleteApiV1DocumentsId.response['404']>;
export type DeleteApiV1HooksIdMetadataParam = FromSchema<typeof schemas.DeleteApiV1HooksId.metadata>;
export type DeleteApiV1HooksIdResponse404 = FromSchema<typeof schemas.DeleteApiV1HooksId.response['404']>;
export type GetApiV1ApiApplicationsIdMetadataParam = FromSchema<typeof schemas.GetApiV1ApiApplicationsId.metadata>;
export type GetApiV1ApiApplicationsIdResponse200 = FromSchema<typeof schemas.GetApiV1ApiApplicationsId.response['200']>;
export type GetApiV1ApiApplicationsIdResponse404 = FromSchema<typeof schemas.GetApiV1ApiApplicationsId.response['404']>;
export type GetApiV1BulkSendsCsvTemplateMetadataParam = FromSchema<typeof schemas.GetApiV1BulkSendsCsvTemplate.metadata>;
export type GetApiV1BulkSendsCsvTemplateResponse200 = FromSchema<typeof schemas.GetApiV1BulkSendsCsvTemplate.response['200']>;
export type GetApiV1BulkSendsCsvTemplateResponse401 = FromSchema<typeof schemas.GetApiV1BulkSendsCsvTemplate.response['401']>;
export type GetApiV1BulkSendsCsvTemplateResponse404 = FromSchema<typeof schemas.GetApiV1BulkSendsCsvTemplate.response['404']>;
export type GetApiV1BulkSendsIdDocumentsMetadataParam = FromSchema<typeof schemas.GetApiV1BulkSendsIdDocuments.metadata>;
export type GetApiV1BulkSendsIdDocumentsResponse200 = FromSchema<typeof schemas.GetApiV1BulkSendsIdDocuments.response['200']>;
export type GetApiV1BulkSendsIdDocumentsResponse401 = FromSchema<typeof schemas.GetApiV1BulkSendsIdDocuments.response['401']>;
export type GetApiV1BulkSendsIdDocumentsResponse404 = FromSchema<typeof schemas.GetApiV1BulkSendsIdDocuments.response['404']>;
export type GetApiV1BulkSendsIdMetadataParam = FromSchema<typeof schemas.GetApiV1BulkSendsId.metadata>;
export type GetApiV1BulkSendsIdResponse200 = FromSchema<typeof schemas.GetApiV1BulkSendsId.response['200']>;
export type GetApiV1BulkSendsIdResponse401 = FromSchema<typeof schemas.GetApiV1BulkSendsId.response['401']>;
export type GetApiV1BulkSendsIdResponse404 = FromSchema<typeof schemas.GetApiV1BulkSendsId.response['404']>;
export type GetApiV1BulkSendsMetadataParam = FromSchema<typeof schemas.GetApiV1BulkSends.metadata>;
export type GetApiV1BulkSendsResponse200 = FromSchema<typeof schemas.GetApiV1BulkSends.response['200']>;
export type GetApiV1BulkSendsResponse401 = FromSchema<typeof schemas.GetApiV1BulkSends.response['401']>;
export type GetApiV1DocumentTemplatesIdMetadataParam = FromSchema<typeof schemas.GetApiV1DocumentTemplatesId.metadata>;
export type GetApiV1DocumentTemplatesIdResponse200 = FromSchema<typeof schemas.GetApiV1DocumentTemplatesId.response['200']>;
export type GetApiV1DocumentTemplatesIdResponse404 = FromSchema<typeof schemas.GetApiV1DocumentTemplatesId.response['404']>;
export type GetApiV1DocumentsIdCompletedPdfMetadataParam = FromSchema<typeof schemas.GetApiV1DocumentsIdCompletedPdf.metadata>;
export type GetApiV1DocumentsIdCompletedPdfResponse200 = FromSchema<typeof schemas.GetApiV1DocumentsIdCompletedPdf.response['200']>;
export type GetApiV1DocumentsIdCompletedPdfResponse404 = FromSchema<typeof schemas.GetApiV1DocumentsIdCompletedPdf.response['404']>;
export type GetApiV1DocumentsIdMetadataParam = FromSchema<typeof schemas.GetApiV1DocumentsId.metadata>;
export type GetApiV1DocumentsIdResponse200 = FromSchema<typeof schemas.GetApiV1DocumentsId.response['200']>;
export type GetApiV1DocumentsIdResponse404 = FromSchema<typeof schemas.GetApiV1DocumentsId.response['404']>;
export type GetApiV1HooksResponse200 = FromSchema<typeof schemas.GetApiV1Hooks.response['200']>;
export type GetApiV1MeResponse200 = FromSchema<typeof schemas.GetApiV1Me.response['200']>;
export type GetApiV1MeResponse401 = FromSchema<typeof schemas.GetApiV1Me.response['401']>;
export type PostApiV1BulkSendsBodyParam = FromSchema<typeof schemas.PostApiV1BulkSends.body>;
export type PostApiV1BulkSendsResponse201 = FromSchema<typeof schemas.PostApiV1BulkSends.response['201']>;
export type PostApiV1BulkSendsResponse401 = FromSchema<typeof schemas.PostApiV1BulkSends.response['401']>;
export type PostApiV1BulkSendsResponse422 = FromSchema<typeof schemas.PostApiV1BulkSends.response['422']>;
export type PostApiV1BulkSendsValidateCsvBodyParam = FromSchema<typeof schemas.PostApiV1BulkSendsValidateCsv.body>;
export type PostApiV1BulkSendsValidateCsvResponse200 = FromSchema<typeof schemas.PostApiV1BulkSendsValidateCsv.response['200']>;
export type PostApiV1BulkSendsValidateCsvResponse401 = FromSchema<typeof schemas.PostApiV1BulkSendsValidateCsv.response['401']>;
export type PostApiV1BulkSendsValidateCsvResponse422 = FromSchema<typeof schemas.PostApiV1BulkSendsValidateCsv.response['422']>;
export type PostApiV1DocumentTemplatesBodyParam = FromSchema<typeof schemas.PostApiV1DocumentTemplates.body>;
export type PostApiV1DocumentTemplatesDocumentsBodyParam = FromSchema<typeof schemas.PostApiV1DocumentTemplatesDocuments.body>;
export type PostApiV1DocumentTemplatesDocumentsResponse201 = FromSchema<typeof schemas.PostApiV1DocumentTemplatesDocuments.response['201']>;
export type PostApiV1DocumentTemplatesDocumentsResponse400 = FromSchema<typeof schemas.PostApiV1DocumentTemplatesDocuments.response['400']>;
export type PostApiV1DocumentTemplatesDocumentsResponse422 = FromSchema<typeof schemas.PostApiV1DocumentTemplatesDocuments.response['422']>;
export type PostApiV1DocumentTemplatesResponse201 = FromSchema<typeof schemas.PostApiV1DocumentTemplates.response['201']>;
export type PostApiV1DocumentTemplatesResponse400 = FromSchema<typeof schemas.PostApiV1DocumentTemplates.response['400']>;
export type PostApiV1DocumentTemplatesResponse422 = FromSchema<typeof schemas.PostApiV1DocumentTemplates.response['422']>;
export type PostApiV1DocumentsBodyParam = FromSchema<typeof schemas.PostApiV1Documents.body>;
export type PostApiV1DocumentsIdRemindBodyParam = FromSchema<typeof schemas.PostApiV1DocumentsIdRemind.body>;
export type PostApiV1DocumentsIdRemindMetadataParam = FromSchema<typeof schemas.PostApiV1DocumentsIdRemind.metadata>;
export type PostApiV1DocumentsIdRemindResponse201 = FromSchema<typeof schemas.PostApiV1DocumentsIdRemind.response['201']>;
export type PostApiV1DocumentsIdRemindResponse404 = FromSchema<typeof schemas.PostApiV1DocumentsIdRemind.response['404']>;
export type PostApiV1DocumentsIdRemindResponse422 = FromSchema<typeof schemas.PostApiV1DocumentsIdRemind.response['422']>;
export type PostApiV1DocumentsIdSendBodyParam = FromSchema<typeof schemas.PostApiV1DocumentsIdSend.body>;
export type PostApiV1DocumentsIdSendMetadataParam = FromSchema<typeof schemas.PostApiV1DocumentsIdSend.metadata>;
export type PostApiV1DocumentsIdSendResponse201 = FromSchema<typeof schemas.PostApiV1DocumentsIdSend.response['201']>;
export type PostApiV1DocumentsIdSendResponse422 = FromSchema<typeof schemas.PostApiV1DocumentsIdSend.response['422']>;
export type PostApiV1DocumentsResponse201 = FromSchema<typeof schemas.PostApiV1Documents.response['201']>;
export type PostApiV1DocumentsResponse400 = FromSchema<typeof schemas.PostApiV1Documents.response['400']>;
export type PostApiV1DocumentsResponse422 = FromSchema<typeof schemas.PostApiV1Documents.response['422']>;
export type PostApiV1HooksBodyParam = FromSchema<typeof schemas.PostApiV1Hooks.body>;
export type PostApiV1HooksResponse201 = FromSchema<typeof schemas.PostApiV1Hooks.response['201']>;
export type PostApiV1HooksResponse400 = FromSchema<typeof schemas.PostApiV1Hooks.response['400']>;
export type PutApiV1DocumentTemplatesIdBodyParam = FromSchema<typeof schemas.PutApiV1DocumentTemplatesId.body>;
export type PutApiV1DocumentTemplatesIdMetadataParam = FromSchema<typeof schemas.PutApiV1DocumentTemplatesId.metadata>;
export type PutApiV1DocumentTemplatesIdResponse200 = FromSchema<typeof schemas.PutApiV1DocumentTemplatesId.response['200']>;
export type PutApiV1DocumentTemplatesIdResponse400 = FromSchema<typeof schemas.PutApiV1DocumentTemplatesId.response['400']>;
export type PutApiV1DocumentTemplatesIdResponse422 = FromSchema<typeof schemas.PutApiV1DocumentTemplatesId.response['422']>;
