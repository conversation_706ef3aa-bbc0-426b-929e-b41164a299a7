{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 134.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-25 21:32:42"}
{"level":"info","message":{"api_url":"/organization/createPersonnel","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-25 21:35:15"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-25 21:35:15"}
{"level":"info","message":{"api_url":"/organization/createPersonnel","browser":"Chrome 134.0.0","description":"Personnel created with email: <EMAIL>","ip_address":"::1","method":"POST","new_value":"{\"userId\":589}","old_value":"null","operation":"CREATE","table_name":"personnel","user":"<EMAIL>"},"timestamp":"2025-03-25 21:35:17"}
{"level":"info","message":{"api_url":"/organization/createPersonnel","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-25 21:36:02"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-25 21:36:02"}
{"level":"info","message":{"api_url":"/organization/createPersonnel","browser":"Chrome 134.0.0","description":"Personnel created with email: <EMAIL>","ip_address":"::1","method":"POST","new_value":"{\"userId\":590}","old_value":"null","operation":"CREATE","table_name":"personnel","user":"<EMAIL>"},"timestamp":"2025-03-25 21:36:05"}
{"level":"info","message":{"api_url":"/organization/createPersonnel","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-25 21:37:07"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-25 21:37:07"}
{"level":"info","message":{"api_url":"/organization/createPersonnel","browser":"Chrome 134.0.0","description":"Personnel created with email: <EMAIL>","ip_address":"::1","method":"POST","new_value":"{\"userId\":591}","old_value":"null","operation":"CREATE","table_name":"personnel","user":"<EMAIL>"},"timestamp":"2025-03-25 21:37:10"}
{"level":"info","message":{"api_url":"/organization/createPersonnel","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-25 21:37:50"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-25 21:37:51"}
{"level":"info","message":{"api_url":"/organization/createPersonnel","browser":"Chrome 134.0.0","description":"Personnel created with email: <EMAIL>","ip_address":"::1","method":"POST","new_value":"{\"userId\":592}","old_value":"null","operation":"CREATE","table_name":"personnel","user":"<EMAIL>"},"timestamp":"2025-03-25 21:37:53"}
{"level":"info","message":{"api_url":"/organization/createPersonnel","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-25 21:37:55"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-25 21:37:56"}
{"level":"info","message":{"api_url":"/organization/createPersonnel","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-25 21:38:37"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-25 21:38:38"}
{"level":"info","message":{"api_url":"/organization/createPersonnel","browser":"Chrome 134.0.0","description":"Personnel created with email: <EMAIL>","ip_address":"::1","method":"POST","new_value":"{\"userId\":594}","old_value":"null","operation":"CREATE","table_name":"personnel","user":"<EMAIL>"},"timestamp":"2025-03-25 21:38:40"}
{"level":"info","message":{"api_url":"/organization/createPersonnel","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-25 21:39:40"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-25 21:39:40"}
{"level":"info","message":{"api_url":"/organization/createPersonnel","browser":"Chrome 134.0.0","description":"Personnel created with email: <EMAIL>","ip_address":"::1","method":"POST","new_value":"{\"userId\":595}","old_value":"null","operation":"CREATE","table_name":"personnel","user":"<EMAIL>"},"timestamp":"2025-03-25 21:39:43"}
{"level":"info","message":{"api_url":"/organization/createPersonnel","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-25 21:40:24"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-25 21:40:25"}
{"level":"info","message":{"api_url":"/organization/createPersonnel","browser":"Chrome 134.0.0","description":"Personnel created with email: <EMAIL>","ip_address":"::1","method":"POST","new_value":"{\"userId\":596}","old_value":"null","operation":"CREATE","table_name":"personnel","user":"<EMAIL>"},"timestamp":"2025-03-25 21:40:27"}
{"level":"info","message":{"api_url":"/organization/createPersonnel","browser":"Chrome 134.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-25 21:41:06"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-25 21:41:06"}
{"level":"info","message":{"api_url":"/organization/createPersonnel","browser":"Chrome 134.0.0","description":"Personnel created with email: <EMAIL>","ip_address":"::1","method":"POST","new_value":"{\"userId\":597}","old_value":"null","operation":"CREATE","table_name":"personnel","user":"<EMAIL>"},"timestamp":"2025-03-25 21:41:09"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/477","browser":"Chrome 134.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-03-25 21:42:01"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-25 21:42:02"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/477","browser":"Chrome 134.0.0","description":"Welcome email sent to it admin (<EMAIL>)","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":597,\"email\":\"<EMAIL>\",\"subject\":\"Welcome to ResearchHero!\"}","old_value":"null","operation":"EMAIL_SENT","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-03-25 21:42:06"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/477","browser":"Chrome 134.0.0","description":"Registration status updated to Accepted for ID 477","ip_address":"::1","method":"PUT","new_value":"{\"id\":\"477\",\"status\":\"Accepted\",\"reason\":\"Accepted By Super Admin \",\"user_id\":597}","old_value":"{}","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-03-25 21:42:06"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/476","browser":"Chrome 134.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-03-25 21:42:16"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-25 21:42:16"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/476","browser":"Chrome 134.0.0","description":"Welcome email sent to site manager (<EMAIL>)","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":596,\"email\":\"<EMAIL>\",\"subject\":\"Welcome to ResearchHero!\"}","old_value":"null","operation":"EMAIL_SENT","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-03-25 21:42:21"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/476","browser":"Chrome 134.0.0","description":"Registration status updated to Accepted for ID 476","ip_address":"::1","method":"PUT","new_value":"{\"id\":\"476\",\"status\":\"Accepted\",\"reason\":\"Accepted By Super Admin \",\"user_id\":596}","old_value":"{}","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-03-25 21:42:21"}
{"level":"error","message":"Failed to log to database or Excel: Corrupted zip or bug: expected 16 records in central dir, got 0","timestamp":"2025-03-25 21:42:21"}
{"level":"error","message":"Failed to log to database or Excel: Corrupted zip or bug: expected 16 records in central dir, got 0","timestamp":"2025-03-25 21:42:22"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/475","browser":"Chrome 134.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-03-25 21:42:32"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-25 21:42:32"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/475","browser":"Chrome 134.0.0","description":"Welcome email sent to csv role (<EMAIL>)","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":595,\"email\":\"<EMAIL>\",\"subject\":\"Welcome to ResearchHero!\"}","old_value":"null","operation":"EMAIL_SENT","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-03-25 21:42:37"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/475","browser":"Chrome 134.0.0","description":"Registration status updated to Accepted for ID 475","ip_address":"::1","method":"PUT","new_value":"{\"id\":\"475\",\"status\":\"Accepted\",\"reason\":\"Accepted By Super Admin \",\"user_id\":595}","old_value":"{}","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-03-25 21:42:37"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/474","browser":"Chrome 134.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-03-25 21:42:49"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-25 21:42:49"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/474","browser":"Chrome 134.0.0","description":"Welcome email sent to saftey responsible (<EMAIL>)","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":594,\"email\":\"<EMAIL>\",\"subject\":\"Welcome to ResearchHero!\"}","old_value":"null","operation":"EMAIL_SENT","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-03-25 21:42:54"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/474","browser":"Chrome 134.0.0","description":"Registration status updated to Accepted for ID 474","ip_address":"::1","method":"PUT","new_value":"{\"id\":\"474\",\"status\":\"Accepted\",\"reason\":\"Accepted By Super Admin \",\"user_id\":594}","old_value":"{}","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-03-25 21:42:54"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/473","browser":"Chrome 134.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-03-25 21:43:05"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-25 21:43:05"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/473","browser":"Chrome 134.0.0","description":"Welcome email sent to sub pi (<EMAIL>)","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":592,\"email\":\"<EMAIL>\",\"subject\":\"Welcome to ResearchHero!\"}","old_value":"null","operation":"EMAIL_SENT","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-03-25 21:43:09"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/473","browser":"Chrome 134.0.0","description":"Registration status updated to Accepted for ID 473","ip_address":"::1","method":"PUT","new_value":"{\"id\":\"473\",\"status\":\"Accepted\",\"reason\":\"Accepted By Super Admin \",\"user_id\":592}","old_value":"{}","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-03-25 21:43:09"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/472","browser":"Chrome 134.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-03-25 21:43:27"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-25 21:43:28"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/472","browser":"Chrome 134.0.0","description":"Welcome email sent to crc role (<EMAIL>)","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":591,\"email\":\"<EMAIL>\",\"subject\":\"Welcome to ResearchHero!\"}","old_value":"null","operation":"EMAIL_SENT","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-03-25 21:43:32"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/472","browser":"Chrome 134.0.0","description":"Registration status updated to Accepted for ID 472","ip_address":"::1","method":"PUT","new_value":"{\"id\":\"472\",\"status\":\"Accepted\",\"reason\":\"Accepted By Super Admin \",\"user_id\":591}","old_value":"{}","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-03-25 21:43:32"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/471","browser":"Chrome 134.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-03-25 21:43:44"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-25 21:43:45"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/471","browser":"Chrome 134.0.0","description":"Welcome email sent to monitor role (<EMAIL>)","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":590,\"email\":\"<EMAIL>\",\"subject\":\"Welcome to ResearchHero!\"}","old_value":"null","operation":"EMAIL_SENT","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-03-25 21:43:49"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/471","browser":"Chrome 134.0.0","description":"Registration status updated to Accepted for ID 471","ip_address":"::1","method":"PUT","new_value":"{\"id\":\"471\",\"status\":\"Accepted\",\"reason\":\"Accepted By Super Admin \",\"user_id\":590}","old_value":"{}","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-03-25 21:43:49"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/470","browser":"Chrome 134.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"update registration status ","user":"<EMAIL>"},"timestamp":"2025-03-25 21:44:05"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-25 21:44:05"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/470","browser":"Chrome 134.0.0","description":"Welcome email sent to PM role (<EMAIL>)","ip_address":"::1","method":"PUT","new_value":"{\"user_id\":589,\"email\":\"<EMAIL>\",\"subject\":\"Welcome to ResearchHero!\"}","old_value":"null","operation":"EMAIL_SENT","table_name":"Registration","user":"<EMAIL>"},"timestamp":"2025-03-25 21:44:10"}
{"level":"info","message":{"api_url":"/registration_approval/updateStatus/470","browser":"Chrome 134.0.0","description":"Registration status updated to Accepted for ID 470","ip_address":"::1","method":"PUT","new_value":"{\"id\":\"470\",\"status\":\"Accepted\",\"reason\":\"Accepted By Super Admin \",\"user_id\":589}","old_value":"{}","operation":"UPDATE","table_name":"RegistrationStatus","user":"<EMAIL>"},"timestamp":"2025-03-25 21:44:10"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Other 0.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-25 21:51:10"}
{"level":"info","message":{"api_url":"/emergency/emergencyEmail","browser":"Other 0.0.0","description":"Emergency email sent and logged successfully","ip_address":"::1","method":"POST","new_value":"{\"subject\":\"Urgent: Immediate Attention Required\",\"date_time\":\"8/1/2024 T 4:14PM\",\"description\":\"Please review and respond to this message as soon as possible due to its high priority.\",\"user_id\":587,\"reportId\":{\"fieldCount\":0,\"affectedRows\":1,\"insertId\":63,\"info\":\"\",\"serverStatus\":2,\"warningStatus\":0,\"changedRows\":0}}","old_value":"null","operation":"SUBMIT","table_name":"Callback Schedule","user":"<EMAIL>"},"timestamp":"2025-03-25 22:03:45"}
{"level":"info","message":{"api_url":"/emergency/emergencyEmail","browser":"Other 0.0.0","description":"Emergency email sent and logged successfully","ip_address":"::1","method":"POST","new_value":"{\"subject\":\"Urgent: Immediate Attention Required\",\"date_time\":\"8/1/2024 T 4:14PM\",\"description\":\"Please review and respond to this message as soon as possible due to its high priority.\",\"user_id\":587,\"reportId\":{\"fieldCount\":0,\"affectedRows\":1,\"insertId\":64,\"info\":\"\",\"serverStatus\":2,\"warningStatus\":0,\"changedRows\":0}}","old_value":"null","operation":"SUBMIT","table_name":"Callback Schedule","user":"<EMAIL>"},"timestamp":"2025-03-25 22:13:36"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-03-25 22:32:55"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Other 0.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-25 22:33:42"}
{"level":"info","message":{"api_url":"/incident_report/createIncidentReportResponse","browser":"Other 0.0.0","description":"Incident report response created:\n          study_id: 1,\n          user_id: 587,\n          question_id: 45,\n          response_text: Texto de respuesta en español.,\n          description: Este es un informe de incidente en español.,\n          incident_severety: Moderate,\n          start_date: 01/10/2025,\n          start_time: 4:54:00 PM,\n          medical_issue: 1,\n          end_date: 2024-12-25,\n          end_time: 15:00","ip_address":"::1","method":"POST","new_value":"{\"study_id\":1,\"user_id\":587,\"question_id\":45,\"response_text\":\"Texto de respuesta en español.\",\"description\":\"Este es un informe de incidente en español.\",\"incident_severety\":\"Moderate\",\"start_date\":\"01/10/2025\",\"start_time\":\"4:54:00 PM\",\"medical_issue\":1,\"end_date\":\"2024-12-25\",\"end_time\":\"15:00\"}","old_value":"null","operation":"SUBMIT","table_name":"Incident Report Response","user":"<EMAIL>"},"timestamp":"2025-03-25 22:34:15"}
{"level":"error","message":"Failed to log to database or Excel: Data too long for column 'description' at row 1","timestamp":"2025-03-25 22:34:16"}
{"level":"info","message":{"api_url":"/incident_report/createIncidentReportResponse","browser":"Other 0.0.0","description":"Incident report response created:\n          study_id: 1,\n          user_id: 587,\n          question_id: 45,\n          response_text: Texto de respuesta en español.,\n          description: Este es un informe de incidente en español.,\n          incident_severety: Moderate,\n          start_date: 01/10/2025,\n          start_time: 4:54:00 PM,\n          medical_issue: 1,\n          end_date: 2024-12-25,\n          end_time: 15:00","ip_address":"::1","method":"POST","new_value":"{\"study_id\":1,\"user_id\":587,\"question_id\":45,\"response_text\":\"Texto de respuesta en español.\",\"description\":\"Este es un informe de incidente en español.\",\"incident_severety\":\"Moderate\",\"start_date\":\"01/10/2025\",\"start_time\":\"4:54:00 PM\",\"medical_issue\":1,\"end_date\":\"2024-12-25\",\"end_time\":\"15:00\"}","old_value":"null","operation":"SUBMIT","table_name":"Incident Report Response","user":"<EMAIL>"},"timestamp":"2025-03-25 22:41:14"}
{"level":"error","message":"Failed to log to database or Excel: Data too long for column 'description' at row 1","timestamp":"2025-03-25 22:41:14"}
{"level":"info","message":{"api_url":"/incident_report/createIncidentReportResponse","browser":"Other 0.0.0","description":"Incident report response created:\n          study_id: 1,\n          user_id: 587,\n          question_id: 45,\n          response_text: Texto de respuesta en español.,\n          description: Este es un informe de incidente en español.,\n          incident_severety: Moderate,\n          start_date: 01/10/2025,\n          start_time: 4:54:00 PM,\n          medical_issue: 1,\n          end_date: 2024-12-25,\n          end_time: 15:00","ip_address":"::1","method":"POST","new_value":"{\"study_id\":1,\"user_id\":587,\"question_id\":45,\"response_text\":\"Texto de respuesta en español.\",\"description\":\"Este es un informe de incidente en español.\",\"incident_severety\":\"Moderate\",\"start_date\":\"01/10/2025\",\"start_time\":\"4:54:00 PM\",\"medical_issue\":1,\"end_date\":\"2024-12-25\",\"end_time\":\"15:00\"}","old_value":"null","operation":"SUBMIT","table_name":"Incident Report Response","user":"<EMAIL>"},"timestamp":"2025-03-25 22:42:23"}
{"level":"error","message":"Failed to log to database or Excel: Data too long for column 'description' at row 1","timestamp":"2025-03-25 22:42:23"}
{"level":"info","message":{"api_url":"/incident_report/createIncidentReportResponse","browser":"Other 0.0.0","description":"Incident report response created:\n          study_id: 1,\n          user_id: 587,\n          question_id: 45,\n          response_text: Texto de respuesta en español.,\n          description: Este es un informe de incidente en español.,\n          incident_severety: Moderate,\n          start_date: 01/10/2025,\n          start_time: 4:54:00 PM,\n          medical_issue: 1,\n          end_date: 2024-12-25,\n          end_time: 15:00","ip_address":"::1","method":"POST","new_value":"{\"study_id\":1,\"user_id\":587,\"question_id\":45,\"response_text\":\"Texto de respuesta en español.\",\"description\":\"Este es un informe de incidente en español.\",\"incident_severety\":\"Moderate\",\"start_date\":\"01/10/2025\",\"start_time\":\"4:54:00 PM\",\"medical_issue\":1,\"end_date\":\"2024-12-25\",\"end_time\":\"15:00\"}","old_value":"null","operation":"SUBMIT","table_name":"Incident Report Response","user":"<EMAIL>"},"timestamp":"2025-03-25 22:43:29"}
{"level":"error","message":"Failed to log to database or Excel: Data too long for column 'description' at row 1","timestamp":"2025-03-25 22:43:29"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 134.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-25 22:49:06"}
{"level":"info","message":{"api_url":"/incident_report/createIncidentReportResponse","browser":"Other 0.0.0","description":"Incident report response created:\n          study_id: 1,\n          user_id: 587,\n          question_id: 45,\n          response_text: Texto de respuesta en español.,\n          description: Este es un informe de incidente en español.,\n          incident_severety: Moderate,\n          start_date: 01/10/2025,\n          start_time: 4:54:00 PM,\n          medical_issue: 1,\n          end_date: 2024-12-25,\n          end_time: 15:00","ip_address":"::1","method":"POST","new_value":"{\"study_id\":1,\"user_id\":587,\"question_id\":45,\"response_text\":\"Texto de respuesta en español.\",\"description\":\"Este es un informe de incidente en español.\",\"incident_severety\":\"Moderate\",\"start_date\":\"01/10/2025\",\"start_time\":\"4:54:00 PM\",\"medical_issue\":1,\"end_date\":\"2024-12-25\",\"end_time\":\"15:00\"}","old_value":"null","operation":"SUBMIT","table_name":"Incident Report Response","user":"<EMAIL>"},"timestamp":"2025-03-25 22:50:47"}
{"level":"error","message":"Failed to log to database or Excel: Data too long for column 'description' at row 1","timestamp":"2025-03-25 22:50:48"}
{"level":"info","message":{"api_url":"/incident_report/createIncidentReportResponse","browser":"Other 0.0.0","description":"Incident report response created:\n          study_id: 1,\n          user_id: 587,\n          question_id: 45,\n          response_text: Texto de respuesta en español.,\n          description: Este es un informe de incidente en español.,\n          incident_severety: Moderate,\n          start_date: 01/10/2025,\n          start_time: 4:54:00 PM,\n          medical_issue: 1,\n          end_date: 2024-12-25,\n          end_time: 15:00","ip_address":"::1","method":"POST","new_value":"{\"study_id\":1,\"user_id\":587,\"question_id\":45,\"response_text\":\"Texto de respuesta en español.\",\"description\":\"Este es un informe de incidente en español.\",\"incident_severety\":\"Moderate\",\"start_date\":\"01/10/2025\",\"start_time\":\"4:54:00 PM\",\"medical_issue\":1,\"end_date\":\"2024-12-25\",\"end_time\":\"15:00\"}","old_value":"null","operation":"SUBMIT","table_name":"Incident Report Response","user":"<EMAIL>"},"timestamp":"2025-03-25 23:04:29"}
{"level":"error","message":"Failed to log to database or Excel: Data too long for column 'description' at row 1","timestamp":"2025-03-25 23:04:29"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 134.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-25 23:58:05"}
{"level":"info","message":"{\"method\":\"POST\",\"api_url\":\"/incident_report/update-ticket-history\",\"table_name\":\"Ticket History\",\"operation\":\"UPDATE\",\"description\":\"Ticket #AE-ZMGM5D3 updated by user_id: 403\",\"old_value\":null,\"new_value\":\"{\\\"user_id\\\":403,\\\"actionType\\\":\\\"Email\\\",\\\"status\\\":\\\"Under Process\\\",\\\"history_text\\\":\\\"this is under process\\\",\\\"ticket_id\\\":\\\"AE-ZMGM5D3\\\"}\",\"browser\":\"Chrome 134.0.0\",\"ip_address\":\"::1\",\"user\":\"<EMAIL>\"}","timestamp":"2025-03-25 23:58:49"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 134.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-26 00:05:52"}
{"level":"info","message":{"api_url":"/organization/update-password","browser":"Chrome 134.0.0","description":"Password updated for user ID: 519","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"UPDATE","table_name":"Password","user":"<EMAIL>"},"timestamp":"2025-03-26 00:07:05"}
{"level":"info","message":{"api_url":"/organization/logout","browser":"Chrome 134.0.0","description":"User logged out successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"LOGOUT","table_name":"N/A","user":"<EMAIL>"},"timestamp":"2025-03-26 00:15:48"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 134.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-26 00:15:52"}
{"level":"info","message":{"api_url":"/organization/logout","browser":"Chrome 134.0.0","description":"User logged out successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"LOGOUT","table_name":"N/A","user":"<EMAIL>"},"timestamp":"2025-03-26 00:16:07"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 134.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-26 00:16:15"}
{"level":"info","message":"{\"method\":\"POST\",\"api_url\":\"/incident_report/update-ticket-history\",\"table_name\":\"Ticket History\",\"operation\":\"UPDATE\",\"description\":\"Ticket #AE-015O3V6 updated by user_id: 403\",\"old_value\":null,\"new_value\":\"{\\\"user_id\\\":403,\\\"actionType\\\":\\\"Submission\\\",\\\"status\\\":\\\"Open\\\",\\\"history_text\\\":\\\"cxv\\\",\\\"ticket_id\\\":\\\"AE-015O3V6\\\"}\",\"browser\":\"Chrome 134.0.0\",\"ip_address\":\"::1\",\"user\":\"<EMAIL>\"}","timestamp":"2025-03-26 00:16:57"}
