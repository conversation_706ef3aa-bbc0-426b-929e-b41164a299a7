{"level":"info","message":{"api_url":"/organization/logout","browser":"Chrome 133.0.0","description":"User logged out successfully","ip_address":"::ffff:127.0.0.1","method":"POST","new_value":"null","old_value":"null","operation":"LOGOUT","table_name":"N/A","user":"<EMAIL>"},"timestamp":"2025-03-06 01:20:44"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 133.0.0","description":"User signed in successfully","ip_address":"::ffff:127.0.0.1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-06 01:21:07"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 133.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-06 20:26:57"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-03-06 20:27:35"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 133.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-06 20:27:48"}
