{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-03 13:27:41"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-03 13:29:54"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-03 13:31:15"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-03 13:38:11"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-03 13:41:40"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-03 13:41:42"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-03 13:44:00"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-03 13:44:01"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-03 13:45:02"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-03 13:45:08"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-03 13:47:31"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-03 14:01:53"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-03 14:02:53"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-03 14:18:09"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-03 14:18:32"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-03 14:18:34"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-03 14:39:26"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-03 14:40:08"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-03 14:40:10"}
{"level":"error","message":"Failed to log to database or Excel: Corrupted zip or bug: expected 16 records in central dir, got 0","timestamp":"2025-02-03 14:40:21"}
{"level":"error","message":"Failed to log to database or Excel: Corrupted zip or bug: expected 16 records in central dir, got 0","timestamp":"2025-02-03 14:40:21"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-03 14:52:49"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-03 15:24:00"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-03 15:24:31"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-03 15:38:50"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-03 15:39:00"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-03 15:39:39"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-03 15:41:17"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-03 15:46:47"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-03 15:48:11"}
{"level":"error","message":"Failed to log to database or Excel: Column 'ip_address' cannot be null","timestamp":"2025-02-03 16:03:02"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-03 16:11:05"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-03 16:13:18"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-03 16:13:20"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-03 16:14:05"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-03 16:14:27"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-03 16:28:37"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-03 17:21:00"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-03 17:21:16"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-03 18:02:44"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-03 18:06:58"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-03 18:55:42"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-03 18:56:11"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-04 08:41:40"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-04 08:41:56"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-04 08:41:57"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-04 08:41:57"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-04 08:41:58"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-04 08:41:58"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-04 08:41:58"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-04 08:41:58"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-04 08:41:59"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-04 08:41:59"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-04 08:41:59"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-04 08:41:59"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-04 08:42:00"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-04 08:57:42"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-04 08:57:43"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-04 08:57:43"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-04 08:57:44"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-04 08:57:44"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-04 08:57:53"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-04 08:58:04"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-04 09:25:13"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-04 09:25:15"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-04 09:25:24"}
