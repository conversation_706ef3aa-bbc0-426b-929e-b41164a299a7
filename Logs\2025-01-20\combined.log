{"level":"info","message":{"api_url":"/organization/logout","browser":"Chrome 131.0.0","description":"User logged out successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"LOGOUT","table_name":"N/A","user":"<EMAIL>"},"timestamp":"2025-01-20 02:08:42"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 131.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-01-20 02:08:47"}
{"level":"info","message":{"api_url":"/medicine/submitMedicineRecord","browser":"Other 0.0.0","description":"No Reason Provided","ip_address":"::1","method":"POST","new_value":"{\"medicine_id\":183,\"intake_quantity\":\"1\",\"user_id\":413,\"study_id\":1,\"date\":\"1/17/2025\",\"time\":\"4:35 PM\"}","old_value":"null","operation":"SUBMIT","table_name":"medicine record","user":"<EMAIL>"},"timestamp":"2025-01-20 02:14:32"}
{"level":"info","message":{"api_url":"/organization/logout","browser":"Chrome 131.0.0","description":"User logged out successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"LOGOUT","table_name":"N/A","user":"<EMAIL>"},"timestamp":"2025-01-20 02:28:52"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 131.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-01-20 02:28:57"}
{"level":"info","message":{"api_url":"/medicine/submitMedicineRecord","browser":"Other 0.0.0","description":"No Reason Provided","ip_address":"::1","method":"POST","new_value":"{\"medicine_id\":183,\"intake_quantity\":\"1\",\"user_id\":430,\"study_id\":1,\"date\":\"1/17/2025\",\"time\":\"4:35 PM\"}","old_value":"null","operation":"SUBMIT","table_name":"medicine record","user":"<EMAIL>"},"timestamp":"2025-01-20 02:31:04"}
{"level":"info","message":{"api_url":"/medicine/submitMedicineRecord","browser":"Other 0.0.0","description":"No Reason Provided","ip_address":"::1","method":"POST","new_value":"{\"medicine_id\":200,\"intake_quantity\":\"1\",\"user_id\":430,\"study_id\":1,\"date\":\"1/17/2025\",\"time\":\"4:35 PM\"}","old_value":"null","operation":"SUBMIT","table_name":"medicine record","user":"<EMAIL>"},"timestamp":"2025-01-20 02:33:27"}
{"level":"info","message":{"api_url":"/medicine/submitMedicineRecord","browser":"Other 0.0.0","description":"No Reason Provided","ip_address":"::1","method":"POST","new_value":"{\"medicine_id\":200,\"intake_quantity\":\"1\",\"user_id\":430,\"study_id\":1,\"date\":\"1/17/2025\",\"time\":\"4:35 PM\"}","old_value":"null","operation":"SUBMIT","table_name":"medicine record","user":"<EMAIL>"},"timestamp":"2025-01-20 02:33:36"}
{"level":"info","message":{"api_url":"/medicine/submitMedicineRecord","browser":"Other 0.0.0","description":"No Reason Provided","ip_address":"::1","method":"POST","new_value":"{\"medicine_id\":200,\"intake_quantity\":\"1\",\"user_id\":430,\"study_id\":1,\"date\":\"1-17-2025\",\"time\":\"4:35 PM\"}","old_value":"null","operation":"SUBMIT","table_name":"medicine record","user":"<EMAIL>"},"timestamp":"2025-01-20 02:34:23"}
{"level":"info","message":{"api_url":"/medicine/submitMedicineRecord","browser":"Other 0.0.0","description":"No Reason Provided","ip_address":"::1","method":"POST","new_value":"{\"medicine_id\":200,\"intake_quantity\":\"1\",\"user_id\":430,\"study_id\":1,\"date\":\"2025-01-16\",\"time\":\"4:35 PM\"}","old_value":"null","operation":"SUBMIT","table_name":"medicine record","user":"<EMAIL>"},"timestamp":"2025-01-20 02:53:01"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 131.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-01-20 21:32:02"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 131.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-01-20 21:48:36"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 131.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-01-20 22:18:24"}
