{"level":"info","message":{"api_url":"/organization/logout","browser":"Chrome 134.0.0","description":"User logged out successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"LOGOUT","table_name":"N/A","user":"<EMAIL>"},"timestamp":"2025-03-14 00:17:45"}
{"level":"info","message":{"api_url":"/organization/logout","browser":"Chrome 134.0.0","description":"User logged out successfully","ip_address":"::ffff:127.0.0.1","method":"POST","new_value":"null","old_value":"null","operation":"LOGOUT","table_name":"N/A","user":"<EMAIL>"},"timestamp":"2025-03-14 00:17:46"}
{"level":"info","message":{"api_url":"/organization/logout","browser":"Chrome 134.0.0","description":"User logged out successfully","ip_address":"::ffff:127.0.0.1","method":"POST","new_value":"null","old_value":"null","operation":"LOGOUT","table_name":"N/A","user":"<EMAIL>"},"timestamp":"2025-03-14 00:17:47"}
{"level":"error","message":"Failed to log to database or Excel: Corrupted zip or bug: expected 16 records in central dir, got 0","timestamp":"2025-03-14 00:17:49"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-03-14 00:18:02"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-03-14 00:18:22"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-03-14 00:18:35"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 134.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-14 00:18:47"}
{"level":"info","message":{"api_url":"/organization/logout","browser":"Chrome 134.0.0","description":"User logged out successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"LOGOUT","table_name":"N/A","user":"<EMAIL>"},"timestamp":"2025-03-14 00:22:16"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 134.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-14 00:22:25"}
{"level":"info","message":{"api_url":"/organization/logout","browser":"Chrome 134.0.0","description":"User logged out successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"LOGOUT","table_name":"N/A","user":"<EMAIL>"},"timestamp":"2025-03-14 00:23:37"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 134.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-14 00:23:53"}
{"level":"info","message":{"api_url":"/organization/logout","browser":"Chrome 134.0.0","description":"User logged out successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"LOGOUT","table_name":"N/A","user":"<EMAIL>"},"timestamp":"2025-03-14 00:24:12"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 134.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-14 00:24:19"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 134.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-15 00:04:33"}
{"level":"info","message":{"api_url":"/organization/logout","browser":"Chrome 134.0.0","description":"User logged out successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"LOGOUT","table_name":"N/A","user":"<EMAIL>"},"timestamp":"2025-03-15 00:05:27"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 134.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-15 00:06:04"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 134.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-15 00:06:05"}
