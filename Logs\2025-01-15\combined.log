{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 131.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-01-15 01:57:36"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 131.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-01-15 21:29:09"}
{"level":"error","message":"Failed to decode token or email is missing","timestamp":"2025-01-15 21:30:08"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-01-15 21:30:11"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 131.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-01-15 21:30:25"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/398","browser":"Chrome 131.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-01-15 21:32:28"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-15 21:32:29"}
{"level":"error","message":"Failed to log to database: Cannot read properties of undefined (reading 'headers')","timestamp":"2025-01-15 21:32:31"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/398","browser":"Chrome 131.0.0","description":"Auto Create Medicine","ip_address":"::1","method":"PUT","new_value":"{\"medication_name\":\"Sunobinop Or Placebo\",\"dosage\":\"0.5mg or 1.0mg or 2.0mg\",\"dosage_times\":[\"09:00 PM\"],\"frequencyType\":\"QD\",\"frequencyTime\":\"N/A\",\"frequencyCondition\":\"At Bedtime\",\"dosageType\":\"Tablet\",\"allot_medicine\":\"1\",\"route\":\"Oral\",\"note\":\"Auto-created medicine\",\"user_id\":\"429\",\"investigator_id\":\"403\",\"tracker_time\":\"2025-01-16T03:32:30.233Z\"}","old_value":"null","operation":"CREATE","table_name":"Auto Create Medicine Due to Subject Status Update","user":"<EMAIL>"},"timestamp":"2025-01-15 21:32:33"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/398","browser":"Chrome 131.0.0","description":"No Reason Provided","ip_address":"::1","method":"PUT","new_value":"{\"first_name\":\"some test\",\"middle_name\":\"\",\"last_name\":\"tlfb\",\"status\":\"Randomized\",\"gender\":\"male\",\"address\":\"some address\",\"contact_number\":\"11232132132\",\"stipend\":\"0\",\"study_enrolled_ids\":\"1\",\"notification\":\"some notification\",\"note\":\"Note for admin\",\"ecrf_id\":\"AAAA-BBB\"}","old_value":"{\"organization_id\":398,\"first_name\":\"some test\",\"middle_name\":null,\"last_name\":\"tlfb\",\"status\":\"Screened\",\"is_randomized\":0,\"is_compliant\":1,\"gender\":\"male\",\"address\":\"some address\",\"timezone\":\"UTC\",\"contact_number\":\"11232132132\",\"date_of_birth\":\"03/01/1990\",\"stipend\":\"0\",\"image\":null,\"study_enrolled_id\":\"1\",\"date_enrolled\":\"12/22/2024\",\"notification\":\"some notification\",\"user_id\":429,\"organization_detail_id\":1,\"role_id\":10,\"ecrf_id\":\"AAAA-BBB\",\"study_enrolled\":[{\"id\":1,\"name\":\"\"}]}","operation":"UPDATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-01-15 21:32:34"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1450","browser":"Chrome 131.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-01-15 21:33:31"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-15 21:33:32"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1450","browser":"Chrome 131.0.0","description":"Auto Create Medicine","ip_address":"::1","method":"PUT","new_value":"{\"medication_name\":\"Sunobinop Or Placebo\",\"dosage\":\"0.5mg or 1.0mg or 2.0mg\",\"dosage_times\":[\"09:00 PM\"],\"frequencyType\":\"QD\",\"frequencyTime\":\"N/A\",\"frequencyCondition\":\"At Bedtime\",\"dosageType\":\"Tablet\",\"allot_medicine\":\"1\",\"route\":\"Oral\",\"note\":\"Auto-created medicine\",\"user_id\":429,\"investigator_id\":0,\"tracker_time\":\"2025-01-16T03:33:33.529Z\"}","old_value":"null","operation":"CREATE","table_name":"Auto Create Medicine Due to Subject Screening Complete","user":"<EMAIL>"},"timestamp":"2025-01-15 21:33:33"}
{"level":"error","message":"Failed to log to database: Cannot read properties of undefined (reading 'headers')","timestamp":"2025-01-15 21:33:34"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1450","browser":"Chrome 131.0.0","description":"Schedule completed with ID: 1450","ip_address":"::1","method":"PUT","new_value":"{\"schedule_date\":\"2025-01-08\",\"schedule_time\":\"09:00\",\"status\":\"Completed\",\"note\":\"Auto-created schedule\",\"user_id\":429}","old_value":"{\"schedule_date\":\"2025-01-08T06:00:00.000Z\",\"schedule_time\":\"09:00\",\"status\":\"Scheduled\",\"note\":\"Auto-created schedule\",\"user_id\":429}","operation":"UPDATE","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-01-15 21:33:39"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1450","browser":"Chrome 131.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-01-15 21:46:50"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-15 21:46:50"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1450","browser":"Chrome 131.0.0","description":"Schedule completed with ID: 1450","ip_address":"::1","method":"PUT","new_value":"{\"schedule_date\":\"2025-01-08\",\"schedule_time\":\"09:00\",\"status\":\"Completed\",\"note\":\"Auto-created schedule\",\"user_id\":429}","old_value":"{\"schedule_date\":\"2025-01-08T06:00:00.000Z\",\"schedule_time\":\"09:00\",\"status\":\"Completed\",\"note\":\"Auto-created schedule\",\"user_id\":429}","operation":"UPDATE","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-01-15 21:46:55"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/355","browser":"Chrome 131.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-01-15 21:49:43"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-15 21:49:44"}
{"level":"error","message":"Failed to log to database: Cannot read properties of undefined (reading 'headers')","timestamp":"2025-01-15 21:49:45"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/355","browser":"Chrome 131.0.0","description":"Auto Create Medicine","ip_address":"::1","method":"PUT","new_value":"{\"medication_name\":\"Sunobinop Or Placebo\",\"dosage\":\"0.5mg or 1.0mg or 2.0mg\",\"dosage_times\":[\"09:00 PM\"],\"frequencyType\":\"QD\",\"frequencyTime\":\"N/A\",\"frequencyCondition\":\"At Bedtime\",\"dosageType\":\"Tablet\",\"allot_medicine\":\"1\",\"route\":\"Oral\",\"note\":\"Auto-created medicine\",\"user_id\":\"386\",\"investigator_id\":\"403\",\"tracker_time\":\"2025-01-16T03:49:45.137Z\"}","old_value":"null","operation":"CREATE","table_name":"Auto Create Medicine Due to Subject Status Update","user":"<EMAIL>"},"timestamp":"2025-01-15 21:49:48"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/355","browser":"Chrome 131.0.0","description":"No Reason Provided","ip_address":"::1","method":"PUT","new_value":"{\"first_name\":\"decembertest\",\"middle_name\":\"\",\"last_name\":\"subject\",\"status\":\"Randomized\",\"gender\":\"male\",\"address\":\"some address\",\"contact_number\":\"11231255151\",\"stipend\":\"0\",\"study_enrolled_ids\":\"1\",\"notification\":\"some notification\",\"note\":\"Note for admin\",\"ecrf_id\":\"A101-209\"}","old_value":"{\"organization_id\":355,\"first_name\":\"decembertest\",\"middle_name\":null,\"last_name\":\"subject\",\"status\":\"Screened\",\"is_randomized\":0,\"is_compliant\":1,\"gender\":\"male\",\"address\":\"some address\",\"timezone\":\"UTC\",\"contact_number\":\"11231255151\",\"date_of_birth\":\"05/02/2000\",\"stipend\":\"0\",\"image\":null,\"study_enrolled_id\":\"1\",\"date_enrolled\":\"12/03/2024\",\"notification\":\"some notification\",\"user_id\":386,\"organization_detail_id\":1,\"role_id\":10,\"ecrf_id\":\"A101-209\",\"study_enrolled\":[{\"id\":1,\"name\":\"\"}]}","operation":"UPDATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-01-15 21:49:48"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1279","browser":"Chrome 131.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-01-15 21:50:55"}
{"level":"error","message":"Failed to log to database: Column 'description' cannot be null","timestamp":"2025-01-15 21:50:55"}
{"level":"info","message":{"api_url":"/schedule/updateSchedule/1279","browser":"Chrome 131.0.0","description":"Schedule completed with ID: 1279","ip_address":"::1","method":"PUT","new_value":"{\"schedule_date\":\"2024-12-01\",\"schedule_time\":\"09:00\",\"status\":\"Completed\",\"note\":\"Auto-created schedule\",\"user_id\":386}","old_value":"{\"schedule_date\":\"2024-12-01T06:00:00.000Z\",\"schedule_time\":\"09:00\",\"status\":\"Scheduled\",\"note\":\"Auto-created schedule\",\"user_id\":386}","operation":"UPDATE","table_name":"schedule","user":"<EMAIL>"},"timestamp":"2025-01-15 21:51:00"}
