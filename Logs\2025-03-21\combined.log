{"level":"info","message":{"api_url":"/organization/updateOrganization/415","browser":"Chrome 134.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-21 01:37:32"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-21 01:37:32"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/415","browser":"Chrome 134.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-21 01:38:35"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-21 01:38:35"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/415","browser":"Chrome 134.0.0","description":"ssssssssssssssssssssss","ip_address":"::1","method":"PUT","new_value":"{\"first_name\":\"topa\",\"last_name\":\"rightssss\",\"status\":\"Withdrew Consent\",\"gender\":\"male\",\"address\":\"5959 Bonhomme Rd\",\"contact_number\":\"12377292048\",\"stipend\":\"3\",\"notification\":\"some notification\",\"ecrf_id\":\"A565-556\",\"reason\":\"ssssssssssssssssssssss\"}","old_value":"{\"first_name\":\"\",\"last_name\":\"Withdrew Consent\",\"status\":\"ed6d4a0dd588ecd2f4bc32e4ca75afb8:8b9a1e768941b0df9a7c8b0939e1d6d0\",\"gender\":\"5959 Bonhomme Rd\",\"address\":\"e5f6c601f3ed9c62cce11d6aefaf8ba9:01c6c126eeb550aec1ed2b31f3bcea19\",\"contact_number\":\"02/03/1998\",\"stipend\":\"1\",\"notification\":\"Note for admin\",\"ecrf_id\":null}","operation":"UPDATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-21 01:38:37"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/504","browser":"Chrome 134.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-21 01:39:53"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-21 01:39:54"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/504","browser":"Chrome 134.0.0","description":"adssssssssssss","ip_address":"::1","method":"PUT","new_value":"{\"first_name\":\"Asad\",\"middle_name\":\"\",\"status\":\"Screened\",\"reason\":\"adssssssssssss\"}","old_value":"{\"first_name\":\"ASasdasd\",\"middle_name\":null,\"status\":\"Screen Failed\"}","operation":"UPDATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-21 01:39:55"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/504","browser":"Chrome 134.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-21 01:44:40"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-21 01:44:40"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/504","browser":"Chrome 134.0.0","description":"sssssssssssssssss","ip_address":"::1","method":"PUT","new_value":"{\"first_name\":\"Asads\",\"middle_name\":\"\",\"status\":\"Screen Failed\",\"reason\":\"sssssssssssssssss\"}","old_value":"{\"first_name\":\"Asad\",\"middle_name\":null,\"status\":\"Screened\"}","operation":"UPDATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-21 01:44:42"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/504","browser":"Chrome 134.0.0","ip_address":"::1","method":"PUT","new_value":"null","old_value":"null","operation":"update","table_name":"organization","user":"<EMAIL>"},"timestamp":"2025-03-21 01:46:44"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-21 01:46:45"}
{"level":"info","message":{"api_url":"/organization/updateOrganization/504","browser":"Chrome 134.0.0","description":"hhhhhhhhhhhhhhhh","ip_address":"::1","method":"PUT","new_value":"{\"first_name\":\"Asad\",\"status\":\"Lost to Follow up\",\"reason\":\"hhhhhhhhhhhhhhhh\"}","old_value":"{\"first_name\":\"Asads\",\"status\":\"Screen Failed\"}","operation":"UPDATE","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-21 01:46:46"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/218","browser":"Chrome 134.0.0","description":"Medicine Updated Successfully","ip_address":"::1","method":"PUT","new_value":"{\"medication_name\":\"Sunobinop Or Placebos\",\"dosage_times\":[\"09:00 PM\"],\"frequencyType\":\"QD\",\"frequencyTime\":\"N/A\",\"frequencyCondition\":\"Before Meal\",\"tracker_time\":\"2025-03-20T20:47:26.272Z\",\"reason\":\"sssssssssssssss\"}","old_value":"{\"medication_name\":\"Sunobinop Or Placebo\"}","operation":"UPDATE","table_name":"Medication","user":"<EMAIL>"},"timestamp":"2025-03-21 01:47:31"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/217","browser":"Chrome 134.0.0","description":"Medicine Updated Successfully","ip_address":"::1","method":"PUT","new_value":"{\"medication_name\":\"Sunobinop Or Placebob\",\"dosage_times\":[\"09:00 PM\"],\"frequencyType\":\"QD\",\"frequencyTime\":\"N/A\",\"frequencyCondition\":\"Before Meal\",\"tracker_time\":\"2025-03-20T20:51:27.695Z\",\"reason\":\"bbbbbbbbbbbbbb\"}","old_value":"{\"medication_name\":\"Sunobinop Or Placebo\"}","operation":"UPDATE","table_name":"Medication","user":"<EMAIL>"},"timestamp":"2025-03-21 01:51:32"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/205","browser":"Chrome 134.0.0","description":"Medicine Updated Successfully","ip_address":"::1","method":"PUT","new_value":"{\"medication_name\":\"Sunobinop or Placebosz\",\"dosage_times\":[\"08:24 AM\"],\"frequencyType\":\"QD\",\"frequencyTime\":\"Morning\",\"frequencyCondition\":\"Anytime\",\"tracker_time\":\"2025-03-20T20:54:30.807Z\",\"reason\":\"zzzzzzzzzzzzzzzzzzzzzzzzzzzz\"}","old_value":"{\"medication_name\":\"Sunobinop or Placebos\"}","operation":"UPDATE","table_name":"Medication","user":"<EMAIL>"},"timestamp":"2025-03-21 01:54:35"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/215","browser":"Chrome 134.0.0","description":"Medicine Updated Successfully","ip_address":"::1","method":"PUT","new_value":"{\"medication_name\":\"Sunobinop Or Placeboh\",\"dosage_times\":[\"09:00 PM\"],\"frequencyType\":\"QD\",\"frequencyCondition\":\"Anytime\",\"tracker_time\":\"2025-03-20T20:55:50.746Z\",\"reason\":\"hhhhhhhhhhhhhh\"}","old_value":"{\"medication_name\":\"Sunobinop Or Placebo\"}","operation":"UPDATE","table_name":"Medication","user":"<EMAIL>"},"timestamp":"2025-03-21 01:55:55"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/215","browser":"Chrome 134.0.0","description":"Medicine Updated Successfully","ip_address":"::ffff:127.0.0.1","method":"PUT","new_value":"{\"medication_name\":\"Sunobinop Or Placebohx\",\"dosage_times\":[\"09:00 PM\"],\"frequencyType\":\"QD\",\"frequencyTime\":\"N/A\",\"frequencyCondition\":\"After Meal\",\"tracker_time\":\"2025-03-20T20:59:11.647Z\",\"reason\":\"x ccccccccccccccc\"}","old_value":"{\"medication_name\":\"Sunobinop Or Placeboh\"}","operation":"UPDATE","table_name":"Medication","user":"<EMAIL>"},"timestamp":"2025-03-21 01:59:16"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/217","browser":"Chrome 134.0.0","description":"Medicine Updated Successfully","ip_address":"::ffff:127.0.0.1","method":"PUT","new_value":"{\"medication_name\":\"Sunobinop Or Placebobd\",\"dosage_times\":[\"09:00 PM\"],\"frequencyType\":\"QD\",\"frequencyTime\":\"N/A\",\"frequencyCondition\":\"Anytime\",\"tracker_time\":\"2025-03-20T21:01:19.302Z\",\"reason\":\"sxxxxxxxxxdsd\"}","old_value":"{\"medication_name\":\"Sunobinop Or Placebob\"}","operation":"UPDATE","table_name":"Medication","user":"<EMAIL>"},"timestamp":"2025-03-21 02:01:24"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/217","browser":"Chrome 134.0.0","description":"Error updating medication: Invalid time value","ip_address":"::1","method":"PUT","new_value":"null","old_value":"{\"medication_id\":\"217\"}","operation":"UPDATE_ERROR","table_name":"Medication","user":"<EMAIL>"},"timestamp":"2025-03-21 02:04:20"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/216","browser":"Chrome 134.0.0","description":"Medicine Updated Successfully","ip_address":"::1","method":"PUT","new_value":"{\"medication_name\":\"Sunobinop Or Placebos\",\"dosage_times\":[\"09:00 PM\"],\"frequencyType\":\"QD\",\"frequencyTime\":\"N/A\",\"frequencyCondition\":\"At Bedtime\",\"route\":\"Parenteral\",\"reason\":\"Zssssssssssssssssss\"}","old_value":"{\"medication_name\":\"Sunobinop Or Placebo\",\"route\":\"Oral\"}","operation":"UPDATE","table_name":"Medication","user":"<EMAIL>"},"timestamp":"2025-03-21 02:06:09"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/217","browser":"Chrome 134.0.0","description":"Medicine Updated Successfully","ip_address":"::1","method":"PUT","new_value":"{\"medication_name\":\"Sunobinop Or Placebo\",\"frequencyType\":\"QD\",\"tracker_time\":\"2025-03-20T21:07:53.615Z\",\"reason\":\",NNNNNNNNNNNNNNN\"}","old_value":"{\"medication_name\":\"Sunobinop Or Placebobdss\"}","operation":"UPDATE","table_name":"Medication","user":"<EMAIL>"},"timestamp":"2025-03-21 02:07:58"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/215","browser":"Chrome 134.0.0","description":"Medicine Updated Successfully","ip_address":"::1","method":"PUT","new_value":"{\"medication_name\":\"Sunobinop Or Placebo\",\"frequencyType\":\"QD\",\"frequencyCondition\":\"At Bedtime\",\"reason\":\"sdssssssssss\"}","old_value":"{\"medication_name\":\"Sunobinop Or Placebohx\"}","operation":"UPDATE","table_name":"Medication","user":"<EMAIL>"},"timestamp":"2025-03-21 02:09:24"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/205","browser":"Chrome 134.0.0","description":"Medicine Updated Successfully","ip_address":"::1","method":"PUT","new_value":"{\"medication_name\":\"Sunobinop or Placebo\",\"dosage_times\":[\"08:24 AM\"],\"frequencyCondition\":\"Before Meal\",\"route\":\"Rectal\",\"tracker_time\":\"2025-03-20T21:12:26.066Z\",\"reason\":\"hhhhhhhhhhhhhh\"}","old_value":"{\"medication_name\":\"Sunobinop or Placebosz\",\"dosage_times\":\"08:24 AM\",\"frequencyCondition\":\"Anytime\",\"route\":\"Oral\"}","operation":"UPDATE","table_name":"Medication","user":"<EMAIL>"},"timestamp":"2025-03-21 02:12:31"}
{"level":"info","message":{"api_url":"/medicine/updateMedication/215","browser":"Chrome 134.0.0","description":"Medicine Updated Successfully","ip_address":"::1","method":"PUT","new_value":"{\"medication_name\":\"Sunobinop Or Placebos\",\"frequencyCondition\":\"Before Meal\",\"reason\":\"zzzzzzzzzzz\"}","old_value":"{\"medication_name\":\"Sunobinop Or Placebo\",\"frequencyCondition\":\"At Bedtime\"}","operation":"UPDATE","table_name":"Medication","user":"<EMAIL>"},"timestamp":"2025-03-21 02:13:48"}
{"level":"info","message":{"api_url":"/organization/logout","browser":"Chrome 134.0.0","description":"User logged out successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"LOGOUT","table_name":"N/A","user":"<EMAIL>"},"timestamp":"2025-03-21 02:22:56"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-03-21 02:23:03"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-03-21 02:23:03"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-03-21 02:23:08"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-03-21 02:23:12"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-03-21 02:23:25"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-03-21 02:23:28"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-03-21 02:23:35"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-03-21 02:27:37"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-03-21 02:27:41"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-03-21 02:27:48"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-03-21 02:34:52"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-03-21 02:38:11"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-03-21 02:39:39"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-03-21 02:42:04"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-03-21 02:42:57"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 134.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-21 02:47:26"}
{"level":"info","message":{"api_url":"/organization/logout","browser":"Chrome 134.0.0","description":"User logged out successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"LOGOUT","table_name":"N/A","user":"<EMAIL>"},"timestamp":"2025-03-21 02:47:43"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-03-21 02:47:51"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-03-21 02:48:42"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-03-21 02:48:47"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-03-21 02:48:52"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 134.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-21 02:49:45"}
{"level":"info","message":{"api_url":"/organization/logout","browser":"Chrome 134.0.0","description":"User logged out successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"LOGOUT","table_name":"N/A","user":"<EMAIL>"},"timestamp":"2025-03-21 02:50:19"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-03-21 02:50:27"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 134.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-21 02:50:34"}
{"level":"info","message":{"api_url":"/organization/logout","browser":"Chrome 134.0.0","description":"User logged out successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"LOGOUT","table_name":"N/A","user":"<EMAIL>"},"timestamp":"2025-03-21 02:50:59"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-03-21 02:51:07"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-03-21 02:51:09"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 134.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-21 02:51:16"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 134.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-21 02:52:33"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-03-21 02:52:40"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 134.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-03-21 02:52:50"}
{"level":"info","message":{"api_url":"/organization/logout","browser":"Chrome 134.0.0","description":"User logged out successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"LOGOUT","table_name":"N/A","user":"<EMAIL>"},"timestamp":"2025-03-21 02:54:00"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-03-21 02:54:52"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-03-21 02:55:39"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-03-21 02:56:21"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-03-21 02:56:45"}
{"level":"info","message":"{\"method\":\"POST\",\"api_url\":\"/incident_report/update-ticket-history\",\"table_name\":\"Ticket History\",\"operation\":\"UPDATE\",\"description\":\"Ticket #AE-LL9MZJD updated by user_id: 403\",\"old_value\":null,\"new_value\":\"{\\\"user_id\\\":403,\\\"actionType\\\":\\\"Comment\\\",\\\"status\\\":\\\"Under Process\\\",\\\"history_text\\\":\\\"ZX\\\",\\\"ticket_id\\\":\\\"AE-LL9MZJD\\\"}\",\"browser\":\"Chrome 134.0.0\",\"ip_address\":\"::1\",\"user\":\"<EMAIL>\"}","timestamp":"2025-03-21 23:24:27"}
{"level":"error","message":"Failed to decode token or email is missing","timestamp":"2025-03-21 23:25:53"}
{"level":"error","message":"Failed to decode token or email is missing","timestamp":"2025-03-21 23:27:32"}
{"level":"error","message":"Failed to decode token or email is missing","timestamp":"2025-03-21 23:29:24"}
{"level":"error","message":"Failed to decode token or email is missing","timestamp":"2025-03-21 23:31:04"}
{"level":"error","message":"Failed to decode token or email is missing","timestamp":"2025-03-21 23:32:30"}
{"level":"error","message":"Failed to decode token or email is missing","timestamp":"2025-03-21 23:33:45"}
{"level":"error","message":"Failed to decode token or email is missing","timestamp":"2025-03-21 23:36:57"}
{"level":"error","message":"Failed to decode token or email is missing","timestamp":"2025-03-21 23:38:02"}
{"level":"error","message":"Failed to decode token or email is missing","timestamp":"2025-03-21 23:39:30"}
{"level":"info","message":"{\"method\":\"POST\",\"api_url\":\"/incident_report/update-ticket-history\",\"table_name\":\"Ticket History\",\"operation\":\"UPDATE\",\"description\":\"Ticket #AE-LL9MZJD updated by user_id: 403\",\"old_value\":null,\"new_value\":\"{\\\"user_id\\\":403,\\\"actionType\\\":\\\"Submission\\\",\\\"status\\\":\\\"Closed\\\",\\\"history_text\\\":\\\"xzc\\\",\\\"ticket_id\\\":\\\"AE-LL9MZJD\\\"}\",\"browser\":\"Chrome 134.0.0\",\"ip_address\":\"::ffff:127.0.0.1\",\"user\":\"<EMAIL>\"}","timestamp":"2025-03-21 23:43:47"}
{"level":"error","message":"Failed to decode token or email is missing","timestamp":"2025-03-21 23:48:15"}
{"level":"info","message":"{\"method\":\"POST\",\"api_url\":\"/app_survey/submitscalequestionresponse\",\"table_name\":\"Scale\",\"operation\":\"SUBMIT\",\"description\":\"Scale responses submitted successfully\",\"old_value\":\"{\\\"userId\\\":\\\"412\\\",\\\"scheduleId\\\":1,\\\"dayId\\\":12,\\\"scaleId\\\":27}\",\"new_value\":\"\\\"Survey responses submitted, PDF sent, and Excel file generated successfully\\\"\",\"browser\":\"Chrome 134.0.0\",\"ip_address\":\"::1\",\"user\":\"<EMAIL>\"}","timestamp":"2025-03-21 23:51:12"}
{"level":"info","message":"{\"method\":\"POST\",\"api_url\":\"/app_survey/submitscalequestionresponse\",\"table_name\":\"Scale\",\"operation\":\"SUBMIT\",\"description\":\"Scale responses submitted successfully\",\"old_value\":\"{\\\"userId\\\":\\\"412\\\",\\\"scheduleId\\\":1,\\\"dayId\\\":12,\\\"scaleId\\\":7,\\\"surveyResponses\\\":[{\\\"questionId\\\":34,\\\"option_id\\\":144,\\\"score\\\":\\\"4.00\\\"},{\\\"questionId\\\":35,\\\"option_id\\\":152,\\\"score\\\":\\\"5.00\\\"},{\\\"questionId\\\":36,\\\"option_id\\\":160,\\\"score\\\":\\\"6.00\\\"},{\\\"questionId\\\":37,\\\"option_id\\\":167,\\\"score\\\":\\\"6.00\\\"},{\\\"questionId\\\":38,\\\"option_id\\\":172,\\\"score\\\":\\\"4.00\\\"},{\\\"questionId\\\":39,\\\"option_id\\\":181,\\\"score\\\":\\\"6.00\\\"},{\\\"questionId\\\":40,\\\"option_id\\\":186,\\\"score\\\":\\\"4.00\\\"},{\\\"questionId\\\":41,\\\"option_id\\\":189,\\\"score\\\":\\\"0.00\\\"},{\\\"questionId\\\":42,\\\"option_id\\\":200,\\\"score\\\":\\\"4.00\\\"},{\\\"questionId\\\":43,\\\"option_id\\\":207,\\\"score\\\":\\\"4.00\\\"}],\\\"scale_start_time\\\":\\\"2025-03-21T18:55:25.403Z\\\",\\\"scale_end_time\\\":\\\"2025-03-21T18:55:45.321Z\\\"}\",\"new_value\":null,\"browser\":\"Chrome 134.0.0\",\"ip_address\":\"::1\",\"user\":\"<EMAIL>\"}","timestamp":"2025-03-21 23:55:57"}
