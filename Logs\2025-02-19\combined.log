{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-19 12:09:17"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-02-19 12:09:25"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 133.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-02-19 12:09:39"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 133.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-02-19 12:16:40"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 133.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-02-19 12:21:16"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 133.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-02-19 12:30:38"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 133.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-02-19 12:40:08"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 133.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-02-19 12:45:44"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 133.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-02-19 14:50:29"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 133.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-02-19 14:52:26"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 133.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-02-19 15:18:37"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 133.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-02-19 15:18:56"}
{"level":"info","message":{"api_url":"/app_survey/submitscalequestionresponse","browser":"Chrome 133.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"submit survey response","user":"<EMAIL>"},"timestamp":"2025-02-19 15:21:26"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-19 15:21:27"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 133.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-02-19 15:28:08"}
{"level":"info","message":{"api_url":"/app_survey/submitscalequestionresponse","browser":"Chrome 133.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"submit survey response","user":"<EMAIL>"},"timestamp":"2025-02-19 15:30:39"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-19 15:30:40"}
{"level":"info","message":{"api_url":"/app_survey/submitscalequestionresponse","browser":"Chrome 133.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"submit survey response","user":"<EMAIL>"},"timestamp":"2025-02-19 15:32:12"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-19 15:32:12"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 133.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-02-19 15:36:12"}
{"level":"info","message":{"api_url":"/app_survey/submitscalequestionresponse","browser":"Chrome 133.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"submit survey response","user":"<EMAIL>"},"timestamp":"2025-02-19 15:37:59"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-19 15:38:00"}
{"level":"info","message":{"api_url":"/app_survey/submitscalequestionresponse","browser":"Chrome 133.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"submit survey response","user":"<EMAIL>"},"timestamp":"2025-02-19 15:39:09"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-19 15:39:10"}
{"level":"info","message":{"api_url":"/app_survey/submitscalequestionresponse","browser":"Chrome 133.0.0","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"create","table_name":"submit survey response","user":"<EMAIL>"},"timestamp":"2025-02-19 15:39:53"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-02-19 15:39:53"}
{"level":"info","message":{"api_url":"/auth/signin","browser":"Chrome 133.0.0","description":"User signed in successfully","ip_address":"::1","method":"POST","new_value":"null","old_value":"null","operation":"SIGNIN","table_name":"Organization","user":"<EMAIL>"},"timestamp":"2025-02-20 12:29:01"}
