{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-25 21:35:15"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-25 21:36:02"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-25 21:37:07"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-25 21:37:51"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-25 21:37:56"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-25 21:38:38"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-25 21:39:40"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-25 21:40:25"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-25 21:41:06"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-25 21:42:02"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-25 21:42:16"}
{"level":"error","message":"Failed to log to database or Excel: Corrupted zip or bug: expected 16 records in central dir, got 0","timestamp":"2025-03-25 21:42:21"}
{"level":"error","message":"Failed to log to database or Excel: Corrupted zip or bug: expected 16 records in central dir, got 0","timestamp":"2025-03-25 21:42:22"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-25 21:42:32"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-25 21:42:49"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-25 21:43:05"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-25 21:43:28"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-25 21:43:45"}
{"level":"error","message":"Failed to log to database or Excel: Column 'description' cannot be null","timestamp":"2025-03-25 21:44:05"}
{"level":"error","message":"Authorization token is missing","timestamp":"2025-03-25 22:32:55"}
{"level":"error","message":"Failed to log to database or Excel: Data too long for column 'description' at row 1","timestamp":"2025-03-25 22:34:16"}
{"level":"error","message":"Failed to log to database or Excel: Data too long for column 'description' at row 1","timestamp":"2025-03-25 22:41:14"}
{"level":"error","message":"Failed to log to database or Excel: Data too long for column 'description' at row 1","timestamp":"2025-03-25 22:42:23"}
{"level":"error","message":"Failed to log to database or Excel: Data too long for column 'description' at row 1","timestamp":"2025-03-25 22:43:29"}
{"level":"error","message":"Failed to log to database or Excel: Data too long for column 'description' at row 1","timestamp":"2025-03-25 22:50:48"}
{"level":"error","message":"Failed to log to database or Excel: Data too long for column 'description' at row 1","timestamp":"2025-03-25 23:04:29"}
